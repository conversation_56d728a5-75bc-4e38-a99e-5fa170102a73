<?php
include('admin/config/dbcon.php');

// Test category filter
$category_filter = isset($_GET['category']) ? $_GET['category'] : '0';
echo "<h2>Testing Category Filter: " . htmlspecialchars($category_filter) . "</h2>";

// Build the query
$sql = "SELECT p.id, p.name, p.category_id, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 0";

// Add category filter if selected
if ($category_filter !== '0') {
    $sql .= " AND (p.category_id = '$category_filter')";
}

$sql .= " ORDER BY p.name ASC";

// Execute the query
$result = mysqli_query($conn, $sql);

// Display the results
if ($result && mysqli_num_rows($result) > 0) {
    echo "<p>Found " . mysqli_num_rows($result) . " products</p>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Category ID</th><th>Category Name</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['category_id'] . "</td>";
        echo "<td>" . $row['category_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No products found or error: " . mysqli_error($conn) . "</p>";
}

// Show links to test different categories
echo "<h3>Test Links</h3>";
echo "<ul>";
echo "<li><a href='test_category_filter.php?category=0'>All Categories</a></li>";
echo "<li><a href='test_category_filter.php?category=1'>Category 1</a></li>";
echo "<li><a href='test_category_filter.php?category=3'>Category 3</a></li>";
echo "</ul>";
?>
