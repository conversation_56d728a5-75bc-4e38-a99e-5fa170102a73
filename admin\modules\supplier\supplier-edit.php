<?php 
include('includes/header.php');
require_once 'includes/functions.php';

// Validate and sanitize input
$id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if(!$id) {
    redirect('suppliers.php', 'Invalid supplier ID provided');
}

// Get supplier details
$supplier = getById('supplier', $id);
if($supplier['status'] !== 200) {
    redirect('suppliers.php', 'Supplier not found');
}
$supplierData = $supplier['data'];
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Edit Supplier</h4>
                <a href="suppliers.php" class="btn btn-danger">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <form action="code.php" method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="id" value="<?= htmlspecialchars($supplierData['id']) ?>" />
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Company Name *</label>
                        <input type="text" name="supplier_company_name" 
                               value="<?= htmlspecialchars($supplierData['supplier_company_name']) ?>" 
                               class="form-control" required />
                        <div class="invalid-feedback">Please enter company name</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Email Address *</label>
                        <input type="email" name="supplier_email" 
                               value="<?= htmlspecialchars($supplierData['supplier_email']) ?>" 
                               class="form-control" required />
                        <div class="invalid-feedback">Please enter a valid email address</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Phone Number *</label>
                        <input type="tel" name="supplier_phone" 
                               value="<?= htmlspecialchars($supplierData['supplier_phone']) ?>" 
                               class="form-control" required 
                               pattern="[0-9+\-\s]+" />
                        <div class="invalid-feedback">Please enter a valid phone number</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="1" <?= $supplierData['status'] == 1 ? 'selected' : '' ?>>Active</option>
                            <option value="0" <?= $supplierData['status'] == 0 ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="form-label">Address</label>
                        <textarea name="address" class="form-control" rows="3"><?= htmlspecialchars($supplierData['address'] ?? '') ?></textarea>
                    </div>
                </div>

                <div class="mb-3">
                    <button type="submit" name="updateSupplier" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Supplier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(event) {
    if (!this.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
    }
    this.classList.add('was-validated');
});
</script>

<?php include('includes/footer.php'); ?>
