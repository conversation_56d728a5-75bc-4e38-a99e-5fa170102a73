<?php
require '../../config/function.php';
require '../../config/dbcon.php';
require '../../includes/header.php';

// Get product data
if(isset($_GET['id'])) {
    $product_id = validate($_GET['id']);
    $product = getById('products', $product_id);
} else {
    redirect('products.php', 'No product ID provided');
}
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Product: <span class="text-primary"><?= htmlspecialchars($product['data']['name']); ?></span>
                <a href="products.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <form action="code.php" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="product_id" value="<?= $product['data']['id']; ?>">
                
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label>Description</label>
                        <textarea name="description" class="form-control" rows="5"><?= htmlspecialchars_decode($product['data']['description']); ?></textarea>
                        <small class="text-muted">You can use basic HTML tags like &lt;b&gt;bold&lt;/b&gt;, &lt;i&gt;italic&lt;/i&gt;, etc.</small>
                    </div>
                    
                    <div class="col-md-12">
                        <button type="submit" name="updateProduct" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include('../../includes/footer.php'); ?>
