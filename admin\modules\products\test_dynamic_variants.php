<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include necessary files
require '../../config/function.php';
require '../../config/dbcon.php';

// Get product ID from query string
$product_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Get selected colors
$selected_colors = [];
$selected_colors_query = mysqli_query($conn, "SELECT DISTINCT color_id FROM product_variants WHERE product_id = $product_id AND color_id IS NOT NULL");
if($selected_colors_query) {
    while($row = mysqli_fetch_assoc($selected_colors_query)) {
        $selected_colors[] = $row['color_id'];
    }
}

// Get selected sizes
$selected_sizes = [];
$selected_sizes_query = mysqli_query($conn, "SELECT DISTINCT size_id FROM product_variants WHERE product_id = $product_id AND size_id IS NOT NULL");
if($selected_sizes_query) {
    while($row = mysqli_fetch_assoc($selected_sizes_query)) {
        $selected_sizes[] = $row['size_id'];
    }
}

// Build the URL for dynamic_variants.php
$url = "dynamic_variants.php?product_id=$product_id";
foreach($selected_colors as $color_id) {
    $url .= "&colors[]=$color_id";
}
foreach($selected_sizes as $size_id) {
    $url .= "&sizes[]=$size_id";
}

// Output HTML
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dynamic Variants</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Dynamic Variants</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>Request Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Product ID:</strong> <?= $product_id ?></p>
                <p><strong>Selected Colors:</strong> <?= implode(', ', $selected_colors) ?></p>
                <p><strong>Selected Sizes:</strong> <?= implode(', ', $selected_sizes) ?></p>
                <p><strong>Request URL:</strong> <a href="<?= $url ?>" target="_blank"><?= $url ?></a></p>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Manual Test</h5>
            </div>
            <div class="card-body">
                <p>Click the button below to manually test the dynamic_variants.php file:</p>
                <button id="testBtn" class="btn btn-primary">Test Dynamic Variants</button>
                <hr>
                <div id="resultContainer" class="mt-3">
                    <h6>Result:</h6>
                    <pre id="result" class="border p-3 bg-light">Click the button to see the result</pre>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    document.getElementById('testBtn').addEventListener('click', function() {
        const url = '<?= $url ?>';
        const resultElement = document.getElementById('result');
        
        resultElement.textContent = 'Loading...';
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                resultElement.textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                resultElement.textContent = `Error: ${error.message}`;
            });
    });
    </script>
</body>
</html>
