<?php
include('includes/header.php');

// Check if admin is logged in and has appropriate permissions
if (!isset($_SESSION['loggedIn'])) {
    redirect('login.php', 'Login to continue...');
}

// Only allow admin users to access this page
if ($_SESSION['loggedInUser']['user_type'] !== 'admin') {
    $_SESSION['message'] = "You don't have permission to access the System page. This action has been logged.";

    // Log unauthorized access attempt
    $userId = $_SESSION['loggedInUser']['id'] ?? 0;
    $userName = $_SESSION['loggedInUser']['name'] ?? 'Unknown';
    $userIp = $_SERVER['REMOTE_ADDR'];
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "Unauthorized system page access attempt by user ID: $userId, Name: $userName, IP: $userIp, Time: $timestamp";

    // Log to file
    error_log($logMessage . "\n", 3, 'logs/security.log');

    // Redirect to dashboard
    redirect('index.php');
}

// Get server information
$serverInfo = [
    'PHP Version' => phpversion(),
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'Server OS' => PHP_OS,
    'Database Version' => mysqli_get_server_info($conn),
    'Max Upload Size' => ini_get('upload_max_filesize'),
    'Max Post Size' => ini_get('post_max_size'),
    'Memory Limit' => ini_get('memory_limit'),
    'Max Execution Time' => ini_get('max_execution_time') . ' seconds',
    'Document Root' => $_SERVER['DOCUMENT_ROOT'],
    'Server Time' => date('Y-m-d H:i:s'),
    'Server Timezone' => date_default_timezone_get()
];

// Get current database name
$dbInfo = [];
$tables = [];

// Get current database name
$dbNameQuery = "SELECT DATABASE() as db_name";
$dbNameResult = mysqli_query($conn, $dbNameQuery);
$dbName = "webapp"; // Default fallback
if ($dbNameResult && $row = mysqli_fetch_assoc($dbNameResult)) {
    $dbName = $row['db_name'];
}

// Get database size
$dbSizeQuery = "SELECT
                    table_schema AS 'Database',
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
                FROM information_schema.TABLES
                WHERE table_schema = '" . $dbName . "'
                GROUP BY table_schema";

$dbSizeResult = mysqli_query($conn, $dbSizeQuery);
if ($dbSizeResult) {
    $dbSize = mysqli_fetch_assoc($dbSizeResult)['Size (MB)'] ?? 'Unknown';
    $dbInfo['Database Size'] = $dbSize . ' MB';
}

// Get table count
$tableCountQuery = "SELECT COUNT(*) as count FROM information_schema.TABLES WHERE table_schema = '" . $dbName . "'";
$tableCountResult = mysqli_query($conn, $tableCountQuery);
if ($tableCountResult) {
    $tableCount = mysqli_fetch_assoc($tableCountResult)['count'] ?? 0;
    $dbInfo['Table Count'] = $tableCount;
}

// Get table information
$tablesQuery = "SELECT
                    table_name AS 'Table',
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
                    table_rows AS 'Rows'
                FROM information_schema.TABLES
                WHERE table_schema = '" . $dbName . "'
                ORDER BY (data_length + index_length) DESC";

$tablesResult = mysqli_query($conn, $tablesQuery);
if ($tablesResult) {
    while ($table = mysqli_fetch_assoc($tablesResult)) {
        $tables[] = $table;
    }
}

// Get PHP extensions
$extensions = get_loaded_extensions();
sort($extensions);

// Function to check if a service is running
function isServiceRunning($service) {
    if (stripos(PHP_OS, 'win') !== false) {
        // Windows
        $output = [];
        exec("sc query $service | find \"RUNNING\"", $output);
        return count($output) > 0;
    } else {
        // Linux/Unix
        $output = [];
        exec("systemctl is-active $service", $output);
        return isset($output[0]) && $output[0] === 'active';
    }
}

// Check common services
$services = [
    'Apache' => isServiceRunning('Apache') || isServiceRunning('httpd'),
    'MySQL' => isServiceRunning('MySQL') || isServiceRunning('mysqld'),
    'PHP-FPM' => isServiceRunning('php-fpm')
];
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">System Information</h4>
        </div>
        <div class="card-body">
            <!-- Content removed as requested -->
            <div class="alert alert-info">
                <p>This page is under development.</p>
            </div>

            <!-- Keep only the backup button -->
            <div class="text-center mt-4">
                <button class="btn btn-primary" id="backupDbBtn">
                    <i class="fas fa-download me-2"></i>Backup Database
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Result Modal -->
<div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resultModalLabel">Operation Result</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="resultModalBody">
                <!-- Result will be displayed here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable for database tables
    if (typeof simpleDatatables !== 'undefined') {
        new simpleDatatables.DataTable('#databaseTables', {
            perPage: 10,
            searchable: true,
            sortable: true,
            fixedHeight: true
        });
    }

    // Clear Cache Button
    const clearCacheBtn = document.getElementById('clearCacheBtn');
    if (clearCacheBtn) {
        clearCacheBtn.addEventListener('click', function() {
            // Simulate cache clearing
            setTimeout(function() {
                showResult('Cache cleared successfully!', 'success');
            }, 1000);
        });
    }

    // Optimize Database Button
    const optimizeDbBtn = document.getElementById('optimizeDbBtn');
    if (optimizeDbBtn) {
        optimizeDbBtn.addEventListener('click', function() {
            // Simulate database optimization
            setTimeout(function() {
                showResult('Database optimized successfully!', 'success');
            }, 1500);
        });
    }

    // Backup Database Button
    const backupDbBtn = document.getElementById('backupDbBtn');
    if (backupDbBtn) {
        backupDbBtn.addEventListener('click', function() {
            // Show loading state
            backupDbBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Backing up...';
            backupDbBtn.disabled = true;

            // Redirect to the existing backup script
            window.location.href = '<?= $BASE_URL ?>/admin/modules/backups_restore/db-backupdb.php';

            // Reset button after 3 seconds (in case user cancels download)
            setTimeout(function() {
                backupDbBtn.innerHTML = '<i class="fas fa-download"></i> Backup Database';
                backupDbBtn.disabled = false;
            }, 3000);
        });
    }

    // Function to show result in modal
    function showResult(message, type) {
        const resultModal = new bootstrap.Modal(document.getElementById('resultModal'));
        const resultModalBody = document.getElementById('resultModalBody');

        let alertClass = 'alert-info';
        if (type === 'success') alertClass = 'alert-success';
        if (type === 'error') alertClass = 'alert-danger';

        resultModalBody.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
        resultModal.show();
    }
});
</script>

<?php include('includes/footer.php'); ?>
