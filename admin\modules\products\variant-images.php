<?php
require '../../config/function.php';

// Check authentication
if(!isset($_SESSION['loggedIn'])) {
    redirect('../../auth/login.php', 'Login to continue...');
}

$product_id = validate($_GET['id']);

// Get product details
$product_query = "SELECT * FROM products WHERE id = ?";
$stmt = mysqli_prepare($conn, $product_query);
mysqli_stmt_bind_param($stmt, "i", $product_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if(!$result || mysqli_num_rows($result) == 0) {
    $_SESSION['message'] = "Product not found";
    header("Location: products.php");
    exit(0);
}

$product = mysqli_fetch_assoc($result);

include('../../includes/header.php');
?>

<div class="container-fluid px-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4>
                            <i class="fas fa-images"></i> 
                            Variant Images: <?= htmlspecialchars($product['name']) ?>
                        </h4>
                        <a href="product-variants.php?id=<?= $product_id ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Variants
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <?php include('../../includes/message.php'); ?>

                    <!-- Product Info -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Product:</strong> <?= htmlspecialchars($product['name']) ?><br>
                                        <strong>Base Price:</strong> R <?= number_format($product['sales_price'], 2) ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Status:</strong> 
                                        <span class="badge <?= $product['status'] ? 'bg-success' : 'bg-danger' ?>">
                                            <?= $product['status'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Variant Images Management -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Manage Variant Images</h5>
                            <p class="text-muted">Upload and manage images for each color variant of this product.</p>

                            <?php
                            // Get all colors used in variants for this product
                            $colors_query = "SELECT DISTINCT pv.color_id, pc.name as color_name, pc.color_code
                                           FROM product_variants pv
                                           LEFT JOIN product_colors pc ON pv.color_id = pc.id
                                           WHERE pv.product_id = ? AND pv.color_id IS NOT NULL
                                           ORDER BY pc.name";
                            $colors_stmt = mysqli_prepare($conn, $colors_query);
                            mysqli_stmt_bind_param($colors_stmt, "i", $product_id);
                            mysqli_stmt_execute($colors_stmt);
                            $colors_result = mysqli_stmt_get_result($colors_stmt);

                            if(mysqli_num_rows($colors_result) > 0):
                            ?>

                            <div class="row">
                                <?php while($color = mysqli_fetch_assoc($colors_result)): ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="d-flex align-items-center">
                                                <span style="display: inline-block; width: 20px; height: 20px; background-color: <?= $color['color_code'] ?>; border: 1px solid #ddd; border-radius: 3px; margin-right: 8px;"></span>
                                                <strong><?= htmlspecialchars($color['color_name']) ?></strong>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <?php
                                            // Get existing image for this color
                                            $image_query = "SELECT * FROM product_variant_images WHERE product_id = ? AND color_id = ?";
                                            $image_stmt = mysqli_prepare($conn, $image_query);
                                            mysqli_stmt_bind_param($image_stmt, "ii", $product_id, $color['color_id']);
                                            mysqli_stmt_execute($image_stmt);
                                            $image_result = mysqli_stmt_get_result($image_stmt);
                                            $existing_image = mysqli_fetch_assoc($image_result);
                                            ?>

                                            <!-- Current Image Display -->
                                            <div class="current-image mb-3">
                                                <?php if($existing_image): ?>
                                                    <img src="../../../uploads/products/<?= $existing_image['image'] ?>" 
                                                         class="img-fluid rounded" 
                                                         style="max-height: 200px; width: 100%; object-fit: cover;"
                                                         alt="<?= htmlspecialchars($color['color_name']) ?> variant">
                                                    <p class="text-muted small mt-2">Current: <?= $existing_image['image'] ?></p>
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                                        <span class="text-muted">No image uploaded</span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Upload Form -->
                                            <form action="variant-images-handler.php" method="POST" enctype="multipart/form-data">
                                                <input type="hidden" name="product_id" value="<?= $product_id ?>">
                                                <input type="hidden" name="color_id" value="<?= $color['color_id'] ?>">
                                                
                                                <div class="mb-3">
                                                    <input type="file" name="variant_image" class="form-control" accept="image/*" required>
                                                </div>
                                                
                                                <div class="d-grid gap-2">
                                                    <button type="submit" name="upload_image" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-upload"></i> 
                                                        <?= $existing_image ? 'Replace Image' : 'Upload Image' ?>
                                                    </button>
                                                    
                                                    <?php if($existing_image): ?>
                                                    <button type="submit" name="delete_image" class="btn btn-danger btn-sm" 
                                                            onclick="return confirm('Are you sure you want to delete this image?')">
                                                        <i class="fas fa-trash"></i> Delete Image
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            </div>

                            <?php else: ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> No Color Variants Found</h6>
                                <p>This product doesn't have any color variants yet. You need to:</p>
                                <ol>
                                    <li>Go to the <a href="product-variants.php?id=<?= $product_id ?>">Variant Management</a> page</li>
                                    <li>Add color variants to the product</li>
                                    <li>Return here to upload images for each color</li>
                                </ol>
                            </div>
                            <?php endif; ?>

                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<?php include('../../includes/footer.php'); ?>
