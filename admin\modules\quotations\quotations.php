<?php
include('../../includes/header.php');
require '../../config/function.php';

// Check if admin is logged in
if (!isset($_SESSION['loggedIn'])) {
    redirect('../../login.php', 'Login to continue...');
}

// Handle AJAX toggle status request
if (isset($_POST['ajax_toggle_status']) && isset($_POST['quotation_id']) && isset($_POST['current_status'])) {
    $id = validate($_POST['quotation_id']);
    $current_status = validate($_POST['current_status']);

    // Toggle the status (0 -> 1, 1 -> 0)
    $new_status = ($current_status == 0) ? 1 : 0;
    $status_text = ($new_status == 1) ? 'Viewed' : 'Unread';

    $query = "UPDATE quotations SET read_status = ? WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ii', $new_status, $id);

    $response = array();
    if (mysqli_stmt_execute($stmt)) {
        $response['success'] = true;
        $response['message'] = "Quotation marked as {$status_text}";
        $response['new_status'] = $new_status;
        $response['status_text'] = $status_text;
    } else {
        $response['success'] = false;
        $response['message'] = 'Something went wrong';
    }

    // Return JSON response for AJAX
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Status updates are now handled by update_status.php

// Get quotations with pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Count total quotations
$count_query = "SELECT COUNT(*) as total FROM quotations";
$count_result = mysqli_query($conn, $count_query);
$count_data = mysqli_fetch_assoc($count_result);
$total_records = $count_data['total'];
$total_pages = ceil($total_records / $limit);

// Get quotations with limit and offset
$query = "SELECT * FROM quotations ORDER BY created_at DESC LIMIT ?, ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'ii', $offset, $limit);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                Quotation Requests
                <?php if ($unreadQuotations > 0): ?>
                    <span class="badge bg-danger ms-2" title="Unread quotations"><?= $unreadQuotations ?> unread</span>
                <?php endif; ?>
                <?php if ($viewedQuotations > 0): ?>
                    <span class="badge bg-secondary ms-1" title="Viewed quotations"><?= $viewedQuotations ?> viewed</span>
                <?php endif; ?>
            </h4>
                <div>
                    <?php include('quotations_statistics_audio.php'); ?>
                    <?php include('quotations_instructions_updated.php'); ?>
                    <a href="../../index.php" class="btn btn-dark">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php
            // Display message from session if exists
            if(isset($_SESSION['message'])) {
                echo '<div class="alert alert-success alert-dismissible fade show" role="alert">';
                echo $_SESSION['message'];
                echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
                echo '</div>';
                unset($_SESSION['message']); // Clear the message
            }

            // Also call the regular alert function
            alertMessage();
            ?>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Quotation #</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($result) > 0): ?>
                            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                <tr <?= $row['read_status'] == 0 ? 'class="table-warning"' : '' ?>>
                                    <td><?= $row['id'] ?></td>
                                    <td><?= $row['quotation_number'] ?></td>
                                    <td><?= $row['name'] ?></td>
                                    <td><?= $row['email'] ?></td>
                                    <td><?= $row['phone'] ?></td>
                                    <td>R <?= number_format($row['total_amount'], 2) ?></td>
                                    <td>
                                        <span class="badge <?= getStatusBadgeClass($row['status']) ?>">
                                            <?= ucfirst($row['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= date('d M Y H:i', strtotime($row['created_at'])) ?></td>
                                    <td>
                                        <a href="quotation-view.php?id=<?= $row['id'] ?>" class="btn btn-sm btn-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($row['read_status'] == 0): ?>
                                            <a href="toggle_status_js.php?id=<?= $row['id'] ?>&current_status=0" class="btn btn-sm btn-success" title="Mark as Viewed">
                                                <i class="fas fa-check"></i> Mark as Viewed
                                            </a>
                                        <?php else: ?>
                                            <a href="toggle_status_js.php?id=<?= $row['id'] ?>&current_status=1" class="btn btn-sm btn-secondary" title="Mark as Unread">
                                                <i class="fas fa-times"></i> Mark as Unread
                                            </a>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#statusModal<?= $row['id'] ?>">
                                            <i class="fas fa-edit"></i> Status
                                        </button>

                                        <?php
                                        // Check if customer exists for this quotation
                                        $customer_query = "SELECT q.customer_id, c.name, c.client_code
                                                          FROM quotations q
                                                          LEFT JOIN customers c ON q.customer_id = c.id
                                                          WHERE q.id = ?";
                                        $customer_stmt = mysqli_prepare($conn, $customer_query);
                                        mysqli_stmt_bind_param($customer_stmt, 'i', $row['id']);
                                        mysqli_stmt_execute($customer_stmt);
                                        $customer_result = mysqli_stmt_get_result($customer_stmt);
                                        $customer_data = mysqli_fetch_assoc($customer_result);

                                        if (!empty($customer_data['customer_id'])) :
                                        ?>
                                            <button type="button" class="btn btn-sm btn-info" disabled>
                                                <i class="fas fa-user-check"></i> Customer: <?= $customer_data['client_code'] ?>
                                            </button>

                                            <!-- Create Invoice Button -->
                                            <a href="quotation-view.php?id=<?= $row['id'] ?>#createInvoiceModal" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#createInvoiceModal<?= $row['id'] ?>">
                                                <i class="fas fa-file-invoice"></i> Create Invoice
                                            </a>

                                            <!-- Create Invoice Modal for this row -->
                                            <div class="modal fade" id="createInvoiceModal<?= $row['id'] ?>" tabindex="-1" aria-labelledby="createInvoiceModalLabel<?= $row['id'] ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="createInvoiceModalLabel<?= $row['id'] ?>">Create Invoice</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <form action="create_invoice_simple2.php" method="POST">
                                                            <div class="modal-body">
                                                                <input type="hidden" name="quotation_id" value="<?= $row['id'] ?>">
                                                                <p>Create an invoice from this quotation for customer: <strong><?= $customer_data['name'] ?></strong></p>

                                                                <div class="mb-3">
                                                                    <label for="delivery_fee<?= $row['id'] ?>" class="form-label">Delivery Fee (Optional)</label>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">R</span>
                                                                        <input type="number" class="form-control" id="delivery_fee<?= $row['id'] ?>" name="delivery_fee" step="0.01" min="0" value="0">
                                                                    </div>
                                                                    <small class="text-muted">Add a delivery fee if applicable</small>
                                                                </div>

                                                                <div class="alert alert-info">
                                                                    <i class="fas fa-info-circle"></i> This will create an invoice and mark the quotation as processed.
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                <button type="submit" class="btn btn-primary">Create Invoice</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <!-- Save Customer Button -->
                                            <form action="save_customer.php" method="POST" class="d-inline">
                                                <input type="hidden" name="quotation_id" value="<?= $row['id'] ?>">
                                                <input type="hidden" name="return_url" value="quotations.php">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="fas fa-user-plus"></i> Save Customer
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <!-- Status Modal -->
                                        <div class="modal fade" id="statusModal<?= $row['id'] ?>" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="statusModalLabel">Update Quotation Status</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <form action="update_status.php" method="POST">
                                                        <div class="modal-body">
                                                            <input type="hidden" name="quotation_id" value="<?= $row['id'] ?>">
                                                            <div class="mb-3">
                                                                <label for="status" class="form-label">Status</label>
                                                                <select name="status" id="status" class="form-select">
                                                                    <option value="pending" <?= $row['status'] == 'pending' ? 'selected' : '' ?>>Pending</option>
                                                                    <option value="processed" <?= $row['status'] == 'processed' ? 'selected' : '' ?>>Processed</option>
                                                                    <option value="completed" <?= $row['status'] == 'completed' ? 'selected' : '' ?>>Completed</option>
                                                                    <option value="cancelled" <?= $row['status'] == 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                            <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" class="text-center">No quotation requests found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                            <a class="page-link" href="?page=<?= $page - 1 ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>

                        <li class="page-item <?= ($page >= $total_pages) ? 'disabled' : '' ?>">
                            <a class="page-link" href="?page=<?= $page + 1 ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Helper function to get badge class based on status
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'pending':
            return 'bg-warning text-dark';
        case 'processed':
            return 'bg-info text-dark';
        case 'completed':
            return 'bg-success';
        case 'cancelled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

// Add CSS for speech buttons if not already included
?>
<style>
.speech-btn {
    width: 38px;
    height: 38px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.speech-btn.speaking {
    background-color: #ffc107;
    border-color: #ffc107;
}
</style>

<?php
include('../../includes/footer.php');
?>
