<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';
require '../../config/dbcon.php';

// Get product ID from URL
$product_id = isset($_GET['id']) ? $_GET['id'] : 0;

if(!$product_id) {
    echo "<p>No product ID provided. Please add ?id=XX to the URL.</p>";
    exit;
}

// Get product data
$product = getById('products', $product_id);

echo "<h2>Product Data Structure</h2>";
echo "<pre>";
print_r($product);
echo "</pre>";

// Get product type data
echo "<h2>Product Type Data</h2>";
$sql = "SELECT * FROM product_types WHERE id = " . ($product['data']['product_type_id'] ?? 0);
$result = mysqli_query($conn, $sql);

if($result && mysqli_num_rows($result) > 0) {
    $product_type = mysqli_fetch_assoc($result);
    echo "<pre>";
    print_r($product_type);
    echo "</pre>";
} else {
    echo "<p>No product type found for ID: " . ($product['data']['product_type_id'] ?? 0) . "</p>";
    echo "<p>SQL Error: " . mysqli_error($conn) . "</p>";
}

// Get all product types
echo "<h2>All Product Types</h2>";
$sql = "SELECT * FROM product_types";
$result = mysqli_query($conn, $sql);

if($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Status</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['description'] . "</td>";
        echo "<td>" . ($row['status'] == 0 ? 'Visible' : 'Hidden') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No product types found.</p>";
    echo "<p>SQL Error: " . mysqli_error($conn) . "</p>";
}

// Fix the product type if needed
if(!isset($product['data']['product_type_id']) || empty($product['data']['product_type_id'])) {
    echo "<h2>Fixing Product Type</h2>";
    
    $sql = "UPDATE products SET product_type_id = 1 WHERE id = $product_id";
    if(mysqli_query($conn, $sql)) {
        echo "<p>Product type updated to 1 (Standard).</p>";
    } else {
        echo "<p>Failed to update product type: " . mysqli_error($conn) . "</p>";
    }
}
?>
