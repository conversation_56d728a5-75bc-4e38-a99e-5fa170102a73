<?php
// Output any errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Create a log file
$log_file = __DIR__ . '/files_structure_log.txt';

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Log the start
log_message("Script started");

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    log_message("POST request received");
    
    // Log POST data (excluding file contents)
    $post_data = $_POST;
    // Remove potentially large fields
    if (isset($post_data['description'])) {
        $post_data['description'] = '[CONTENT REMOVED]';
    }
    log_message("POST data", $post_data);
    
    // Log FILES structure
    if (!empty($_FILES)) {
        log_message("FILES array structure");
        
        foreach ($_FILES as $key => $file_data) {
            log_message("File input: $key", [
                'structure' => array_keys($file_data),
                'is_array_name' => is_array($file_data['name']),
                'count_if_array' => is_array($file_data['name']) ? count($file_data['name']) : 'N/A'
            ]);
            
            // For variant_images, check the nested structure
            if ($key === 'variant_images' && is_array($file_data['name'])) {
                log_message("variant_images keys", array_keys($file_data['name']));
                
                foreach ($file_data['name'] as $color_id => $files) {
                    if (is_array($files)) {
                        log_message("Color ID: $color_id", [
                            'files_count' => count($files),
                            'files' => $files
                        ]);
                        
                        // Check if tmp_name has the same structure
                        if (isset($file_data['tmp_name'][$color_id]) && is_array($file_data['tmp_name'][$color_id])) {
                            log_message("tmp_name structure for color $color_id", [
                                'count' => count($file_data['tmp_name'][$color_id]),
                                'values' => $file_data['tmp_name'][$color_id]
                            ]);
                        } else {
                            log_message("tmp_name structure for color $color_id is invalid", isset($file_data['tmp_name'][$color_id]) ? $file_data['tmp_name'][$color_id] : 'Not set');
                        }
                    } else {
                        log_message("Color ID: $color_id has non-array files", $files);
                    }
                }
            }
        }
    } else {
        log_message("No files uploaded");
    }
    
    // Redirect back to the product creation page
    echo "<script>alert('Form data logged. Check files_structure_log.txt for details.'); window.location.href = 'products-create.php';</script>";
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Check Files Structure</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-info">
            <h3>Instructions</h3>
            <p>This script will log the structure of the $_FILES array when submitting the product form.</p>
            <p>To use it:</p>
            <ol>
                <li>Change the form action in products-create.php to point to this script</li>
                <li>Submit the form as usual</li>
                <li>Check the files_structure_log.txt file for the logged data</li>
                <li>Change the form action back to code.php</li>
            </ol>
        </div>
        
        <div class="alert alert-warning">
            <h4>Temporary Form Action Change</h4>
            <p>Add this to your browser console to temporarily change the form action:</p>
            <pre>document.getElementById('productForm').action = 'check_files.php';</pre>
        </div>
        
        <a href="products-create.php" class="btn btn-primary">Back to Product Creation</a>
    </div>
</body>
</html>
