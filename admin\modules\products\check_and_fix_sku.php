<?php
require '../../config/function.php';

echo "<h2>Checking and Fixing SKU Field in product_variants Table</h2>";

// Check if SKU field exists in product_variants table
$check_sku = mysqli_query($conn, "SHOW COLUMNS FROM product_variants LIKE 'sku'");

if(mysqli_num_rows($check_sku) == 0) {
    echo "<p style='color: orange;'>SKU field does not exist in product_variants table. Adding it now...</p>";
    
    // Add SKU field to product_variants table
    $add_sku_sql = "ALTER TABLE product_variants ADD COLUMN sku VARCHAR(100) NOT NULL DEFAULT '' AFTER product_id";
    
    if(mysqli_query($conn, $add_sku_sql)) {
        echo "<p style='color: green;'>✓ SKU field added successfully to product_variants table.</p>";
        
        // Now update existing variants with generated SKUs
        echo "<p>Updating existing variants with generated SKUs...</p>";
        
        $existing_variants = mysqli_query($conn, "SELECT * FROM product_variants WHERE sku = '' OR sku IS NULL");
        
        if($existing_variants && mysqli_num_rows($existing_variants) > 0) {
            $updated_count = 0;
            
            while($variant = mysqli_fetch_assoc($existing_variants)) {
                // Generate SKU for this variant
                $product_id = $variant['product_id'];
                $color_id = $variant['color_id'];
                $size_id = $variant['size_id'];
                
                // Get product name
                $product_query = mysqli_query($conn, "SELECT name FROM products WHERE id = $product_id");
                $product = mysqli_fetch_assoc($product_query);
                $product_name = $product ? $product['name'] : 'PROD';
                
                // Clean product name for SKU base
                $sku_base = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $product_name));
                $sku_base = substr($sku_base, 0, 8);
                
                // Add color code if available
                $color_code = '';
                if ($color_id) {
                    $color_query = mysqli_query($conn, "SELECT name FROM product_colors WHERE id = $color_id");
                    $color = mysqli_fetch_assoc($color_query);
                    if ($color) {
                        $color_code = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $color['name']), 0, 3));
                    }
                }
                
                // Add size code if available
                $size_code = '';
                if ($size_id) {
                    $size_query = mysqli_query($conn, "SELECT name FROM product_sizes WHERE id = $size_id");
                    $size = mysqli_fetch_assoc($size_query);
                    if ($size) {
                        $size_code = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $size['name']), 0, 3));
                    }
                }
                
                // Combine to create SKU
                $sku = $sku_base . '-' . $product_id;
                if ($color_code) $sku .= '-' . $color_code;
                if ($size_code) $sku .= '-' . $size_code;
                $sku .= '-' . $variant['id']; // Use variant ID for uniqueness
                
                // Update the variant with the generated SKU
                $update_sql = "UPDATE product_variants SET sku = '$sku' WHERE id = {$variant['id']}";
                if(mysqli_query($conn, $update_sql)) {
                    $updated_count++;
                    echo "<p style='color: blue;'>Updated variant ID {$variant['id']} with SKU: $sku</p>";
                } else {
                    echo "<p style='color: red;'>Failed to update variant ID {$variant['id']}: " . mysqli_error($conn) . "</p>";
                }
            }
            
            echo "<p style='color: green;'>✓ Updated $updated_count existing variants with SKUs.</p>";
        } else {
            echo "<p>No existing variants found that need SKU updates.</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Failed to add SKU field: " . mysqli_error($conn) . "</p>";
    }
} else {
    echo "<p style='color: green;'>✓ SKU field already exists in product_variants table.</p>";
}

// Show current table structure
echo "<h3>Current product_variants table structure:</h3>";
$structure = mysqli_query($conn, "DESCRIBE product_variants");

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

while($row = mysqli_fetch_assoc($structure)) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}

echo "</table>";

// Show sample variants
echo "<h3>Sample product variants (first 5):</h3>";
$sample_variants = mysqli_query($conn, "SELECT pv.*, pc.name as color_name, ps.name as size_name 
                                       FROM product_variants pv 
                                       LEFT JOIN product_colors pc ON pv.color_id = pc.id 
                                       LEFT JOIN product_sizes ps ON pv.size_id = ps.id 
                                       LIMIT 5");

if($sample_variants && mysqli_num_rows($sample_variants) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Product ID</th><th>SKU</th><th>Color</th><th>Size</th><th>Price</th><th>Quantity</th></tr>";
    
    while($variant = mysqli_fetch_assoc($sample_variants)) {
        echo "<tr>";
        echo "<td>" . $variant['id'] . "</td>";
        echo "<td>" . $variant['product_id'] . "</td>";
        echo "<td>" . $variant['sku'] . "</td>";
        echo "<td>" . ($variant['color_name'] ?: 'N/A') . "</td>";
        echo "<td>" . ($variant['size_name'] ?: 'N/A') . "</td>";
        echo "<td>" . $variant['additional_price'] . "</td>";
        echo "<td>" . $variant['quantity'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No variants found in the database.</p>";
}

echo "<p><a href='products-create.php'>← Back to Create Product</a></p>";
?>
