<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Check if product_images table exists
$sql = "SHOW TABLES LIKE 'product_images'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>product_images table exists.</p>";
    
    // Show table structure
    $sql = "DESCRIBE product_images";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>product_images table structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>product_images table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE product_images (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        image VARCHAR(255) NOT NULL,
        display_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    )";
    
    if(mysqli_query($conn, $sql)) {
        echo "<p>product_images table created successfully.</p>";
    } else {
        echo "<p>Error creating product_images table: " . mysqli_error($conn) . "</p>";
    }
}

// Check if product_variant_images table exists
$sql = "SHOW TABLES LIKE 'product_variant_images'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>product_variant_images table exists.</p>";
    
    // Show table structure
    $sql = "DESCRIBE product_variant_images";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>product_variant_images table structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>product_variant_images table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE product_variant_images (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        color_id INT NOT NULL,
        image VARCHAR(255) NOT NULL,
        display_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    )";
    
    if(mysqli_query($conn, $sql)) {
        echo "<p>product_variant_images table created successfully.</p>";
    } else {
        echo "<p>Error creating product_variant_images table: " . mysqli_error($conn) . "</p>";
    }
}
?>
