<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Create a log file
$log_file = __DIR__ . '/direct_sql_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - <PERSON>ript started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Direct SQL query to insert a product
try {
    // Basic product data
    $category_id = 1; // Use a valid category ID
    $name = "Direct SQL Product " . date('YmdHis');
    $description = "This is a test product created on " . date('Y-m-d H:i:s');
    $barcode = "TEST" . rand(1000, 9999);
    $cost_price = 100.00;
    $sales_price = 150.00;
    $vat_percentage = 0;
    $vatT = 0.00;
    $price = 150.00;
    $quantity = 10;
    $image = "default.jpg";
    $status = 0;
    $created_at = date('Y-m-d H:i:s');
    $featured = 0;
    $is_featured = 0;
    $product_type_id = 1;
    $can_be_component = 0;
    
    log_message("Basic product data prepared", [
        'category_id' => $category_id,
        'name' => $name,
        'cost_price' => $cost_price,
        'sales_price' => $sales_price,
        'vat_percentage' => $vat_percentage,
        'vatT' => $vatT,
        'price' => $price,
        'quantity' => $quantity,
        'barcode' => $barcode,
        'image' => $image,
        'status' => $status
    ]);
    
    // Direct SQL query
    $sql = "INSERT INTO products (
        category_id, name, description, barcode, cost_price, sales_price, 
        vat_percentage, vatT, price, quantity, image, status, created_at,
        featured, is_featured, product_type_id, can_be_component
    ) VALUES (
        '$category_id', '$name', '$description', '$barcode', '$cost_price', '$sales_price',
        '$vat_percentage', '$vatT', '$price', '$quantity', '$image', '$status', '$created_at',
        '$featured', '$is_featured', '$product_type_id', '$can_be_component'
    )";
    
    log_message("SQL Query", $sql);
    
    $result = mysqli_query($conn, $sql);
    $product_id = mysqli_insert_id($conn);
    
    log_message("Query result", [
        'success' => $result ? 'true' : 'false',
        'product_id' => $product_id,
        'error' => mysqli_error($conn)
    ]);
    
    if($result) {
        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Success!</h3>
            <p>The product was created successfully with ID: $product_id</p>
            <p><a href='products.php'>View all products</a></p>
        </div>";
    } else {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Error!</h3>
            <p>Failed to insert the product into the database.</p>
            <p>MySQL Error: " . mysqli_error($conn) . "</p>
            <p><a href='products-create.php'>Try again</a></p>
        </div>";
    }
} catch (Exception $e) {
    log_message("Exception", [
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>An exception occurred: " . $e->getMessage() . "</p>
        <p><a href='products-create.php'>Try again</a></p>
    </div>";
} catch (Error $e) {
    log_message("Error", [
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>An error occurred: " . $e->getMessage() . "</p>
        <p><a href='products-create.php'>Try again</a></p>
    </div>";
}
?>
