<?php include('includes/header.php'); ?>
<script src="https://cdn.ckeditor.com/ckeditor5/23.0.0/classic/ckeditor.js"></script>
<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">services Edit
                <a href="index.php" class="btn me-4 btn-dark float-end">Dashboard <i
                        class="fas fa-tachometer-alt"></i></a>
                <!-- <a href="contacts-create.php" class="btn me-4 btn-primary float-end">Add Contacts</a>
                <a href="contacts-export.php" class="btn me-4 btn-info float-end">Export</a> -->
            </h4>
        </div>
        <div class="card-body">

            <?php alertMessage(); ?>

            <form action="code.php" method="POST">

                <?php
                $parmValue = checkParamId('id');
                if(!is_numeric($parmValue)){
                    echo '<h5>'.$parmValue.'</h5>';
                    return false;
                }

                $services = getServiceById($parmValue);
                if($services['status'] == 200)
                {
                ?>

                <input type="hidden" name="contactsId" value="<?= $services['data']['id']; ?>">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="">Name *</label>
                        <input type="text" name="name" value="<?= $services['data']['name']; ?>" required
                            class="form-control" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <label for="">Description *</label>
                        <textarea name="description" id="description" required
                            class="form-control"><?= $services['data']['description']; ?></textarea>
                    </div>
                    <script>
                    ClassicEditor
                        .create(document.querySelector('#description'), {
                            toolbar: {
                                items: [
                                    'heading',
                                    '|',
                                    'bold',
                                    'italic',
                                    'underline',
                                    'fontSize',
                                    'fontFamily',
                                    'fontColor',
                                    'alignment',
                                    'bullets',
                                    'numberedList',
                                    'mediaEmbed',
                                    '|',
                                    'undo',
                                    'redo'
                                ]
                            }
                        })
                        .then(editor => {
                            console.log(editor);
                        })
                        .catch(error => {
                            console.error(error);
                        });
                    </script>
                    <div class="col-md-6 mb-3">
                        <label for="">Cost Price *</label>
                        <input type="number" name="cost_price" value="<?= $services['data']['cost_price']; ?>" required
                            class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Sales Price *</label>
                        <input type="number" name="sales_price" value="<?= $services['data']['sales_price']; ?>"
                            required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">VAT Percentage *</label>
                        <input type="number" name="vat_percentage" value="<?= $services['data']['vat_percentage']; ?>"
                            required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Price *</label>
                        <input type="number" name="price" value="<?= $services['data']['price']; ?>" required
                            class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Quantity *</label>
                        <input type="number" name="quantity" value="<?= $services['data']['quantity']; ?>" required
                            class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Status *</label>
                        <select name="status" required class="form-control">
                            <option value="0" <?= ($services['data']['status'] == 0) ? 'selected' : ''; ?>>Visible
                            </option>
                            <option value="1" <?= ($services['data']['status'] == 1) ? 'selected' : ''; ?>>Hidden
                            </option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Featured *</label>
                        <select name="featured" required class="form-control">
                            <option value="0" <?= ($services['data']['featured'] == 0) ? 'selected' : ''; ?>>No</option>
                            <option value="1" <?= ($services['data']['featured'] == 1) ? 'selected' : ''; ?>>Yes
                            </option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3 text-end">
                        <br />
                        <button type="submit" name="updateServices" class="btn btn-primary">Update</button>
                    </div>
                </div>
                <?php
                }
                else
                {
                    echo '<h5>'.$category['message'].'</h5>';
                }
                ?>
            </form>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>

<?php
function getServiceById($id) {
    global $conn;
    $stmt = mysqli_prepare($conn, "SELECT * FROM services WHERE id = ? LIMIT 1");
    mysqli_stmt_bind_param($stmt, "i", $id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if($result && mysqli_num_rows($result) > 0) {
        return [
            'status' => 200,
            'data' => mysqli_fetch_assoc($result),
            'message' => 'Record Found'
        ];
    }
    return [
        'status' => 404,
        'message' => 'No Record Found'
    ];
}

function updateService($id, $data) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, 
        "UPDATE services SET 
         name = ?, description = ?, cost_price = ?, 
         sales_price = ?, vat_percentage = ?, price = ?, 
         quantity = ?, status = ? 
         WHERE id = ?"
    );
    
    mysqli_stmt_bind_param($stmt, "ssddddiii",
        $data['name'],
        $data['description'],
        $data['cost_price'],
        $data['sales_price'],
        $data['vat_percentage'],
        $data['price'],
        $data['quantity'],
        $data['status'],
        $id
    );
    
    return mysqli_stmt_execute($stmt);
<?php include('includes/footer.php'); ?>
