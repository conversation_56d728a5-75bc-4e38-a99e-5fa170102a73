<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Create a log file
$log_file = __DIR__ . '/direct_sql_form_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - <PERSON>ript started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    log_message("Form submitted", $_POST);

    try {
        // Basic product data
        $category_id = validate($_POST['category_id']);
        $name = validate($_POST['name']);
        $description = $_POST['description']; // Don't validate HTML content
        $barcode = isset($_POST['barcode']) ? validate($_POST['barcode']) : '0000';
        $cost_price = validate($_POST['cost_price']);
        $sales_price = validate($_POST['sales_price']);
        $vat_percentage = isset($_POST['vat_percentage']) ? validate($_POST['vat_percentage']) : 0;
        $vatT = isset($_POST['vatT']) ? validate($_POST['vatT']) : 0;
        $price = isset($_POST['price']) ? validate($_POST['price']) : $sales_price;
        $quantity = validate($_POST['quantity']);
        $status = isset($_POST['status']) ? 1 : 0;

        log_message("Basic product data", [
            'category_id' => $category_id,
            'name' => $name,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price,
            'quantity' => $quantity,
            'barcode' => $barcode,
            'status' => $status
        ]);

        // Handle main image upload
        $finalImage = "default.jpg"; // Default image
        if(isset($_FILES['image']['name']) && !empty($_FILES['image']['name'])) {
            $upload_path = "../../../uploads/products/";
            $image = $_FILES['image']['name'];

            if($image != "") {
                $finalImage = time() . '_' . $image;
                $upload_to = $upload_path . $finalImage;

                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0777, true);
                }

                if(move_uploaded_file($_FILES['image']['tmp_name'], $upload_to)) {
                    log_message("Main image uploaded successfully", $finalImage);
                } else {
                    log_message("Failed to upload main image", $_FILES['image']['error']);
                }
            }
        }

        // Other fields with default values
        $featured = isset($_POST['featured']) ? 1 : 0;
        $is_featured = 0;
        $product_type_id = isset($_POST['product_type_id']) ? validate($_POST['product_type_id']) : 1;
        $can_be_component = isset($_POST['can_be_component']) ? 1 : 0;
        $created_at = date('Y-m-d H:i:s');

        // Handle discounted price if enabled
        $discounted_price = 'NULL';
        if(isset($_POST['enable_discounted_price']) && isset($_POST['discounted_price']) && !empty($_POST['discounted_price'])) {
            $discounted_price = "'" . validate($_POST['discounted_price']) . "'";
            log_message("Discounted price set", $discounted_price);
        }

        // Direct SQL query
        $sql = "INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '$category_id', '$name', '$description', '$barcode', '$cost_price', '$sales_price',
            '$vat_percentage', '$vatT', '$price', $discounted_price, '$quantity', '$finalImage', '$status', '$created_at',
            '$featured', '$is_featured', '$product_type_id', '$can_be_component'
        )";

        log_message("SQL Query", $sql);

        $result = mysqli_query($conn, $sql);
        $product_id = mysqli_insert_id($conn);

        log_message("Query result", [
            'success' => $result ? 'true' : 'false',
            'product_id' => $product_id,
            'error' => mysqli_error($conn)
        ]);

        if($result && $product_id) {
            log_message("Product created successfully with ID: $product_id");

            // Handle product type
            $product_type_id = isset($_POST['product_type_id']) ? validate($_POST['product_type_id']) : 1; // Default to Standard (1)
            $can_be_component = isset($_POST['can_be_component']) ? 1 : 0;

            log_message("Product type information", [
                'product_type_id' => $product_type_id,
                'can_be_component' => $can_be_component
            ]);

            // Handle product variants (colors and sizes)
            if(isset($_POST['variant_colors']) || isset($_POST['variant_sizes'])) {
                $color_ids = isset($_POST['variant_colors']) ? $_POST['variant_colors'] : [];
                $size_ids = isset($_POST['variant_sizes']) ? $_POST['variant_sizes'] : [];
                $variant_pricing_type = isset($_POST['variant_pricing_type']) ? validate($_POST['variant_pricing_type']) : 'none';

                log_message("Processing product variants", [
                    'color_ids' => $color_ids,
                    'size_ids' => $size_ids,
                    'variant_pricing_type' => $variant_pricing_type
                ]);

                // If using base price for all variants (no additional cost)
                if($variant_pricing_type === 'none') {
                    // If both colors and sizes are selected, create combinations
                    if(!empty($color_ids) && !empty($size_ids)) {
                        // Calculate quantity per variant
                        $variant_count = count($color_ids) * count($size_ids);
                        $quantity_per_variant = $variant_count > 0 ? ceil($quantity / $variant_count) : $quantity;

                        log_message("Creating color and size combinations", [
                            'variant_count' => $variant_count,
                            'quantity_per_variant' => $quantity_per_variant
                        ]);

                        foreach($color_ids as $color_id) {
                            foreach($size_ids as $size_id) {
                                $sql = "INSERT INTO product_variants (product_id, color_id, size_id, additional_price, quantity, status)
                                       VALUES ('$product_id', '$color_id', '$size_id', 0, '$quantity_per_variant', 0)";

                                $variant_result = mysqli_query($conn, $sql);
                                log_message("Variant created", [
                                    'color_id' => $color_id,
                                    'size_id' => $size_id,
                                    'success' => $variant_result ? 'true' : 'false',
                                    'error' => mysqli_error($conn)
                                ]);
                            }
                        }
                    }
                    // If only colors are selected
                    else if(!empty($color_ids)) {
                        // Calculate quantity per variant
                        $variant_count = count($color_ids);
                        $quantity_per_variant = $variant_count > 0 ? ceil($quantity / $variant_count) : $quantity;

                        log_message("Creating color variants only", [
                            'variant_count' => $variant_count,
                            'quantity_per_variant' => $quantity_per_variant
                        ]);

                        foreach($color_ids as $color_id) {
                            $sql = "INSERT INTO product_variants (product_id, color_id, size_id, additional_price, quantity, status)
                                   VALUES ('$product_id', '$color_id', NULL, 0, '$quantity_per_variant', 0)";

                            $variant_result = mysqli_query($conn, $sql);
                            log_message("Color variant created", [
                                'color_id' => $color_id,
                                'success' => $variant_result ? 'true' : 'false',
                                'error' => mysqli_error($conn)
                            ]);
                        }
                    }
                    // If only sizes are selected
                    else if(!empty($size_ids)) {
                        // Calculate quantity per variant
                        $variant_count = count($size_ids);
                        $quantity_per_variant = $variant_count > 0 ? ceil($quantity / $variant_count) : $quantity;

                        log_message("Creating size variants only", [
                            'variant_count' => $variant_count,
                            'quantity_per_variant' => $quantity_per_variant
                        ]);

                        foreach($size_ids as $size_id) {
                            $sql = "INSERT INTO product_variants (product_id, color_id, size_id, additional_price, quantity, status)
                                   VALUES ('$product_id', NULL, '$size_id', 0, '$quantity_per_variant', 0)";

                            $variant_result = mysqli_query($conn, $sql);
                            log_message("Size variant created", [
                                'size_id' => $size_id,
                                'success' => $variant_result ? 'true' : 'false',
                                'error' => mysqli_error($conn)
                            ]);
                        }
                    }
                }
                // If using additional pricing for variants
                else if($variant_pricing_type === 'additional' && isset($_POST['variant_color_ids']) && isset($_POST['variant_size_ids'])) {
                    $color_ids = $_POST['variant_color_ids'] ?? [];
                    $size_ids = $_POST['variant_size_ids'] ?? [];
                    $additional_prices = $_POST['variant_additional_prices'] ?? [];
                    $variant_quantities = $_POST['variant_quantities'] ?? [];

                    log_message("Processing variants with additional pricing", [
                        'color_ids' => $color_ids,
                        'size_ids' => $size_ids,
                        'additional_prices' => $additional_prices,
                        'variant_quantities' => $variant_quantities
                    ]);

                    // Save each variant
                    for($i = 0; $i < count($additional_prices); $i++) {
                        if(isset($additional_prices[$i]) && isset($variant_quantities[$i])) {
                            $color_id = isset($color_ids[$i]) ? $color_ids[$i] : 'NULL';
                            $size_id = isset($size_ids[$i]) ? $size_ids[$i] : 'NULL';
                            $additional_price = validate($additional_prices[$i]);
                            $variant_quantity = validate($variant_quantities[$i]);

                            $sql = "INSERT INTO product_variants (product_id, color_id, size_id, additional_price, quantity, status)
                                   VALUES ('$product_id', $color_id, $size_id, '$additional_price', '$variant_quantity', 0)";

                            $variant_result = mysqli_query($conn, $sql);
                            log_message("Variant with additional price created", [
                                'color_id' => $color_id,
                                'size_id' => $size_id,
                                'additional_price' => $additional_price,
                                'quantity' => $variant_quantity,
                                'success' => $variant_result ? 'true' : 'false',
                                'error' => mysqli_error($conn)
                            ]);
                        }
                    }
                }
            }

            // Handle bulk pricing if enabled
            if(isset($_POST['enable_bulk_pricing'])) {
                $min_quantities = $_POST['bulk_min_qty'] ?? [];
                $max_quantities = $_POST['bulk_max_qty'] ?? [];
                $bulk_prices = $_POST['bulk_price'] ?? [];

                log_message("Processing bulk pricing", [
                    'min_quantities' => $min_quantities,
                    'max_quantities' => $max_quantities,
                    'bulk_prices' => $bulk_prices
                ]);

                // Save each bulk pricing rule
                for($i = 0; $i < count($min_quantities); $i++) {
                    if(isset($min_quantities[$i]) && isset($max_quantities[$i]) && isset($bulk_prices[$i])) {
                        $min_qty = validate($min_quantities[$i]);
                        $max_qty = validate($max_quantities[$i]);
                        $bulk_price = validate($bulk_prices[$i]);

                        $sql = "INSERT INTO product_bulk_pricing (product_id, min_quantity, max_quantity, price, created_at)
                               VALUES ('$product_id', '$min_qty', '$max_qty', '$bulk_price', NOW())";

                        $bulk_result = mysqli_query($conn, $sql);
                        log_message("Bulk pricing rule inserted", [
                            'min_qty' => $min_qty,
                            'max_qty' => $max_qty,
                            'price' => $bulk_price,
                            'success' => $bulk_result ? 'true' : 'false',
                            'error' => mysqli_error($conn)
                        ]);
                    }
                }
            }

            // Handle product label if provided
            if(isset($_POST['product_labels']) && !empty($_POST['product_labels'][0])) {
                $label_id = validate($_POST['product_labels'][0]);

                log_message("Processing product label", [
                    'product_id' => $product_id,
                    'label_id' => $label_id
                ]);

                $sql = "INSERT INTO product_label_assignments (product_id, label_id)
                       VALUES ('$product_id', '$label_id')";

                $label_result = mysqli_query($conn, $sql);
                log_message("Product label inserted", [
                    'success' => $label_result ? 'true' : 'false',
                    'error' => mysqli_error($conn)
                ]);
            }

            // Handle additional images upload
            if(isset($_FILES['additional_images']) && !empty($_FILES['additional_images']['name'][0])) {
                $upload_path = "../../../uploads/products/";

                // Ensure upload directory exists
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0777, true);
                }

                // Limit to 3 additional images
                $maxAdditionalImages = 3;
                $uploadCount = 0;

                log_message("Processing additional images", $_FILES['additional_images']);

                // Loop through each uploaded file (up to 3)
                for($i = 0; $i < count($_FILES['additional_images']['name']) && $uploadCount < $maxAdditionalImages; $i++) {
                    $additionalImage = $_FILES['additional_images']['name'][$i];

                    if($additionalImage != "") {
                        $finalAdditionalImage = time() . '_additional_' . $i . '_' . $additionalImage;
                        $upload_to = $upload_path . $finalAdditionalImage;

                        log_message("Uploading additional image", [
                            'index' => $i,
                            'name' => $additionalImage,
                            'final_name' => $finalAdditionalImage,
                            'upload_to' => $upload_to
                        ]);

                        if(move_uploaded_file($_FILES['additional_images']['tmp_name'][$i], $upload_to)) {
                            // Insert into product_images table
                            $sql = "INSERT INTO product_images (product_id, image, display_order)
                                   VALUES ('$product_id', '$finalAdditionalImage', " . ($i + 1) . ")";

                            $image_result = mysqli_query($conn, $sql);
                            log_message("Additional image inserted", [
                                'success' => $image_result ? 'true' : 'false',
                                'error' => mysqli_error($conn)
                            ]);

                            $uploadCount++;
                        } else {
                            log_message("Failed to upload additional image", [
                                'index' => $i,
                                'error' => $_FILES['additional_images']['error'][$i]
                            ]);
                        }
                    }
                }
            }

            // Success - redirect to products page
            log_message("Redirecting to products.php");
            redirect('products.php', 'Product Created Successfully!');
        } else {
            // Failure - redirect back to create page
            log_message("Failed to create product");
            redirect('products-create.php', 'Something Went Wrong! MySQL Error: ' . mysqli_error($conn));
        }
    } catch (Exception $e) {
        log_message("Exception", [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        redirect('products-create.php', 'Error: ' . $e->getMessage());
    } catch (Error $e) {
        log_message("Error", [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        redirect('products-create.php', 'Error: ' . $e->getMessage());
    }
} else {
    log_message("Form not submitted");
    redirect('products-create.php', 'Invalid request method');
}
?>
