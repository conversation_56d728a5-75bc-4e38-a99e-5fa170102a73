<?php
require '../../config/function.php';
require '../../config/dbcon.php';
require '../../includes/header.php';
include('product_audio.php'); // Include the product audio functionality
?>

<!-- <PERSON><PERSON> Styles -->
<style>
    #stickyButtonContainer {
        position: fixed;
        bottom: 100px;
        right: 30px;
        z-index: 99999;
    }

    #stickyButton {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
        padding: 15px 25px;
        font-weight: bold;
        font-size: 18px;
        border-radius: 50px;
        background-color: #28a745; /* Green background */
        border-color: #28a745;
        min-width: 200px;
        transition: all 0.3s ease;
    }

    #stickyButton:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
        background-color: #218838;
        border-color: #1e7e34;
    }
</style>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Add Product
                <a href="products.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <form id="productForm" action="direct_sql_form.php" method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label>Name *</label>
                        <input type="text" name="name" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label>Select Category *</label>
                        <select name="category_id" class="form-select" required>
                            <option value="">Select Category</option>
                            <?php
                            // Get all categories
                            $categories_query = "SELECT * FROM categories ORDER BY name ASC";
                            $categories_result = mysqli_query($conn, $categories_query);

                            if($categories_result && mysqli_num_rows($categories_result) > 0) {
                                // First, get all main categories (parent_id is NULL or 0)
                                $main_categories = [];
                                $sub_categories = [];

                                // Organize categories into main and sub categories
                                while($category = mysqli_fetch_assoc($categories_result)) {
                                    if(empty($category['parent_id'])) {
                                        $main_categories[] = $category;
                                    } else {
                                        $sub_categories[$category['parent_id']][] = $category;
                                    }
                                }

                                // Display main categories and their sub-categories
                                foreach($main_categories as $main_category) {
                                    echo '<option value="' . $main_category['id'] . '">' . $main_category['name'] . '</option>';

                                    // Check if this main category has sub-categories
                                    if(isset($sub_categories[$main_category['id']])) {
                                        foreach($sub_categories[$main_category['id']] as $sub_category) {
                                            echo '<option value="' . $sub_category['id'] . '">-- ' . $sub_category['name'] . '</option>';
                                        }
                                    }
                                }
                            } else {
                                echo '<option value="">No Categories Found</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>Cost Price *</label>
                        <input type="number" step="0.01" name="cost_price" id="cost_price" required class="form-control" onchange="calculatePrice()" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>Sales Price *</label>
                        <input type="number" step="0.01" name="sales_price" id="sales_price" required class="form-control" onchange="calculatePrice()" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>VAT Percentage *</label>
                        <input type="number" step="0.01" name="vat_percentage" id="vat" required class="form-control" value="0" onchange="calculatePrice()" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>VAT Amount</label>
                        <input type="number" step="0.01" name="vatT" id="vatT" readonly class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>Final Price</label>
                        <input type="number" step="0.01" name="price" id="price" readonly class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label>Quantity *</label>
                        <input type="number" name="quantity" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label>Barcode</label>
                        <input type="text" name="barcode" class="form-control" value="0000" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label>Image (Main)</label>
                        <input type="file" name="image" class="form-control" accept="image/*" />
                        <small class="text-muted">This will be the primary product image shown in listings</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label>Additional Images</label>
                        <input type="file" name="additional_images[]" class="form-control" accept="image/*" multiple />
                        <small class="text-muted">You can select up to 3 additional images (hold Ctrl to select multiple)</small>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label>Description</label>
                        <textarea name="description" id="editor" class="form-control" rows="3"></textarea>
                    </div>
                    <!-- Product Labels -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-tag me-2"></i>Product Labels
                                    <?php createAudioButton('labelsAudioBtn', 'Listen to Product Labels explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Select a label to display on this product:</p>
                                <div class="d-flex flex-wrap gap-3">
                                    <?php
                                    $labels = mysqli_query($conn, "SELECT * FROM product_labels WHERE status = 0 ORDER BY name ASC");
                                    if(mysqli_num_rows($labels) > 0) {
                                        while($label = mysqli_fetch_assoc($labels)) {
                                            ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="product_labels[]" value="<?= $label['id'] ?>" id="label<?= $label['id'] ?>">
                                                <label class="form-check-label" for="label<?= $label['id'] ?>">
                                                    <span class="badge" style="background-color: <?= $label['background_color'] ?>; color: <?= $label['text_color'] ?>; padding: 5px 10px;">
                                                        <?= htmlspecialchars($label['name']) ?>
                                                    </span>
                                                </label>
                                            </div>
                                            <?php
                                        }
                                    } else {
                                        echo '<p>No labels available. <a href="labels-create.php">Create some labels</a> first.</p>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Discounted Price -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-tags me-2"></i>Discounted Price
                                    <?php createAudioButton('discountedPriceAudioBtn', 'Listen to Discounted Price explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableDiscountedPrice" name="enable_discounted_price" value="1">
                                            <label class="form-check-label" for="enableDiscountedPrice">
                                                Enable discounted price
                                            </label>
                                        </div>
                                        <small class="text-muted">Set a discounted price for this product</small>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body p-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="fw-bold">Final Price:</span>
                                                    <span class="fs-5 text-success" id="finalPriceDisplay">R 0.00</span>
                                                </div>
                                                <small class="text-muted">Price including VAT</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="discounted-price-container mt-3" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>Discounted Price *</label>
                                            <input type="number" step="0.01" name="discounted_price" id="discounted_price" class="form-control" onchange="calculatePrice()" />
                                            <small class="text-muted">Enter the new discounted price (before VAT)</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label>Savings</label>
                                            <div class="input-group">
                                                <input type="text" id="savings_amount" class="form-control" readonly />
                                                <span class="input-group-text">R</span>
                                            </div>
                                            <div class="mt-2">
                                                <span id="savings_percentage" class="badge bg-success">0% Off</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Type -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-cube me-2"></i>Product Type
                                    <?php createAudioButton('typeAudioBtn', 'Listen to Product Type explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="productType">Select Product Type</label>
                                    <select name="product_type_id" id="productType" class="form-select">
                                        <?php
                                        // Check if the table exists
                                        $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'product_types'");
                                        if(mysqli_num_rows($table_check) > 0) {
                                            $product_types_query = mysqli_query($conn, "SELECT * FROM product_types WHERE status = 0");
                                            if($product_types_query && mysqli_num_rows($product_types_query) > 0) {
                                                while($type = mysqli_fetch_assoc($product_types_query)) {
                                                    echo "<option value='{$type['id']}'>{$type['name']}</option>";
                                                }
                                            } else {
                                                echo "<option value='1'>Standard</option>";
                                                echo "<option value='2'>Buildable</option>";
                                                echo "<option value='3'>Component</option>";
                                            }
                                        } else {
                                            echo "<option value='1'>Standard</option>";
                                            echo "<option value='2'>Buildable</option>";
                                            echo "<option value='3'>Component</option>";
                                        }
                                        ?>
                                    </select>
                                    <small class="text-muted">Standard: Regular product | Buildable: Product that can be built from components | Component: Can be used in buildable products</small>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="can_be_component" id="canBeComponent" value="1">
                                    <label class="form-check-label" for="canBeComponent">
                                        This product can be used as a component in buildable products
                                    </label>
                                </div>

                                <!-- Buildable Product Options -->
                                <div id="buildableOptions" class="mt-3 p-3 border rounded" style="display: none;">
                                    <h6 class="mb-3">Buildable Product Options</h6>
                                    <div class="mb-3">
                                        <label for="buildableCategory">Select Component Category</label>
                                        <select id="buildableCategory" class="form-select mb-2">
                                            <option value="">Select a category</option>
                                            <?php
                                            // Get main categories (parent_id = 0 or NULL)
                                            $main_categories_query = mysqli_query($conn, "SELECT * FROM categories WHERE (parent_id = 0 OR parent_id IS NULL) AND status = 0 ORDER BY name ASC");

                                            if($main_categories_query && mysqli_num_rows($main_categories_query) > 0) {
                                                while($main_category = mysqli_fetch_assoc($main_categories_query)) {
                                                    echo "<option value='{$main_category['id']}' style='font-weight: bold;'>{$main_category['name']}</option>";

                                                    // Get subcategories for this main category
                                                    $sub_categories_query = mysqli_query($conn, "SELECT * FROM categories WHERE parent_id = {$main_category['id']} AND status = 0 ORDER BY name ASC");

                                                    if($sub_categories_query && mysqli_num_rows($sub_categories_query) > 0) {
                                                        while($sub_category = mysqli_fetch_assoc($sub_categories_query)) {
                                                            echo "<option value='{$sub_category['id']}' style='padding-left: 20px;'>&nbsp;&nbsp;- {$sub_category['name']}</option>";
                                                        }
                                                    }
                                                }
                                            } else {
                                                // Fallback to regular category list if parent_id doesn't exist
                                                $categories = getAll('categories');
                                                if($categories && mysqli_num_rows($categories) > 0){
                                                    foreach($categories as $category){
                                                        echo "<option value='{$category['id']}'>{$category['name']}</option>";
                                                    }
                                                }
                                            }
                                            ?>
                                        </select>
                                        <button type="button" id="loadComponents" class="btn btn-sm btn-primary">Load Components</button>
                                    </div>

                                    <div id="componentsContainer" class="mb-3" style="display: none;">
                                        <label>Available Components</label>
                                        <div id="componentsList" class="border p-2 rounded mb-2" style="max-height: 200px; overflow-y: auto;">
                                            <!-- Components will be loaded here -->
                                        </div>
                                        <button type="button" id="addSelectedComponents" class="btn btn-sm btn-success">Add Selected Components</button>
                                    </div>

                                    <div id="selectedComponentsContainer">
                                        <label>Selected Components</label>
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Component</th>
                                                    <th>Quantity</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody id="selectedComponentsTable">
                                                <!-- Selected components will be added here -->
                                                <tr>
                                                    <td colspan="3" class="text-center text-muted">No components selected yet</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Variants -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-tags me-2"></i>Product Variants
                                    <?php createAudioButton('variantsAudioBtn', 'Listen to Product Variants explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableVariants" name="enable_variants" value="1">
                                    <label class="form-check-label" for="enableVariants">
                                        Enable product variants (colors and sizes)
                                    </label>
                                </div>

                                <div class="variants-container mt-3" style="display: none;">
                                    <!-- Variant Pricing Type -->
                                    <div class="mb-3">
                                        <label>Variant Pricing Type</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="variant_pricing_type" id="variantPricingNone" value="none" checked>
                                            <label class="form-check-label" for="variantPricingNone">
                                                Use base price for all variants
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="variant_pricing_type" id="variantPricingAdditional" value="additional">
                                            <label class="form-check-label" for="variantPricingAdditional">
                                                Add additional cost for variants
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Colors Section -->
                                    <div class="mb-4">
                                        <h6 class="border-bottom pb-2">Available Colors</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="border p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                                                    <?php
                                                    // Check if the table exists
                                                    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'product_colors'");
                                                    if(mysqli_num_rows($table_check) > 0) {
                                                        $colors_query = mysqli_query($conn, "SELECT * FROM product_colors WHERE status = 0 ORDER BY name ASC");
                                                        if($colors_query && mysqli_num_rows($colors_query) > 0) {
                                                            echo '<div class="row row-cols-1 row-cols-md-2 g-3">';
                                                            while($color = mysqli_fetch_assoc($colors_query)) {
                                                                // Check for color_code or hex_code field
                                                                $color_code = isset($color['color_code']) ? $color['color_code'] : (isset($color['hex_code']) ? $color['hex_code'] : '#cccccc');
                                                                ?>
                                                                <div class="col">
                                                                    <div class="card h-100 border">
                                                                        <div class="card-body p-2">
                                                                            <div class="d-flex align-items-center mb-2">
                                                                                <div class="form-check">
                                                                                    <input class="form-check-input color-checkbox" type="checkbox" name="variant_colors[]" value="<?= $color['id'] ?>" id="color<?= $color['id'] ?>" data-name="<?= htmlspecialchars($color['name']) ?>" data-color="<?= $color_code ?>">
                                                                                </div>
                                                                                <div style="width: 30px; height: 30px; background-color: <?= $color_code ?>; border: 1px solid #ddd; border-radius: 4px; margin: 0 10px;"></div>
                                                                                <label class="form-check-label mb-0" for="color<?= $color['id'] ?>">
                                                                                    <?= htmlspecialchars($color['name']) ?>
                                                                                </label>
                                                                            </div>
                                                                            <div class="color-upload-container" id="color-upload-<?= $color['id'] ?>" style="display: none;">
                                                                                <div class="variant-image-upload">
                                                                                    <input type="file" name="variant_images[<?= $color['id'] ?>][]" class="variant-image-input d-none" accept="image/*">
                                                                                    <button type="button" class="btn btn-sm btn-outline-secondary variant-image-btn w-100">
                                                                                        <i class="fas fa-upload"></i> Upload Image
                                                                                    </button>
                                                                                    <div class="variant-image-preview mt-2" style="display: none;">
                                                                                        <img src="" class="img-thumbnail" style="width: 100%; height: 80px; object-fit: cover;">
                                                                                        <button type="button" class="btn btn-sm btn-danger remove-variant-image position-absolute top-0 end-0">×</button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <?php
                                                            }
                                                            echo '</div>';
                                                        } else {
                                                            echo '<p>No colors available.</p>';
                                                        }
                                                    } else {
                                                        echo '<p>Colors feature not set up yet.</p>';
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div id="selectedColorsContainer" class="border p-3 rounded" style="min-height: 200px;">
                                                    <p class="text-muted mb-2">Selected colors will appear here</p>
                                                    <div id="selectedColors" class="d-flex flex-wrap gap-2"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Sizes Section -->
                                    <div class="mb-4">
                                        <h6 class="border-bottom pb-2">Available Sizes</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="border p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                                                    <?php
                                                    // Check if the table exists
                                                    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'product_sizes'");
                                                    if(mysqli_num_rows($table_check) > 0) {
                                                        $sizes_query = mysqli_query($conn, "SELECT * FROM product_sizes WHERE status = 0 ORDER BY name ASC");
                                                        if($sizes_query && mysqli_num_rows($sizes_query) > 0) {
                                                            echo '<div class="row row-cols-1 row-cols-md-2 g-3">';
                                                            while($size = mysqli_fetch_assoc($sizes_query)) {
                                                                ?>
                                                                <div class="col">
                                                                    <div class="card h-100 border">
                                                                        <div class="card-body p-2 d-flex align-items-center">
                                                                            <div class="form-check">
                                                                                <input class="form-check-input size-checkbox" type="checkbox" name="variant_sizes[]" value="<?= $size['id'] ?>" id="size<?= $size['id'] ?>" data-name="<?= htmlspecialchars($size['name']) ?>">
                                                                            </div>
                                                                            <label class="form-check-label mb-0 ms-2" for="size<?= $size['id'] ?>">
                                                                                <?= htmlspecialchars($size['name']) ?>
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <?php
                                                            }
                                                            echo '</div>';
                                                        } else {
                                                            echo '<p>No sizes available.</p>';
                                                        }
                                                    } else {
                                                        echo '<p>Sizes feature not set up yet.</p>';
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div id="selectedSizesContainer" class="border p-3 rounded" style="min-height: 200px;">
                                                    <p class="text-muted mb-2">Selected sizes will appear here</p>
                                                    <div id="selectedSizes" class="d-flex flex-wrap gap-2"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>



                                    <!-- Variant Combinations -->
                                    <div id="variantCombinationsContainer" class="mb-3" style="display: none;">
                                        <h6 class="border-bottom pb-2">Variant Pricing & Stock</h6>
                                        <p class="text-muted">Set additional pricing and stock for each variant combination</p>

                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>Color</th>
                                                        <th>Size</th>
                                                        <th>SKU</th>
                                                        <th>Additional Price (R)</th>
                                                        <th>Stock</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="variantCombinationsTable">
                                                    <!-- Variant combinations will be added here -->
                                                    <tr>
                                                        <td colspan="4" class="text-center text-muted">Select colors and sizes to generate combinations</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Pricing -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-money-bill-wave me-2"></i>Bulk Pricing
                                    <?php createAudioButton('bulkPricingAudioBtn', 'Listen to Bulk Pricing explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableBulkPricing" name="enable_bulk_pricing" value="1">
                                            <label class="form-check-label" for="enableBulkPricing">
                                                Enable bulk pricing
                                            </label>
                                        </div>
                                        <small class="text-muted">Set different prices for quantity ranges</small>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body p-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="fw-bold">Final Price:</span>
                                                    <span class="fs-5 text-success" id="finalPriceDisplay">R 0.00</span>
                                                </div>
                                                <small class="text-muted">Price including VAT</small>
                                                <div id="discountedPriceInfo" style="display: none;" class="mt-1">
                                                    <span class="badge bg-danger">Discounted</span>
                                                    <small class="text-muted ms-1">Original: <span id="originalPriceDisplay">R 0.00</span></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="bulk-pricing-container mt-3" style="display: none;">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Quantity Range</th>
                                                    <th>Price Per Unit</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody id="bulkPricingTableBody">
                                                <tr>
                                                    <td>
                                                        <div class="input-group">
                                                            <input type="number" name="bulk_min_qty[]" class="form-control" placeholder="Min" min="2" value="2">
                                                            <span class="input-group-text">-</span>
                                                            <input type="number" name="bulk_max_qty[]" class="form-control" placeholder="Max" min="5" value="5">
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <input type="number" step="0.01" name="bulk_price[]" class="form-control" placeholder="Price per unit">
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-danger remove-bulk-row">Remove</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success" id="addBulkPricingRow">Add Range</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label>Product Visibility</label>
                        <br/>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="statusSwitch" name="status" style="width:50px;height:25px;" />
                            <label class="form-check-label" for="statusSwitch">
                                <span id="statusText" style="font-weight:bold;color:green">
                                    VISIBLE - Shown to customers
                                </span>
                            </label>
                        </div>
                        <small class="text-muted">Toggle the switch to change visibility</small>
                    </div>
                    <!-- Regular button (will be hidden on larger screens) -->
                    <div class="col-md-6 d-md-none">
                        <button type="submit" name="saveProduct" class="btn btn-primary" id="saveProductBtn">Save</button>
                        <button type="button" class="btn btn-info ms-2" onclick="console.log('Form submission test'); document.getElementById('productForm').submit();">Test Submit</button>
                    </div>
                </div>
                <!-- Sticky button container placeholder -->
                <div class="d-none d-md-block" style="height: 60px;"></div>

                <!-- Hidden submit button with name saveProduct -->
                <input type="hidden" name="saveProduct" value="1">
            </form>

            <!-- Sticky button that triggers form submission -->
            <div id="stickyButtonContainer" class="d-none d-md-block">
                <button type="button" id="stickyButton" class="btn btn-success" onclick="document.getElementById('productForm').submit();">
                    <i class="fas fa-save me-2"></i> Save Product
                </button>
            </div>
        </div>
    </div>
</div>

<!-- CKEditor CDN -->
<script src="https://cdn.ckeditor.com/ckeditor5/38.1.0/classic/ckeditor.js"></script>

<script>
    // Debug form submission
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('productForm');
        if (form) {
            form.addEventListener('submit', function(e) {
                console.log('Form submission event triggered');
                // Uncomment to debug form data
                // e.preventDefault();
                // console.log('Form data:', new FormData(form));
            });
        } else {
            console.error('Product form not found!');
        }

        const saveBtn = document.getElementById('saveProductBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', function(e) {
                console.log('Save button clicked');
            });
        }

        const stickyBtn = document.getElementById('stickyButton');
        if (stickyBtn) {
            stickyBtn.addEventListener('click', function(e) {
                console.log('Sticky button clicked');
            });
        }
    });

function calculatePrice() {
    const costPrice = parseFloat(document.getElementById("cost_price").value) || 0;
    const salesPrice = parseFloat(document.getElementById("sales_price").value) || 0;
    const vat = parseFloat(document.getElementById("vat").value) || 0;

    // Check if discounted price is enabled and has a value
    const enableDiscountedPrice = document.getElementById("enableDiscountedPrice").checked;
    const discountedPriceInput = document.getElementById("discounted_price");
    const discountedPrice = enableDiscountedPrice && discountedPriceInput ? parseFloat(discountedPriceInput.value) || 0 : 0;

    // Use discounted price if enabled, otherwise use sales price
    const basePrice = enableDiscountedPrice && discountedPrice > 0 ? discountedPrice : salesPrice;

    const vatAmount = basePrice * (vat/100);
    const finalPrice = basePrice + vatAmount;

    document.getElementById("vatT").value = vatAmount.toFixed(2);
    document.getElementById("price").value = finalPrice.toFixed(2);

    // Update the final price display
    const finalPriceDisplay = document.getElementById("finalPriceDisplay");
    if (finalPriceDisplay) {
        finalPriceDisplay.textContent = `R ${finalPrice.toFixed(2)}`;
    }

    // Update the discounted price info in the Bulk Pricing section
    const discountedPriceInfo = document.getElementById("discountedPriceInfo");
    const originalPriceDisplay = document.getElementById("originalPriceDisplay");

    if (discountedPriceInfo && originalPriceDisplay) {
        if (enableDiscountedPrice && discountedPrice > 0 && salesPrice > 0) {
            // Show the discounted price info
            discountedPriceInfo.style.display = "block";

            // Calculate the original price with VAT
            const originalVatAmount = salesPrice * (vat/100);
            const originalFinalPrice = salesPrice + originalVatAmount;

            // Display the original price
            originalPriceDisplay.textContent = `R ${originalFinalPrice.toFixed(2)}`;
        } else {
            // Hide the discounted price info
            discountedPriceInfo.style.display = "none";
        }
    }

    // Calculate and display savings if discounted price is enabled
    if (enableDiscountedPrice && discountedPrice > 0 && salesPrice > 0) {
        const savingsAmount = salesPrice - discountedPrice;
        const savingsPercentage = (savingsAmount / salesPrice) * 100;

        const savingsAmountElement = document.getElementById("savings_amount");
        const savingsPercentageElement = document.getElementById("savings_percentage");

        if (savingsAmountElement) {
            savingsAmountElement.value = savingsAmount.toFixed(2);
        }

        if (savingsPercentageElement) {
            savingsPercentageElement.textContent = `${savingsPercentage.toFixed(0)}% Off`;
        }
    }
}

// Initialize CKEditor and other functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor
    ClassicEditor
        .create(document.querySelector('#editor'), {
            toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote'],
            heading: {
                options: [
                    { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                    { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                ]
            }
        })
        .catch(error => {
            console.error(error);
        });

    // Initialize the final price display
    calculatePrice();

    // Discounted Price Toggle
    const enableDiscountedPriceCheckbox = document.getElementById('enableDiscountedPrice');
    const discountedPriceContainer = document.querySelector('.discounted-price-container');

    if (enableDiscountedPriceCheckbox && discountedPriceContainer) {
        enableDiscountedPriceCheckbox.addEventListener('change', function() {
            discountedPriceContainer.style.display = this.checked ? 'block' : 'none';
            calculatePrice(); // Recalculate price when toggling
        });
    }

    // Product Type Toggle
    const productTypeSelect = document.getElementById('productType');
    const canBeComponentCheckbox = document.getElementById('canBeComponent');
    const buildableOptionsContainer = document.getElementById('buildableOptions');

    // Get the container of the canBeComponent checkbox
    const canBeComponentContainer = canBeComponentCheckbox.closest('.form-check');

    productTypeSelect.addEventListener('change', function() {
        // If product type is Component (id=3), disable the can_be_component checkbox
        if (this.value === '3') {
            canBeComponentCheckbox.checked = false;
            canBeComponentCheckbox.disabled = true;
            canBeComponentContainer.style.display = 'block'; // Show the checkbox but disabled
            buildableOptionsContainer.style.display = 'none';
        } else if (this.value === '2') { // Buildable product
            canBeComponentCheckbox.checked = false;
            canBeComponentCheckbox.disabled = true;
            canBeComponentContainer.style.display = 'none'; // Hide the checkbox completely
            buildableOptionsContainer.style.display = 'block';
        } else { // Standard product
            canBeComponentCheckbox.disabled = false;
            canBeComponentContainer.style.display = 'block'; // Show the checkbox
            buildableOptionsContainer.style.display = 'none';
        }
    });

    // Trigger the change event to set initial state
    productTypeSelect.dispatchEvent(new Event('change'));

    // Buildable Product Components
    const loadComponentsBtn = document.getElementById('loadComponents');
    const buildableCategorySelect = document.getElementById('buildableCategory');
    const componentsContainer = document.getElementById('componentsContainer');
    const componentsList = document.getElementById('componentsList');
    const addSelectedComponentsBtn = document.getElementById('addSelectedComponents');
    const selectedComponentsTable = document.getElementById('selectedComponentsTable');

    if (loadComponentsBtn && buildableCategorySelect) {
        loadComponentsBtn.addEventListener('click', function() {
            const categoryId = buildableCategorySelect.value;
            if (!categoryId) {
                alert('Please select a category first');
                return;
            }

            // Show loading indicator
            componentsList.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

            // Fetch components from the selected category
            // In a real implementation, this would be an AJAX call to the server
            // For now, we'll simulate it with category-specific dummy data
            setTimeout(() => {
                componentsList.innerHTML = '';

                // Different components based on selected category
                let components = [];

                // Computer components (category 1)
                if (categoryId === '1' || categoryId === '5') {
                    components = [
                        { id: 1, name: 'Intel Core i5-12600K', price: 4999.99 },
                        { id: 2, name: 'AMD Ryzen 7 5800X', price: 5499.99 },
                        { id: 3, name: 'NVIDIA GeForce RTX 3070', price: 12999.99 }
                    ];
                }
                // Memory components (category 2)
                else if (categoryId === '2' || categoryId === '6') {
                    components = [
                        { id: 4, name: 'Corsair 16GB DDR4 RAM', price: 1299.99 },
                        { id: 5, name: 'Kingston 32GB DDR4 RAM', price: 2499.99 },
                        { id: 6, name: 'Samsung 1TB NVMe SSD', price: 1899.99 }
                    ];
                }
                // Power components (category 3)
                else if (categoryId === '3' || categoryId === '7') {
                    components = [
                        { id: 7, name: 'Corsair 750W PSU', price: 1599.99 },
                        { id: 8, name: 'EVGA 850W PSU', price: 1999.99 }
                    ];
                }
                // Default components for other categories
                else {
                    components = [
                        { id: 9, name: 'Generic Component 1', price: 499.99 },
                        { id: 10, name: 'Generic Component 2', price: 799.99 }
                    ];
                }

                if (components.length === 0) {
                    componentsList.innerHTML = '<p class="text-center">No components found in this category</p>';
                    return;
                }

                components.forEach(component => {
                    const componentItem = document.createElement('div');
                    componentItem.className = 'form-check';
                    componentItem.innerHTML = `
                        <input class="form-check-input component-checkbox" type="checkbox" value="${component.id}" id="component${component.id}" data-name="${component.name}" data-price="${component.price}">
                        <label class="form-check-label" for="component${component.id}">
                            ${component.name} - R${component.price.toFixed(2)}
                        </label>
                    `;
                    componentsList.appendChild(componentItem);
                });
            }, 500); // Simulate network delay

            componentsContainer.style.display = 'block';
        });

        // Add selected components to the table
        if (addSelectedComponentsBtn) {
            addSelectedComponentsBtn.addEventListener('click', function() {
                const selectedCheckboxes = document.querySelectorAll('.component-checkbox:checked');

                if (selectedCheckboxes.length === 0) {
                    alert('Please select at least one component');
                    return;
                }

                // Clear the "No components selected yet" message if it exists
                const noComponentsRow = selectedComponentsTable.querySelector('tr td.text-center.text-muted');
                if (noComponentsRow) {
                    noComponentsRow.closest('tr').remove();
                }

                selectedCheckboxes.forEach(checkbox => {
                    const componentId = checkbox.value;
                    const componentName = checkbox.dataset.name;
                    const componentPrice = checkbox.dataset.price;

                    // Check if component is already in the table
                    const existingRow = document.querySelector(`#component-row-${componentId}`);
                    if (existingRow) {
                        // Increment quantity
                        const quantityInput = existingRow.querySelector('.component-quantity');
                        quantityInput.value = parseInt(quantityInput.value) + 1;
                    } else {
                        // Add new row
                        const newRow = document.createElement('tr');
                        newRow.id = `component-row-${componentId}`;
                        newRow.innerHTML = `
                            <td>
                                ${componentName}
                                <small class="text-muted d-block">R${parseFloat(componentPrice).toFixed(2)} each</small>
                                <input type="hidden" name="component_ids[]" value="${componentId}">
                                <input type="hidden" name="component_names[]" value="${componentName}">
                                <input type="hidden" name="component_prices[]" value="${componentPrice}">
                            </td>
                            <td>
                                <input type="number" name="component_quantities[]" class="form-control form-control-sm component-quantity" value="1" min="1">
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger remove-component">Remove</button>
                            </td>
                        `;
                        selectedComponentsTable.appendChild(newRow);

                        // Add event listener to the remove button
                        newRow.querySelector('.remove-component').addEventListener('click', function() {
                            // If this is the last row, add the "No components selected yet" message back
                            if (selectedComponentsTable.querySelectorAll('tr').length === 1) {
                                const emptyRow = document.createElement('tr');
                                emptyRow.innerHTML = '<td colspan="3" class="text-center text-muted">No components selected yet</td>';
                                selectedComponentsTable.appendChild(emptyRow);
                            }
                            newRow.remove();
                        });
                    }

                    // Uncheck the checkbox
                    checkbox.checked = false;
                });
            });
        }
    }

    // Product Variants Toggle
    const enableVariantsCheckbox = document.getElementById('enableVariants');
    const variantsContainer = document.querySelector('.variants-container');
    const variantPricingNone = document.getElementById('variantPricingNone');
    const variantPricingAdditional = document.getElementById('variantPricingAdditional');
    const variantCombinationsContainer = document.getElementById('variantCombinationsContainer');

    if (variantsContainer) {
        // Toggle variants container
        enableVariantsCheckbox.addEventListener('change', function() {
            if (this.checked) {
                variantsContainer.style.display = 'block';

            } else {
                variantsContainer.style.display = 'none';
            }
        });

        // Set initial state
        variantsContainer.style.display = enableVariantsCheckbox.checked ? 'block' : 'none';


        // Toggle variant combinations based on pricing type
        // Function to update variant combinations table title and columns
        function updateVariantCombinationsTable(showPricing) {
            const tableTitle = document.querySelector('#variantCombinationsContainer h6');
            const tableHeader = document.querySelector('#variantCombinationsContainer thead tr');

            if (showPricing) {
                tableTitle.textContent = 'Variant Pricing & Stock';
                tableHeader.innerHTML = `
                    <th>Color</th>
                    <th>Size</th>
                    <th>SKU</th>
                    <th>Additional Price (R)</th>
                    <th>Stock</th>
                `;
            } else {
                tableTitle.textContent = 'Variant Stock';
                tableHeader.innerHTML = `
                    <th>Color</th>
                    <th>Size</th>
                    <th>SKU</th>
                    <th>Stock</th>
                `;
            }

            // Regenerate combinations
            generateVariantCombinations(showPricing);
        }

        variantPricingAdditional.addEventListener('change', function() {
            if (this.checked) {
                variantCombinationsContainer.style.display = 'block';
                updateVariantCombinationsTable(true);
            }
        });

        variantPricingNone.addEventListener('change', function() {
            if (this.checked) {
                variantCombinationsContainer.style.display = 'block';
                updateVariantCombinationsTable(false);
            }
        });

        // Handle color selection
        const colorCheckboxes = document.querySelectorAll('.color-checkbox');
        const selectedColors = document.getElementById('selectedColors');

        colorCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Toggle the upload button container
                const colorId = this.value;
                const uploadContainer = document.getElementById(`color-upload-${colorId}`);
                if (uploadContainer) {
                    uploadContainer.style.display = this.checked ? 'block' : 'none';
                }

                updateSelectedColors();

                if (variantPricingAdditional.checked) {
                    generateVariantCombinations(true);
                } else if (variantPricingNone.checked) {
                    generateVariantCombinations(false);
                }
            });

            // Set initial state for existing checkboxes
            if (checkbox.checked) {
                const colorId = checkbox.value;
                const uploadContainer = document.getElementById(`color-upload-${colorId}`);
                if (uploadContainer) {
                    uploadContainer.style.display = 'block';
                }
            }
        });

        function updateSelectedColors() {
            selectedColors.innerHTML = '';
            const checkedColors = document.querySelectorAll('.color-checkbox:checked');

            if (checkedColors.length === 0) {
                selectedColors.innerHTML = '<p class="text-muted">No colors selected</p>';
                return;
            }

            checkedColors.forEach(checkbox => {
                const colorName = checkbox.dataset.name;
                const colorCode = checkbox.dataset.color;

                const colorBadge = document.createElement('div');
                colorBadge.className = 'card mb-2';
                colorBadge.innerHTML = `
                    <div class="card-body p-2 d-flex align-items-center">
                        <div style="width: 30px; height: 30px; background-color: ${colorCode}; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;"></div>
                        <span class="fw-medium">${colorName}</span>
                    </div>
                `;

                selectedColors.appendChild(colorBadge);
            });
        }

        // Handle size selection
        const sizeCheckboxes = document.querySelectorAll('.size-checkbox');
        const selectedSizes = document.getElementById('selectedSizes');

        sizeCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectedSizes();
                if (variantPricingAdditional.checked) {
                    generateVariantCombinations(true);
                } else if (variantPricingNone.checked) {
                    generateVariantCombinations(false);
                }
            });
        });

        function updateSelectedSizes() {
            selectedSizes.innerHTML = '';
            const checkedSizes = document.querySelectorAll('.size-checkbox:checked');

            if (checkedSizes.length === 0) {
                selectedSizes.innerHTML = '<p class="text-muted">No sizes selected</p>';
                return;
            }

            checkedSizes.forEach(checkbox => {
                const sizeName = checkbox.dataset.name;

                const sizeBadge = document.createElement('div');
                sizeBadge.className = 'card mb-2';
                sizeBadge.innerHTML = `
                    <div class="card-body p-2 d-flex align-items-center">
                        <span class="fw-medium">${sizeName}</span>
                    </div>
                `;

                selectedSizes.appendChild(sizeBadge);
            });
        }



        // Generate variant combinations
        function generateVariantCombinations(showPricing = true) {
            const variantCombinationsTable = document.getElementById('variantCombinationsTable');
            const checkedColors = document.querySelectorAll('.color-checkbox:checked');
            const checkedSizes = document.querySelectorAll('.size-checkbox:checked');

            variantCombinationsTable.innerHTML = '';

            if (checkedColors.length === 0 && checkedSizes.length === 0) {
                const colSpan = showPricing ? 4 : 3;
                variantCombinationsTable.innerHTML = `<tr><td colspan="${colSpan}" class="text-center text-muted">Select colors and sizes to generate combinations</td></tr>`;
                return;
            }

            // If only colors are selected
            if (checkedColors.length > 0 && checkedSizes.length === 0) {
                checkedColors.forEach(colorCheckbox => {
                    const colorId = colorCheckbox.value;
                    const colorName = colorCheckbox.dataset.name;
                    const colorCode = colorCheckbox.dataset.color;

                    const row = document.createElement('tr');

                    if (showPricing) {
                        row.innerHTML = `
                            <td>
                                <div class="d-flex align-items-center">
                                    <div style="width: 20px; height: 20px; background-color: ${colorCode}; border: 1px solid #ddd; border-radius: 4px; margin-right: 8px;"></div>
                                    ${colorName}
                                    <input type="hidden" name="variant_color_ids[]" value="${colorId}">
                                    <input type="hidden" name="variant_size_ids[]" value="">
                                </div>
                            </td>
                            <td>-</td>
                            <td>
                                <input type="text" name="variant_skus[]" class="form-control form-control-sm" placeholder="Auto-generated if empty">
                            </td>
                            <td>
                                <input type="number" name="variant_additional_prices[]" class="form-control form-control-sm" value="0" min="0" step="0.01">
                            </td>
                            <td>
                                <input type="number" name="variant_quantities[]" class="form-control form-control-sm" value="10" min="0">
                            </td>
                        `;
                    } else {
                        row.innerHTML = `
                            <td>
                                <div class="d-flex align-items-center">
                                    <div style="width: 20px; height: 20px; background-color: ${colorCode}; border: 1px solid #ddd; border-radius: 4px; margin-right: 8px;"></div>
                                    ${colorName}
                                    <input type="hidden" name="variant_color_ids[]" value="${colorId}">
                                    <input type="hidden" name="variant_size_ids[]" value="">
                                </div>
                            </td>
                            <td>-</td>
                            <td>
                                <input type="text" name="variant_skus[]" class="form-control form-control-sm" placeholder="Auto-generated if empty">
                            </td>
                            <td>
                                <input type="number" name="variant_quantities[]" class="form-control form-control-sm" value="10" min="0">
                            </td>
                        `;
                    }

                    variantCombinationsTable.appendChild(row);
                });
                return;
            }

            // If only sizes are selected
            if (checkedSizes.length > 0 && checkedColors.length === 0) {
                checkedSizes.forEach(sizeCheckbox => {
                    const sizeId = sizeCheckbox.value;
                    const sizeName = sizeCheckbox.dataset.name;

                    const row = document.createElement('tr');

                    if (showPricing) {
                        row.innerHTML = `
                            <td>-</td>
                            <td>
                                ${sizeName}
                                <input type="hidden" name="variant_color_ids[]" value="">
                                <input type="hidden" name="variant_size_ids[]" value="${sizeId}">
                            </td>
                            <td>
                                <input type="text" name="variant_skus[]" class="form-control form-control-sm" placeholder="Auto-generated if empty">
                            </td>
                            <td>
                                <input type="number" name="variant_additional_prices[]" class="form-control form-control-sm" value="0" min="0" step="0.01">
                            </td>
                            <td>
                                <input type="number" name="variant_quantities[]" class="form-control form-control-sm" value="10" min="0">
                            </td>
                        `;
                    } else {
                        row.innerHTML = `
                            <td>-</td>
                            <td>
                                ${sizeName}
                                <input type="hidden" name="variant_color_ids[]" value="">
                                <input type="hidden" name="variant_size_ids[]" value="${sizeId}">
                            </td>
                            <td>
                                <input type="text" name="variant_skus[]" class="form-control form-control-sm" placeholder="Auto-generated if empty">
                            </td>
                            <td>
                                <input type="number" name="variant_quantities[]" class="form-control form-control-sm" value="10" min="0">
                            </td>
                        `;
                    }

                    variantCombinationsTable.appendChild(row);
                });
                return;
            }

            // If both colors and sizes are selected, generate all combinations
            checkedColors.forEach(colorCheckbox => {
                const colorId = colorCheckbox.value;
                const colorName = colorCheckbox.dataset.name;
                const colorCode = colorCheckbox.dataset.color;

                checkedSizes.forEach(sizeCheckbox => {
                    const sizeId = sizeCheckbox.value;
                    const sizeName = sizeCheckbox.dataset.name;

                    const row = document.createElement('tr');

                    if (showPricing) {
                        row.innerHTML = `
                            <td>
                                <div class="d-flex align-items-center">
                                    <div style="width: 20px; height: 20px; background-color: ${colorCode}; border: 1px solid #ddd; border-radius: 4px; margin-right: 8px;"></div>
                                    ${colorName}
                                    <input type="hidden" name="variant_color_ids[]" value="${colorId}">
                                </div>
                            </td>
                            <td>
                                ${sizeName}
                                <input type="hidden" name="variant_size_ids[]" value="${sizeId}">
                            </td>
                            <td>
                                <input type="text" name="variant_skus[]" class="form-control form-control-sm" placeholder="Auto-generated if empty">
                            </td>
                            <td>
                                <input type="number" name="variant_additional_prices[]" class="form-control form-control-sm" value="0" min="0" step="0.01">
                            </td>
                            <td>
                                <input type="number" name="variant_quantities[]" class="form-control form-control-sm" value="10" min="0">
                            </td>
                        `;
                    } else {
                        row.innerHTML = `
                            <td>
                                <div class="d-flex align-items-center">
                                    <div style="width: 20px; height: 20px; background-color: ${colorCode}; border: 1px solid #ddd; border-radius: 4px; margin-right: 8px;"></div>
                                    ${colorName}
                                    <input type="hidden" name="variant_color_ids[]" value="${colorId}">
                                </div>
                            </td>
                            <td>
                                ${sizeName}
                                <input type="hidden" name="variant_size_ids[]" value="${sizeId}">
                            </td>
                            <td>
                                <input type="text" name="variant_skus[]" class="form-control form-control-sm" placeholder="Auto-generated if empty">
                            </td>
                            <td>
                                <input type="number" name="variant_quantities[]" class="form-control form-control-sm" value="10" min="0">
                            </td>
                        `;
                    }

                    variantCombinationsTable.appendChild(row);
                });
            });
        }

        // Initialize selected colors and sizes
        updateSelectedColors();
        updateSelectedSizes();

        // Handle variant image uploads
        document.addEventListener('click', function(e) {
            // Handle upload button clicks
            if (e.target.classList.contains('variant-image-btn') || e.target.closest('.variant-image-btn')) {
                const button = e.target.classList.contains('variant-image-btn') ? e.target : e.target.closest('.variant-image-btn');
                const uploadContainer = button.closest('.variant-image-upload');
                const fileInput = uploadContainer.querySelector('.variant-image-input');

                fileInput.click();
            }

            // Handle remove button clicks
            if (e.target.classList.contains('remove-variant-image') || e.target.closest('.remove-variant-image')) {
                const button = e.target.classList.contains('remove-variant-image') ? e.target : e.target.closest('.remove-variant-image');
                const previewContainer = button.closest('.variant-image-preview');
                const uploadContainer = previewContainer.closest('.variant-image-upload');
                const fileInput = uploadContainer.querySelector('.variant-image-input');

                // Clear the file input
                fileInput.value = '';

                // Hide the preview
                previewContainer.style.display = 'none';
                previewContainer.querySelector('img').src = '';
            }
        });

        // Handle file input changes
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('variant-image-input')) {
                const fileInput = e.target;
                const uploadContainer = fileInput.closest('.variant-image-upload');
                const previewContainer = uploadContainer.querySelector('.variant-image-preview');
                const previewImage = previewContainer.querySelector('img');

                if (fileInput.files && fileInput.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        previewImage.src = e.target.result;
                        previewContainer.style.display = 'block';
                    };

                    reader.readAsDataURL(fileInput.files[0]);
                } else {
                    previewContainer.style.display = 'none';
                    previewImage.src = '';
                }
            }
        });
    }

    // Bulk Pricing Toggle
    const enableBulkPricingCheckbox = document.getElementById('enableBulkPricing');
    const bulkPricingContainer = document.querySelector('.bulk-pricing-container');

    if (bulkPricingContainer) {
        enableBulkPricingCheckbox.addEventListener('change', function() {
            if (this.checked) {
                bulkPricingContainer.style.display = 'block';
            } else {
                bulkPricingContainer.style.display = 'none';
            }
        });

        // Set initial state
        bulkPricingContainer.style.display = enableBulkPricingCheckbox.checked ? 'block' : 'none';

        // Add Bulk Pricing Row
        const addBulkPricingRowBtn = document.getElementById('addBulkPricingRow');
        const bulkPricingTableBody = document.getElementById('bulkPricingTableBody');

        if (addBulkPricingRowBtn && bulkPricingTableBody) {
            addBulkPricingRowBtn.addEventListener('click', function() {
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td>
                        <div class="input-group">
                            <input type="number" name="bulk_min_qty[]" class="form-control" placeholder="Min" min="2">
                            <span class="input-group-text">-</span>
                            <input type="number" name="bulk_max_qty[]" class="form-control" placeholder="Max" min="5">
                        </div>
                    </td>
                    <td>
                        <input type="number" step="0.01" name="bulk_price[]" class="form-control" placeholder="Price per unit">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-bulk-row">Remove</button>
                    </td>
                `;

                bulkPricingTableBody.appendChild(newRow);

                // Add event listener to the new remove button
                newRow.querySelector('.remove-bulk-row').addEventListener('click', function() {
                    this.closest('tr').remove();
                });
            });

            // Remove Bulk Pricing Row
            document.querySelectorAll('.remove-bulk-row').forEach(function(button) {
                button.addEventListener('click', function() {
                    this.closest('tr').remove();
                });
            });
        }
    }

    // Product Visibility Toggle
    const statusSwitch = document.getElementById('statusSwitch');
    const statusText = document.getElementById('statusText');

    statusSwitch.addEventListener('change', function() {
        if (this.checked) {
            statusText.textContent = 'HIDDEN - Not visible to customers';
            statusText.style.color = 'red';
        } else {
            statusText.textContent = 'VISIBLE - Shown to customers';
            statusText.style.color = 'green';
        }
    });
});
</script>

<?php include('../../includes/footer.php'); ?>
