<?php
require '../../config/function.php';

// Create the product_variant_images table if it doesn't exist
$create_variant_images_table_sql = "CREATE TABLE IF NOT EXISTS `product_variant_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) NOT NULL,
  `image` varchar(255) NOT NULL,
  `display_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `color_id` (`color_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

// Execute the query
if(mysqli_query($conn, $create_variant_images_table_sql)) {
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Success!</h3>
        <p>The product_variant_images table was created successfully.</p>
        <p><a href='products-create.php'>Go to Product Creation</a></p>
    </div>";
} else {
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>Failed to create the product_variant_images table.</p>
        <p>MySQL Error: " . mysqli_error($conn) . "</p>
        <p><a href='products-create.php'>Go to Product Creation</a></p>
    </div>";
}
?>
