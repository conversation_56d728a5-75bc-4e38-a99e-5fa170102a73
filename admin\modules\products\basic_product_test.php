<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Create a log file
$log_file = __DIR__ . '/basic_product_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - <PERSON><PERSON><PERSON> started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Basic product data
$category_id = 1; // Use a valid category ID
$name = "Test Product " . date('YmdHis');
$description = "This is a test product created on " . date('Y-m-d H:i:s');
$cost_price = 100.00;
$sales_price = 150.00;
$quantity = 10;
$barcode = "TEST" . rand(1000, 9999);

log_message("Basic product data prepared", [
    'category_id' => $category_id,
    'name' => $name,
    'description' => $description,
    'cost_price' => $cost_price,
    'sales_price' => $sales_price,
    'quantity' => $quantity,
    'barcode' => $barcode
]);

// Get VAT percentage from settings
$vat_query = mysqli_query($conn, "SELECT vat_percentage FROM settings LIMIT 1");
$vat_percentage = 0; // Default value

if($vat_query && mysqli_num_rows($vat_query) > 0) {
    $vat_percentage = mysqli_fetch_assoc($vat_query)['vat_percentage'];
}

// Calculate VAT
$vatT = ($sales_price * $vat_percentage) / 100;
$price = $sales_price + $vatT;

log_message("VAT calculations", [
    'vat_percentage' => $vat_percentage,
    'vatT' => $vatT,
    'price' => $price
]);

// Prepare product data
$data = [
    'category_id' => $category_id,
    'name' => $name,
    'description' => $description,
    'cost_price' => $cost_price,
    'sales_price' => $sales_price,
    'vat_percentage' => $vat_percentage,
    'vatT' => $vatT,
    'price' => $price,
    'quantity' => $quantity,
    'barcode' => $barcode,
    'image' => 'default.jpg', // Default image
    'status' => 0, // Visible
    'created_at' => date('Y-m-d H:i:s'),
    'featured' => 0, // Not featured
    'product_type_id' => 1, // Standard product
    'can_be_component' => 0 // Not a component
];

log_message("Product data prepared", $data);

try {
    // Insert the product
    log_message("Inserting product");

    // Use direct SQL query instead of the insert function
    $columns = implode(", ", array_keys($data));
    $values = "'" . implode("', '", array_values($data)) . "'";

    $sql = "INSERT INTO products ($columns) VALUES ($values)";
    log_message("SQL Query", $sql);

    $result = mysqli_query($conn, $sql);

    if($result) {
        $product_id = mysqli_insert_id($conn);
        log_message("Product inserted successfully with ID: $product_id");

        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Success!</h3>
            <p>The product was created successfully with ID: $product_id</p>
            <p><a href='products.php'>View all products</a></p>
        </div>";
    } else {
        log_message("Product insertion failed: " . mysqli_error($conn));

        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Error!</h3>
            <p>Failed to insert the product into the database.</p>
            <p>MySQL Error: " . mysqli_error($conn) . "</p>
            <p><a href='products-create.php'>Try again</a></p>
        </div>";
    }
} catch (Exception $e) {
    log_message("Exception: " . $e->getMessage());

    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>An exception occurred: " . $e->getMessage() . "</p>
        <p><a href='products-create.php'>Try again</a></p>
    </div>";
} catch (Error $e) {
    log_message("Error: " . $e->getMessage());

    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>An error occurred: " . $e->getMessage() . "</p>
        <p><a href='products-create.php'>Try again</a></p>
    </div>";
}
?>
