<?php
require 'config/function.php';
require 'config/config.php';

if(isset($_POST['loginBtn']))
{
$email = validateCustomer($_POST['email']);
$client_code = validateCustomer($_POST['client_code']);

    if($email != '' && $client_code != '')
    {
        $query = "SELECT * FROM customers WHERE email='$email' AND client_code='$client_code' LIMIT 1";
        $result = mysqli_query($conn, $query);
        if($result){

            if(mysqli_num_rows($result) == 1){

                $row = mysqli_fetch_assoc($result);

                $_SESSION['customerLoggedIn'] = true;
                $_SESSION['customerLoggedInUser'] = [
                    'client_id' => $row['id'],
                    'name' => $row['name'],
                    'email' => $row['email'],
                    'phone' => $row['phone'],
                ];

redirectCustomer('admin/index.php','Logged In Successfully');

            }else{
                redirectCustomer('login.php','Invalid Email Address or Client Code');
            }

        }else{
            redirectCustomer('login.php','Something Went Wrong!');
        }
    }
    else
    {
        redirectCustomer('login.php','All fields are mandatory!');
    }

}
?>