<?php
    require_once(__DIR__ . '/../config/dbcon.php');
    // Ensure BASE_URL is defined
    if (!defined('BASE_URL')) {
        define('BASE_URL', 'http://localhost/webappPatsSEO/');
    }
    $page = substr($_SERVER['SCRIPT_NAME'], strrpos($_SERVER['SCRIPT_NAME'], "/")+1);

    // Get unread counts for sidebar
    // Only calculate if not already defined (to avoid duplicate queries)
    if (!isset($unreadCallBacks)) {
        // Get unread call back requests
        $unreadCallBackQuery = "SELECT COUNT(*) as total FROM request_call_back WHERE read_status = 0";
        $unreadCallBackResult = mysqli_query($conn, $unreadCallBackQuery);
        $unreadCallBacks = 0;
        if ($unreadCallBackResult) {
            $unreadCallBacks = mysqli_fetch_assoc($unreadCallBackResult)['total'];
        }
    }

    if (!isset($unreadServiceInfo)) {
        // Get unread service inquiries
        $unreadServiceInfoQuery = "SELECT COUNT(*) as total FROM services_more_information WHERE read_status = 0";
        $unreadServiceInfoResult = mysqli_query($conn, $unreadServiceInfoQuery);
        $unreadServiceInfo = 0;
        if ($unreadServiceInfoResult) {
            $unreadServiceInfo = mysqli_fetch_assoc($unreadServiceInfoResult)['total'];
        }
    }

    if (!isset($unreadProductInfo)) {
        // Get unread product inquiries
        $unreadProductInfoQuery = "SELECT COUNT(*) as total FROM product_more_information WHERE read_status = 0";
        $unreadProductInfoResult = mysqli_query($conn, $unreadProductInfoQuery);
        $unreadProductInfo = 0;
        if ($unreadProductInfoResult) {
            $unreadProductInfo = mysqli_fetch_assoc($unreadProductInfoResult)['total'];
        }
    }

    if (!isset($unreadQuotations) || !isset($readQuotations)) {
        // Get unread quotation requests
        $unreadQuotationsQuery = "SELECT COUNT(*) as total FROM quotations WHERE read_status = 0";
        $unreadQuotationsResult = mysqli_query($conn, $unreadQuotationsQuery);
        $unreadQuotations = 0;
        if ($unreadQuotationsResult) {
            $unreadQuotations = mysqli_fetch_assoc($unreadQuotationsResult)['total'];
        }

        // Get viewed quotation requests
        $viewedQuotationsQuery = "SELECT COUNT(*) as total FROM quotations WHERE read_status = 1";
        $viewedQuotationsResult = mysqli_query($conn, $viewedQuotationsQuery);
        $viewedQuotations = 0;
        if ($viewedQuotationsResult) {
            $viewedQuotations = mysqli_fetch_assoc($viewedQuotationsResult)['total'];
        }

        // Get total quotation requests
        $totalQuotations = $unreadQuotations + $viewedQuotations;
    }
?>

<div id="layoutSidenav_nav">
    <nav class="sb-sidenav accordion sb-sidenav-dark" id="sidenavAccordion">
        <div class="sb-sidenav-menu">
            <div class="nav">
                <div class="sb-sidenav-menu-heading">Core</div>

                <a class="nav-link <?= $page == 'index.php' ? 'active':''; ?>" href="<?= $BASE_URL ?>/admin/index.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-tachometer-alt"></i></div>
                    Dashboard
                </a>

                <a class="nav-link <?= strpos($page, 'call_back') !== false ? 'active':''; ?>" href="<?= $BASE_URL ?>/admin/modules/call_back/call_back.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-phone-volume"></i></div>
                    Call Back Requests <?= $unreadCallBacks > 0 ? "<span class='badge bg-danger ms-2'>$unreadCallBacks</span>" : ''; ?>
                </a>

                <a class="nav-link <?= strpos($page, 'product_info') !== false ? 'active':''; ?>" href="<?= $BASE_URL ?>/admin/modules/product_info/product_info.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-box-open"></i></div>
                    Product Inquiries <?= $unreadProductInfo > 0 ? "<span class='badge bg-danger ms-2'>$unreadProductInfo</span>" : ''; ?>
                </a>

                <a class="nav-link <?= strpos($page, 'service_info') !== false ? 'active':''; ?>" href="<?= $BASE_URL ?>/admin/modules/service_info/service_info.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-cogs"></i></div>
                    Service Inquiries <?= $unreadServiceInfo > 0 ? "<span class='badge bg-danger ms-2'>$unreadServiceInfo</span>" : ''; ?>
                </a>

                <div class="sb-sidenav-menu-heading">Sales & Orders</div>

                <a class="nav-link <?= strpos($page, 'quotations') !== false ? 'active':''; ?>"
                    href="<?= $BASE_URL ?>/admin/modules/quotations/quotations.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-quote-left"></i></div>
                    Quotations
                    <?php if (isset($unreadQuotations) && $unreadQuotations > 0): ?>
                        <span class='badge bg-danger ms-2' title="Unread quotations"><?= $unreadQuotations ?></span>
                    <?php endif; ?>
                    <?php if (isset($viewedQuotations) && $viewedQuotations > 0): ?>
                        <span class='badge bg-secondary ms-1' title="Viewed quotations"><?= $viewedQuotations ?></span>
                    <?php endif; ?>
                </a>

                <a class="nav-link <?= strpos($page, 'orders') !== false ? 'active':''; ?>"
                    href="<?= $BASE_URL ?>/admin/modules/orders/orders.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-file-invoice"></i></div>
                    Orders
                </a>

                <div class="sb-sidenav-menu-heading">Inventory</div>

                <a class="nav-link <?= strpos($page, 'products') !== false ? 'collapse active':'collapsed'; ?>"
                    href="#" data-bs-toggle="collapse" data-bs-target="#collapseProducts">
                    <div class="sb-nav-link-icon"><i class="fas fa-box"></i></div>
                    Products
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse <?= strpos($page, 'products') !== false ? 'show':''; ?>" id="collapseProducts">
                    <nav class="sb-sidenav-menu-nested nav">
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/products/products.php">View Products</a>
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/products/products-create.php">Add Product</a>
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/products/categories.php">Categories</a>
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/products/criteria.php">Colors & Sizes</a>
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/products/labels.php">Labels</a>
                    </nav>
                </div>

                <a class="nav-link <?= strpos($page, 'services') !== false ? 'collapse active':'collapsed'; ?>"
                    href="#" data-bs-toggle="collapse" data-bs-target="#collapseServices">
                    <div class="sb-nav-link-icon"><i class="fas fa-concierge-bell"></i></div>
                    Services
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse <?= strpos($page, 'services') !== false ? 'show':''; ?>" id="collapseServices">
                    <nav class="sb-sidenav-menu-nested nav">
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/services/services.php">View Services</a>
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/services/services-create.php">Add Service</a>
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/services/services-categories.php">Categories</a>
                    </nav>
                </div>

                <a class="nav-link <?= strpos($page, 'supplier') !== false ? 'collapse active':'collapsed'; ?>"
                    href="#" data-bs-toggle="collapse" data-bs-target="#collapseSuppliers">
                    <div class="sb-nav-link-icon"><i class="fas fa-truck"></i></div>
                    Suppliers
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse <?= strpos($page, 'supplier') !== false ? 'show':''; ?>" id="collapseSuppliers">
                    <nav class="sb-sidenav-menu-nested nav">
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/supplier/suppliers.php">View Suppliers</a>
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/supplier/suppliers-create.php">Add Supplier</a>
                    </nav>
                </div>

                <div class="sb-sidenav-menu-heading">CRM</div>

                <a class="nav-link <?= strpos($page, 'customers') !== false ? 'collapse active':'collapsed'; ?>"
                    href="#" data-bs-toggle="collapse" data-bs-target="#collapseCustomers">
                    <div class="sb-nav-link-icon"><i class="fas fa-users"></i></div>
                    Customers
                    <div class="sb-sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                </a>
                <div class="collapse <?= strpos($page, 'customers') !== false ? 'show':''; ?>" id="collapseCustomers">
                    <nav class="sb-sidenav-menu-nested nav">
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/customers/customers.php">View Customers</a>
                        <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/customers/customers-create.php">Add Customer</a>
                    </nav>
                </div>

                <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/schedule/schedule.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-calendar"></i></div>
                    Schedule
                </a>

                <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/call_log/call-log.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-phone"></i></div>
                    Call Log
                </a>



                <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/contacts/contacts.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-address-book"></i></div>
                    Contacts
                </a>

                <div class="sb-sidenav-menu-heading">Reports & Tools</div>

                <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/reports/report.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-chart-bar"></i></div>
                    Reports
                </a>

                <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/settings/settings.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-cog"></i></div>
                    Settings
                </a>

                <?php if($_SESSION['loggedInUser']['user_type'] === 'admin'): ?>
                <div class="sb-sidenav-menu-heading">Administration</div>

                <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/admins/admins.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-user-shield"></i></div>
                    User Management
                </a>

                <a class="nav-link" href="<?= $BASE_URL ?>/admin/modules/backups_restore/db-backup.php">
                    <div class="sb-nav-link-icon"><i class="fas fa-database"></i></div>
                    Backup & Restore
                </a>
                <?php endif; ?>

            </div>
        </div>
        <div class="sb-sidenav-footer">
            <div class="small">Logged in as: <?= $_SESSION['loggedInUser']['name'] ?? 'User'; ?></div>
            BizBox Business Assistant 2.0
        </div>
    </nav>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all collapse elements in the sidebar
    var collapseElementList = [].slice.call(document.querySelectorAll('.nav-link[data-bs-toggle="collapse"]'))
    collapseElementList.forEach(function(collapseEl) {
        // Make sure Bootstrap is loaded before initializing
        if (typeof bootstrap !== 'undefined') {
            new bootstrap.Collapse(document.querySelector(collapseEl.getAttribute('data-bs-target')), {
                toggle: false
            });
        }
    });
});
</script>
