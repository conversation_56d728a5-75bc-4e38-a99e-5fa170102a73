<?php
require '../../config/function.php';
require '../../config/dbcon.php';

// Check if the table already exists
$tableCheckQuery = "SHOW TABLES LIKE 'product_images'";
$tableCheckResult = mysqli_query($conn, $tableCheckQuery);

if(mysqli_num_rows($tableCheckResult) == 0) {
    // Table doesn't exist, create it
    $createTableQuery = "CREATE TABLE `product_images` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `product_id` int(11) NOT NULL,
        `image` varchar(255) NOT NULL,
        `display_order` int(11) DEFAULT 0,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    if(mysqli_query($conn, $createTableQuery)) {
        echo "Product images table created successfully!";
    } else {
        echo "Error creating product images table: " . mysqli_error($conn);
    }
} else {
    echo "Product images table already exists!";
}
?>
