<?php
// Include database connection
include('admin/config/dbcon.php');

// Get product ID 10 with all its details
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.id = 10";

$result = mysqli_query($conn, $sql);
$product = mysqli_fetch_assoc($result);

echo "<h2>Product Details</h2>";
echo "<pre>";
print_r($product);
echo "</pre>";

// Get variants
$variantsQuery = "SELECT pv.*, pv.additional_price, pc.name as color_name, pc.color_code, ps.name as size_name
                  FROM product_variants pv
                  LEFT JOIN product_colors pc ON pv.color_id = pc.id
                  LEFT JOIN product_sizes ps ON pv.size_id = ps.id
                  WHERE pv.product_id = 10 AND pv.status = 0";
$variantsResult = mysqli_query($conn, $variantsQuery);
$variants = mysqli_fetch_all($variantsResult, MYSQLI_ASSOC);

// Process variants
$colors = [];
$sizes = [];

foreach ($variants as $variant) {
    // Find additional price for this color
    $colorAdditionalPrice = 0;
    if (!empty($variant['color_id'])) {
        foreach ($variants as $v) {
            if ($v['color_id'] == $variant['color_id'] && empty($v['size_id'])) {
                $colorAdditionalPrice = $v['additional_price'];
                break;
            }
        }
        
        // Check if this color is already in the array
        $colorExists = false;
        foreach ($colors as $existingColor) {
            if ($existingColor['id'] == $variant['color_id']) {
                $colorExists = true;
                break;
            }
        }
        
        if (!$colorExists) {
            $colors[] = [
                'id' => $variant['color_id'],
                'name' => $variant['color_name'],
                'color_code' => $variant['color_code'],
                'additional_price' => $colorAdditionalPrice
            ];
        }
    }
    
    // Find additional price for this size
    $sizeAdditionalPrice = 0;
    if (!empty($variant['size_id'])) {
        foreach ($variants as $v) {
            if ($v['size_id'] == $variant['size_id'] && empty($v['color_id'])) {
                $sizeAdditionalPrice = $v['additional_price'];
                break;
            }
        }
        
        // Check if this size is already in the array
        $sizeExists = false;
        foreach ($sizes as $existingSize) {
            if ($existingSize['id'] == $variant['size_id']) {
                $sizeExists = true;
                break;
            }
        }
        
        if (!$sizeExists) {
            $sizes[] = [
                'id' => $variant['size_id'],
                'name' => $variant['size_name'],
                'additional_price' => $sizeAdditionalPrice
            ];
        }
    }
}

echo "<h2>Processed Colors</h2>";
echo "<pre>";
print_r($colors);
echo "</pre>";

echo "<h2>Processed Sizes</h2>";
echo "<pre>";
print_r($sizes);
echo "</pre>";

// Direct approach - get additional prices for each variant
echo "<h2>Direct Approach</h2>";
echo "<pre>";
foreach ($variants as $variant) {
    echo "Color: " . $variant['color_name'] . ", Size: " . $variant['size_name'] . ", Additional Price: " . $variant['additional_price'] . "\n";
}
echo "</pre>";
?>
