<?php include('includes/header.php'); ?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Contacts View
                <a href="contacts.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">

            <?php alertMessage(); ?>

            <form action="code.php" method="POST">

                <?php
                $parmValue = checkParamId('id');
                if(!is_numeric($parmValue)){
                    echo '<h5>'.$parmValue.'</h5>';
                    return false;
                }

                $contacts = getById('contacts',$parmValue);
                if($contacts['status'] == 200)
                {
                ?>

                <input type="hidden" name="contactsId" value="<?= $contacts['data']['id']; ?>">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="">contact_name *</label>
                        <input type="text" name="contact_name" value="<?= $contacts['data']['contact_name']; ?>" disabled readonly class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">contact_number *</label>
                        <input type="text" name="contact_number" value="<?= $contacts['data']['contact_number']; ?>" disabled readonly class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">contact_email *</label>
                        <input type="email" name="date" value="<?= $contacts['data']['contact_email']; ?>" disabled readonly class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">contact_description *</label>
                        <input type="text" name="time" value="<?= $contacts['data']['contact_description']; ?>" disabled readonly class="form-control" />
                    </div>
                </div>
                <?php
                }
                else
                {
                    echo '<h5>'.$category['message'].'</h5>';
                }
                ?>
            </form>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>