<?php
// Include database connection
require_once('admin/config/dbcon.php');

// Get the current product description
$query = "SELECT description FROM products WHERE id = 10";
$result = mysqli_query($conn, $query);
$row = mysqli_fetch_assoc($result);
$current_description = $row['description'];

echo "<h2>Current Description in Database:</h2>";
echo "<pre>" . htmlspecialchars($current_description) . "</pre>";

// The correct description we want to set
$new_description = '<p>colour <i>product</i> <strong>test</strong></p>';

// Update the database
$update_sql = "UPDATE products SET description = ? WHERE id = 10";
$stmt = mysqli_prepare($conn, $update_sql);
mysqli_stmt_bind_param($stmt, "s", $new_description);
$success = mysqli_stmt_execute($stmt);

if($success) {
    echo "<div style='margin: 20px 0; padding: 15px; background-color: #d4edda; color: #155724; border-radius: 4px;'>";
    echo "<h3>✅ Database Updated Successfully!</h3>";
    echo "<p>The description has been updated in the database.</p>";
    echo "</div>";
    
    // Verify the update
    $verify_query = "SELECT description FROM products WHERE id = 10";
    $verify_result = mysqli_query($conn, $verify_query);
    $verify_row = mysqli_fetch_assoc($verify_result);
    $updated_description = $verify_row['description'];
    
    echo "<h2>Updated Description in Database:</h2>";
    echo "<pre>" . htmlspecialchars($updated_description) . "</pre>";
    
    echo "<h2>How it will appear on the website:</h2>";
    echo "<div style='margin: 20px 0; padding: 15px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 4px;'>";
    echo $updated_description;
    echo "</div>";
} else {
    echo "<div style='margin: 20px 0; padding: 15px; background-color: #f8d7da; color: #721c24; border-radius: 4px;'>";
    echo "<h3>❌ Error Updating Database</h3>";
    echo "<p>Error: " . mysqli_error($conn) . "</p>";
    echo "</div>";
}

// Link to go back to the product edit page
echo "<div style='margin-top: 30px;'>";
echo "<a href='admin/modules/products/products-edit.php?id=10' style='padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block;'>Go to Product Edit Page</a>";
echo "</div>";
?>
