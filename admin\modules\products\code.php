<?php
require '../../config/function.php';

// Category Management
if(isset($_POST['saveCategory'])) {
    $name = validate($_POST['name']);
    $description = validate($_POST['description']);
    $parent_id = validate($_POST['parent_id']);
    $status = isset($_POST['status']) ? 1 : 0;

    if($name != '') {
        $data = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        // Add parent_id only if it's not empty
        if($parent_id != '') {
            $data['parent_id'] = $parent_id;
        }

        $result = insert('categories', $data);
        if($result) {
            redirect('categories.php', 'Category Created Successfully!');
        } else {
            redirect('categories-create.php', 'Something Went Wrong!');
        }
    } else {
        redirect('categories-create.php', 'Please fill required fields.');
    }
}

if(isset($_POST['updateCategory'])) {
    $categoryId = validate($_POST['categoryId']);
    $name = validate($_POST['name']);
    $description = validate($_POST['description']);
    $parent_id = validate($_POST['parent_id']);
    $status = isset($_POST['status']) ? 1 : 0;

    if($name != '') {
        $data = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        // Add parent_id only if it's not empty
        if($parent_id != '') {
            $data['parent_id'] = $parent_id;
        } else {
            // If parent_id is empty, set it to NULL
            $data['parent_id'] = NULL;
        }

        $result = update('categories', $categoryId, $data);
        if($result) {
            redirect('categories.php', 'Category Updated Successfully!');
        } else {
            redirect('categories-edit.php?id='.$categoryId, 'Something Went Wrong!');
        }
    } else {
        redirect('categories-edit.php?id='.$categoryId, 'Please fill required fields.');
    }
}

// Product Management
if(isset($_POST['saveProduct']))
{
    try {

    $category_id = validate($_POST['category_id']);
    $name = validate($_POST['name']);
    // Don't use validate() for description as it contains HTML
    $description = $_POST['description'];
    $cost_price = validate($_POST['cost_price']);
    $sales_price = validate($_POST['sales_price']);
    // Get VAT percentage from settings if not provided in the form
    if(isset($_POST['vat_percentage']) && !empty($_POST['vat_percentage'])) {
        $vat_percentage = validate($_POST['vat_percentage']);
    } else {
        // Get from settings table
        $vat_query = mysqli_query($conn, "SELECT vat_percentage FROM settings LIMIT 1");
        $vat_percentage = 0; // Default value

        if($vat_query && mysqli_num_rows($vat_query) > 0) {
            $vat_percentage = mysqli_fetch_assoc($vat_query)['vat_percentage'];
        }
    }

    // Calculate VAT amount
    $vatT = ($sales_price * $vat_percentage) / 100;
    $price = $sales_price + $vatT;
    $quantity = validate($_POST['quantity']);
    $barcode = validate($_POST['barcode']);
    $status = isset($_POST['status']) == true ? 1 : 0;
    $featured = isset($_POST['featured']) == true ? 1 : 0;
    $product_type_id = validate($_POST['product_type_id']) ?: 1; // Default to Standard (1) if not provided
    $can_be_component = isset($_POST['can_be_component']) ? 1 : 0;

    // VAT is already calculated above

    // Handle main image upload
    $finalImage = "default.jpg"; // Default image if none is uploaded
    if(isset($_FILES['image']['name']) && !empty($_FILES['image']['name'])) {
        $upload_path = "../../../uploads/products/";
        $image = $_FILES['image']['name'];

        if($image != "") {
            $finalImage = time() . '_main_' . $image;
            $upload_to = $upload_path . $finalImage;

            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
            }

            move_uploaded_file($_FILES['image']['tmp_name'], $upload_to);
        }
    }

    // Prepare array to store additional image filenames
    $additionalImages = [];

    // Handle discounted price if enabled
    $discounted_price = null;
    if(isset($_POST['enable_discounted_price']) && isset($_POST['discounted_price']) && !empty($_POST['discounted_price'])) {
        $discounted_price = validate($_POST['discounted_price']);
    }

    debug_log('Preparing product data');
    $data = [
        'category_id' => $category_id,
        'name' => $name,
        'description' => $description,
        'cost_price' => $cost_price,
        'sales_price' => $sales_price,
        'vat_percentage' => $vat_percentage,
        'vatT' => $vatT, // Add vatT field
        'price' => $price,
        'discounted_price' => $discounted_price,
        'quantity' => $quantity,
        'barcode' => $barcode,
        'image' => $finalImage,
        'status' => $status,
        'featured' => $featured,
        'product_type_id' => $product_type_id,
        'can_be_component' => $can_be_component
    ];

    debug_log('Inserting product');
    $result = insert('products', $data);
    $product_id = mysqli_insert_id($conn);
    debug_log('Product inserted with ID', $product_id);

    if($result && $product_id) {
        // Handle secondary categories if provided
        if(isset($_POST['secondary_category_ids']) && is_array($_POST['secondary_category_ids'])) {
            foreach($_POST['secondary_category_ids'] as $secondary_category_id) {
                if(!empty($secondary_category_id)) {
                    $secondary_data = [
                        'product_id' => $product_id,
                        'category_id' => validate($secondary_category_id)
                    ];
                    insert('product_secondary_categories', $secondary_data);
                }
            }
        }
        // Handle product variants if enabled
        if(isset($_POST['enable_variants'])) {
            $variant_pricing_type = validate($_POST['variant_pricing_type']);

            // If additional pricing is enabled
            if($variant_pricing_type === 'additional' && isset($_POST['variant_color_ids']) && isset($_POST['variant_size_ids'])) {
                $color_ids = $_POST['variant_color_ids'] ?? [];
                $size_ids = $_POST['variant_size_ids'] ?? [];
                $additional_prices = $_POST['variant_additional_prices'] ?? [];
                $quantities = $_POST['variant_quantities'] ?? [];

                // Calculate total quantity from variants
                $total_variant_quantity = 0;

                // Save each variant
                for($i = 0; $i < count($additional_prices); $i++) {
                    // Ensure we have a valid quantity (default to 1 if empty or invalid)
                    $variant_quantity = isset($quantities[$i]) && !empty($quantities[$i]) ? validate($quantities[$i]) : 1;
                    $total_variant_quantity += $variant_quantity;

                    $variant_data = [
                        'product_id' => $product_id,
                        'color_id' => isset($color_ids[$i]) ? $color_ids[$i] : null,
                        'size_id' => isset($size_ids[$i]) ? $size_ids[$i] : null,
                        'additional_price' => validate($additional_prices[$i]),
                        'quantity' => $variant_quantity,
                        'status' => 0 // Visible by default
                    ];

                    insert('product_variants', $variant_data);
                }

                // Update the main product quantity to match the sum of variant quantities
                if($total_variant_quantity > 0) {
                    $update_quantity = [
                        'quantity' => $total_variant_quantity
                    ];
                    update('products', $update_quantity, $product_id);
                }
            }
            // If using base price for all variants
            else if($variant_pricing_type === 'none') {
                // Check if we have variant quantities
                if(isset($_POST['variant_color_ids']) && isset($_POST['variant_size_ids']) && isset($_POST['variant_quantities'])) {
                    $color_ids = $_POST['variant_color_ids'] ?? [];
                    $size_ids = $_POST['variant_size_ids'] ?? [];
                    $quantities = $_POST['variant_quantities'] ?? [];

                    // Calculate total quantity from variants
                    $total_variant_quantity = 0;

                    // Save each variant with its quantity
                    for($i = 0; $i < count($color_ids); $i++) {
                        // Ensure we have a valid quantity (default to 1 if empty or invalid)
                        $variant_quantity = isset($quantities[$i]) && !empty($quantities[$i]) ? validate($quantities[$i]) : 1;
                        $total_variant_quantity += $variant_quantity;

                        $variant_data = [
                            'product_id' => $product_id,
                            'color_id' => !empty($color_ids[$i]) ? $color_ids[$i] : null,
                            'size_id' => !empty($size_ids[$i]) ? $size_ids[$i] : null,
                            'additional_price' => 0, // No additional price
                            'quantity' => $variant_quantity,
                            'status' => 0 // Visible by default
                        ];

                        insert('product_variants', $variant_data);
                    }

                    // Update the main product quantity to match the sum of variant quantities
                    if($total_variant_quantity > 0) {
                        $update_quantity = [
                            'quantity' => $total_variant_quantity
                        ];
                        update('products', $update_quantity, $product_id);
                    }
                } else {
                    // Fallback to old method if no variant quantities
                    $color_ids = $_POST['variant_colors'] ?? [];
                    $size_ids = $_POST['variant_sizes'] ?? [];

                    // If both colors and sizes are selected, create combinations
                    if(!empty($color_ids) && !empty($size_ids)) {
                        // Calculate quantity per variant
                        $variant_count = count($color_ids) * count($size_ids);
                        $quantity_per_variant = $variant_count > 0 ? ceil($quantity / $variant_count) : $quantity;

                        foreach($color_ids as $color_id) {
                            foreach($size_ids as $size_id) {
                                $variant_data = [
                                    'product_id' => $product_id,
                                    'color_id' => $color_id,
                                    'size_id' => $size_id,
                                    'additional_price' => 0, // No additional price
                                    'quantity' => $quantity_per_variant, // Distribute base quantity evenly
                                    'status' => 0 // Visible by default
                                ];

                                insert('product_variants', $variant_data);
                            }
                        }
                    }
                    // If only colors are selected
                    else if(!empty($color_ids)) {
                        // Calculate quantity per variant
                        $variant_count = count($color_ids);
                        $quantity_per_variant = $variant_count > 0 ? ceil($quantity / $variant_count) : $quantity;

                        foreach($color_ids as $color_id) {
                            $variant_data = [
                                'product_id' => $product_id,
                                'color_id' => $color_id,
                                'size_id' => null,
                                'additional_price' => 0, // No additional price
                                'quantity' => $quantity_per_variant, // Distribute base quantity evenly
                                'status' => 0 // Visible by default
                            ];

                            insert('product_variants', $variant_data);
                        }
                    }
                    // If only sizes are selected
                    else if(!empty($size_ids)) {
                        // Calculate quantity per variant
                        $variant_count = count($size_ids);
                        $quantity_per_variant = $variant_count > 0 ? ceil($quantity / $variant_count) : $quantity;

                        foreach($size_ids as $size_id) {
                            $variant_data = [
                                'product_id' => $product_id,
                                'color_id' => null,
                                'size_id' => $size_id,
                                'additional_price' => 0, // No additional price
                                'quantity' => $quantity_per_variant, // Distribute base quantity evenly
                                'status' => 0 // Visible by default
                            ];

                            insert('product_variants', $variant_data);
                        }
                    }
                }
            }
        }

        // Handle bulk pricing if enabled
        if(isset($_POST['enable_bulk_pricing'])) {
            $min_quantities = $_POST['bulk_min_qty'] ?? [];
            $max_quantities = $_POST['bulk_max_qty'] ?? [];
            $bulk_prices = $_POST['bulk_price'] ?? [];

            // Save each bulk pricing rule
            for($i = 0; $i < count($min_quantities); $i++) {
                if(isset($min_quantities[$i]) && isset($max_quantities[$i]) && isset($bulk_prices[$i])) {
                    $bulk_data = [
                        'product_id' => $product_id,
                        'min_quantity' => validate($min_quantities[$i]),
                        'max_quantity' => validate($max_quantities[$i]),
                        'price' => validate($bulk_prices[$i])
                    ];

                    insert('product_bulk_pricing', $bulk_data);
                }
            }
        }

        // Handle component categories for buildable products
        if($product_type_id == 2 && isset($_POST['component_category_ids'])) {
            $component_category_ids = $_POST['component_category_ids'] ?? [];
            $component_min_required = $_POST['component_min_required'] ?? [];
            $component_max_allowed = $_POST['component_max_allowed'] ?? [];
            $component_is_required = $_POST['component_is_required'] ?? [];
            $component_display_order = $_POST['component_display_order'] ?? [];

            // Save each component category
            for($i = 0; $i < count($component_category_ids); $i++) {
                if(isset($component_category_ids[$i])) {
                    $component_data = [
                        'buildable_product_id' => $product_id,
                        'component_category_id' => validate($component_category_ids[$i]),
                        'min_required' => validate($component_min_required[$i]),
                        'max_allowed' => validate($component_max_allowed[$i]),
                        'is_required' => validate($component_is_required[$i]),
                        'display_order' => validate($component_display_order[$i])
                    ];

                    $component_id = insert('product_components', $component_data);

                    // Add component products if provided
                    if($component_id && isset($_POST['component_products'][$i]) && is_array($_POST['component_products'][$i])) {
                        foreach($_POST['component_products'][$i] as $product_id_to_add) {
                            if(!empty($product_id_to_add)) {
                                $product_component_data = [
                                    'component_id' => $component_id,
                                    'product_id' => validate($product_id_to_add)
                                ];
                                insert('product_component_products', $product_component_data);
                            }
                        }
                    }
                }
            }
        }

        // Handle product label if provided
        if(isset($_POST['product_labels']) && !empty($_POST['product_labels'][0])) {
            $label_id = validate($_POST['product_labels'][0]);
            $label_data = [
                'product_id' => $product_id,
                'label_id' => $label_id
            ];
            insert('product_label_assignments', $label_data);
        }

        // Handle additional images upload
        if(isset($_FILES['additional_images']) && !empty($_FILES['additional_images']['name'][0])) {
            $upload_path = "../../../uploads/products/";

            // Ensure upload directory exists
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
            }

            // Limit to 3 additional images
            $maxAdditionalImages = 3;
            $uploadCount = 0;

            // Loop through each uploaded file (up to 3)
            for($i = 0; $i < count($_FILES['additional_images']['name']) && $uploadCount < $maxAdditionalImages; $i++) {
                $additionalImage = $_FILES['additional_images']['name'][$i];

                if($additionalImage != "") {
                    $finalAdditionalImage = time() . '_additional_' . $i . '_' . $additionalImage;
                    $upload_to = $upload_path . $finalAdditionalImage;

                    if(move_uploaded_file($_FILES['additional_images']['tmp_name'][$i], $upload_to)) {
                        // Insert into product_images table
                        $imageData = [
                            'product_id' => $product_id,
                            'image' => $finalAdditionalImage,
                            'display_order' => $i + 1 // Start from 1
                        ];

                        insert('product_images', $imageData);
                        $uploadCount++;
                    }
                }
            }
        }


        // Handle variant images upload - Simplified approach
        if(isset($_FILES['variant_images']) && is_array($_FILES['variant_images']['name'])) {
            $upload_path = "../../../uploads/products/";

            // Ensure upload directory exists
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
            }

            // Process each color's images
            foreach($_FILES['variant_images']['name'] as $colorId => $files) {
                // Skip if not an array or empty
                if(!is_array($files) || empty($files)) {
                    continue;
                }

                // Process each file for this color
                foreach($files as $index => $filename) {
                    // Skip empty filenames
                    if(empty($filename)) {
                        continue;
                    }

                    // Check if all required file data exists
                    if(!isset($_FILES['variant_images']['tmp_name'][$colorId][$index]) ||
                       !isset($_FILES['variant_images']['type'][$colorId][$index]) ||
                       !isset($_FILES['variant_images']['error'][$colorId][$index])) {
                        continue;
                    }

                    // Get file details
                    $tmp_name = $_FILES['variant_images']['tmp_name'][$colorId][$index];
                    $file_type = $_FILES['variant_images']['type'][$colorId][$index];
                    $file_error = $_FILES['variant_images']['error'][$colorId][$index];

                    // Skip invalid files
                    if($file_error !== 0 || empty($tmp_name) || !file_exists($tmp_name)) {
                        continue;
                    }

                    // Check file type
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                    if(!in_array($file_type, $allowed_types)) {
                        continue;
                    }

                    // Generate a unique filename
                    $finalVariantImage = time() . '_variant_' . $colorId . '_' . $index . '_' . $filename;
                    $upload_to = $upload_path . $finalVariantImage;

                    // Upload the file
                    if(move_uploaded_file($tmp_name, $upload_to)) {
                        // Insert into product_variant_images table
                        $variantImageData = [
                            'product_id' => $product_id,
                            'color_id' => $colorId,
                            'image' => $finalVariantImage,
                            'display_order' => $index + 1
                        ];

                        // Insert the record
                        insert('product_variant_images', $variantImageData);
                    }
                }
            }
        }

        redirect('products.php', 'Product Created Successfully!');
    } else {
        redirect('products-create.php', 'Something Went Wrong!');
    }
    } catch (Exception $e) {
        // Redirect with error message
        redirect('products-create.php', 'Error: ' . $e->getMessage());
    } catch (Error $e) {
        // Redirect with error message
        redirect('products-create.php', 'Error: ' . $e->getMessage());
    }
}

if(isset($_POST['updateProduct']))
{
    $product_id = validate($_POST['product_id']);
    $category_id = validate($_POST['category_id']);
    $name = validate($_POST['name']);
    // Don't use validate() for description as it contains HTML
    $description = $_POST['description'];
    $cost_price = validate($_POST['cost_price']);
    $sales_price = validate($_POST['sales_price']);
    $vat_percentage = validate($_POST['vat_percentage']);
    $vatT = validate($_POST['vatT']) ?: 0; // Get vatT from form or calculate if not provided
    $price = validate($_POST['price']) ?: 0;
    $quantity = validate($_POST['quantity']);
    $barcode = validate($_POST['barcode']);
    $status = isset($_POST['status']) == true ? 1 : 0;
    $product_type_id = validate($_POST['product_type_id']) ?: 1; // Default to Standard (1) if not provided
    $can_be_component = isset($_POST['can_be_component']) ? 1 : 0;

    // Calculate vatT if not provided
    if(empty($vatT) && !empty($sales_price) && !empty($vat_percentage)) {
        $vatT = $sales_price * ($vat_percentage / 100);
    }

    $oldImage = getById('products', $product_id);
    $updateImage = $oldImage['data']['image'];

    if(isset($_FILES['image']['name']) && $_FILES['image']['name'] != '') {
        $upload_path = "../../../uploads/products/";
        $image = $_FILES['image']['name'];

        $updateImage = time() . '_' . $image;
        $upload_to = $upload_path . $updateImage;

        if (!file_exists($upload_path)) {
            mkdir($upload_path, 0777, true);
        }

        if($oldImage['data']['image'] != '' && file_exists($upload_path . $oldImage['data']['image'])) {
            unlink($upload_path . $oldImage['data']['image']);
        }

        move_uploaded_file($_FILES['image']['tmp_name'], $upload_to);
    }

    // Handle discounted price if enabled
    $discounted_price = null;
    if(isset($_POST['enable_discounted_price']) && isset($_POST['discounted_price']) && !empty($_POST['discounted_price'])) {
        $discounted_price = validate($_POST['discounted_price']);
    }

    // Handle variants
    $enable_variants = isset($_POST['enable_variants']) ? 1 : 0;

    // Handle SEO fields
    $seo_title = validate($_POST['seo_title'] ?? '');
    $meta_description = validate($_POST['meta_description'] ?? '');
    $seo_keywords = validate($_POST['seo_keywords'] ?? '');
    $seo_slug = validate($_POST['seo_slug'] ?? '');
    $meta_robots = validate($_POST['meta_robots'] ?? '');
    $canonical_url = validate($_POST['canonical_url'] ?? '');

    $data = [
        'category_id' => $category_id,
        'name' => $name,
        'description' => $description,
        'cost_price' => $cost_price,
        'sales_price' => $sales_price,
        'vat_percentage' => $vat_percentage,
        'vatT' => $vatT,
        'price' => $price,
        'discounted_price' => $discounted_price,
        'quantity' => $quantity,
        'barcode' => $barcode,
        'image' => $updateImage,
        'status' => $status,
        'product_type_id' => $product_type_id,
        'can_be_component' => $can_be_component,
        'enable_variants' => $enable_variants,
        'seo_title' => $seo_title,
        'meta_description' => $meta_description,
        'seo_keywords' => $seo_keywords,
        'seo_slug' => $seo_slug,
        'meta_robots' => $meta_robots,
        'canonical_url' => $canonical_url
    ];

    $result = update('products', $product_id, $data);

    if($result) {
        // Handle secondary categories
        // First, delete existing secondary categories
        $delete_secondary = mysqli_prepare($conn, "DELETE FROM product_secondary_categories WHERE product_id = ?");
        mysqli_stmt_bind_param($delete_secondary, "i", $product_id);
        mysqli_stmt_execute($delete_secondary);

        // Then add new secondary categories if provided
        if(isset($_POST['secondary_category_ids']) && is_array($_POST['secondary_category_ids'])) {
            foreach($_POST['secondary_category_ids'] as $secondary_category_id) {
                if(!empty($secondary_category_id)) {
                    $secondary_data = [
                        'product_id' => $product_id,
                        'category_id' => validate($secondary_category_id)
                    ];
                    insert('product_secondary_categories', $secondary_data);
                }
            }
        }
        // Handle product variants if enabled
        // NOTE: Variant management is now handled separately in product-variants.php
        // This code is commented out to prevent deletion of existing variants
        /*
        if(isset($_POST['enable_variants'])) {
            // Delete existing variants
            require_once('product_variant_functions.php');
            deleteProductVariants($product_id);

            $variant_pricing_type = validate($_POST['variant_pricing_type']);

            // If additional pricing is enabled
            if($variant_pricing_type === 'additional' && isset($_POST['variant_color_ids']) && isset($_POST['variant_size_ids'])) {
                $color_ids = $_POST['variant_color_ids'] ?? [];
                $size_ids = $_POST['variant_size_ids'] ?? [];
                $additional_prices = $_POST['variant_additional_prices'] ?? [];
                $quantities = $_POST['variant_quantities'] ?? [];

                // Save each variant
                for($i = 0; $i < count($additional_prices); $i++) {
                    $variant_data = [
                        'product_id' => $product_id,
                        'color_id' => isset($color_ids[$i]) ? $color_ids[$i] : null,
                        'size_id' => isset($size_ids[$i]) ? $size_ids[$i] : null,
                        'additional_price' => validate($additional_prices[$i]),
                        'quantity' => validate($quantities[$i]),
                        'status' => 0 // Visible by default
                    ];

                    insert('product_variants', $variant_data);
                }
            }
            // If using base price for all variants
            else if($variant_pricing_type === 'none') {
                $color_ids = $_POST['variant_colors'] ?? [];
                $size_ids = $_POST['variant_sizes'] ?? [];

                // If both colors and sizes are selected, create combinations
                if(!empty($color_ids) && !empty($size_ids)) {
                    foreach($color_ids as $color_id) {
                        foreach($size_ids as $size_id) {
                            $variant_data = [
                                'product_id' => $product_id,
                                'color_id' => $color_id,
                                'size_id' => $size_id,
                                'additional_price' => 0, // No additional price
                                'quantity' => $quantity, // Use base product quantity
                                'status' => 0 // Visible by default
                            ];

                            insert('product_variants', $variant_data);
                        }
                    }
                }
                // If only colors are selected
                else if(!empty($color_ids)) {
                    foreach($color_ids as $color_id) {
                        $variant_data = [
                            'product_id' => $product_id,
                            'color_id' => $color_id,
                            'size_id' => null,
                            'additional_price' => 0, // No additional price
                            'quantity' => $quantity, // Use base product quantity
                            'status' => 0 // Visible by default
                        ];

                        insert('product_variants', $variant_data);
                    }
                }
                // If only sizes are selected
                else if(!empty($size_ids)) {
                    foreach($size_ids as $size_id) {
                        $variant_data = [
                            'product_id' => $product_id,
                            'color_id' => null,
                            'size_id' => $size_id,
                            'additional_price' => 0, // No additional price
                            'quantity' => $quantity, // Use base product quantity
                            'status' => 0 // Visible by default
                        ];

                        insert('product_variants', $variant_data);
                    }
                }
            }
        } else {
            // If variants are disabled, delete any existing variants
            require_once('product_variant_functions.php');
            deleteProductVariants($product_id);
        }
        */
        // END OF COMMENTED VARIANT HANDLING CODE

        // Handle bulk pricing if enabled
        if(isset($_POST['enable_bulk_pricing'])) {
            // Delete existing bulk pricing rules
            require_once('product_variant_functions.php');
            deleteProductBulkPricing($product_id);

            $min_quantities = $_POST['bulk_min_qty'] ?? [];
            $max_quantities = $_POST['bulk_max_qty'] ?? [];
            $bulk_prices = $_POST['bulk_price'] ?? [];

            // Save each bulk pricing rule
            for($i = 0; $i < count($min_quantities); $i++) {
                if(isset($min_quantities[$i]) && isset($max_quantities[$i]) && isset($bulk_prices[$i])) {
                    $bulk_data = [
                        'product_id' => $product_id,
                        'min_quantity' => validate($min_quantities[$i]),
                        'max_quantity' => validate($max_quantities[$i]),
                        'price' => validate($bulk_prices[$i])
                    ];

                    insert('product_bulk_pricing', $bulk_data);
                }
            }
        } else {
            // If bulk pricing is disabled, delete any existing bulk pricing rules
            require_once('product_variant_functions.php');
            deleteProductBulkPricing($product_id);
        }

        // Handle component categories for buildable products
        if($product_type_id == 2) {
            // First, delete existing component categories
            $delete_components = mysqli_prepare($conn, "DELETE FROM product_components WHERE buildable_product_id = ?");
            mysqli_stmt_bind_param($delete_components, "i", $product_id);
            mysqli_stmt_execute($delete_components);

            // Then add new component categories if provided
            if(isset($_POST['component_category_ids'])) {
                $component_category_ids = $_POST['component_category_ids'] ?? [];
                $component_min_required = $_POST['component_min_required'] ?? [];
                $component_max_allowed = $_POST['component_max_allowed'] ?? [];
                $component_is_required = $_POST['component_is_required'] ?? [];
                $component_display_order = $_POST['component_display_order'] ?? [];

                // Save each component category
                for($i = 0; $i < count($component_category_ids); $i++) {
                    if(isset($component_category_ids[$i])) {
                        $component_data = [
                            'buildable_product_id' => $product_id,
                            'component_category_id' => validate($component_category_ids[$i]),
                            'min_required' => validate($component_min_required[$i]),
                            'max_allowed' => validate($component_max_allowed[$i]),
                            'is_required' => validate($component_is_required[$i]),
                            'display_order' => validate($component_display_order[$i])
                        ];

                        $component_id = insert('product_components', $component_data);

                        // Add component products if provided
                        // Check for products in the POST data
                        if($component_id) {
                            // For existing components (from edit form)
                            if(isset($_POST['component_products']) && is_array($_POST['component_products'])) {
                                // Check if we have products for this component
                                foreach($_POST['component_products'] as $comp_id => $products) {
                                    // This could be a new component or an existing one with a different ID
                                    // We'll add products from any array we find
                                    if(is_array($products)) {
                                        foreach($products as $product_id_to_add) {
                                            if(!empty($product_id_to_add)) {
                                                $product_component_data = [
                                                    'component_id' => $component_id,
                                                    'product_id' => validate($product_id_to_add)
                                                ];
                                                insert('product_component_products', $product_component_data);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            // If not a buildable product, delete any existing component categories
            $delete_components = mysqli_prepare($conn, "DELETE FROM product_components WHERE buildable_product_id = ?");
            mysqli_stmt_bind_param($delete_components, "i", $product_id);
            mysqli_stmt_execute($delete_components);
        }

        // Handle product label
        // First, delete existing label assignments
        $delete_labels = mysqli_prepare($conn, "DELETE FROM product_label_assignments WHERE product_id = ?");
        mysqli_stmt_bind_param($delete_labels, "i", $product_id);
        mysqli_stmt_execute($delete_labels);

        // Then add new label assignment if provided
        if(isset($_POST['product_labels']) && !empty($_POST['product_labels'][0])) {
            $label_id = validate($_POST['product_labels'][0]);
            $label_data = [
                'product_id' => $product_id,
                'label_id' => $label_id
            ];
            insert('product_label_assignments', $label_data);
        }

        // Handle additional images upload
        if(isset($_FILES['additional_images']) && !empty($_FILES['additional_images']['name'][0])) {
            $upload_path = "../../../uploads/products/";

            // Ensure upload directory exists
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
            }

            // Limit to 3 additional images
            $maxAdditionalImages = 3;
            $uploadCount = 0;

            // Get current count of additional images
            $current_images_query = mysqli_query($conn, "SELECT COUNT(*) as count FROM product_images WHERE product_id = '$product_id'");
            $current_count = 0;
            if($current_images_query) {
                $current_count = mysqli_fetch_assoc($current_images_query)['count'];
            }

            // Calculate how many more images we can add
            $remaining_slots = $maxAdditionalImages - $current_count;

            if($remaining_slots > 0) {
                // Loop through each uploaded file (up to remaining slots)
                for($i = 0; $i < count($_FILES['additional_images']['name']) && $uploadCount < $remaining_slots; $i++) {
                    $additionalImage = $_FILES['additional_images']['name'][$i];

                    if($additionalImage != "") {
                        $finalAdditionalImage = time() . '_additional_' . $i . '_' . $additionalImage;
                        $upload_to = $upload_path . $finalAdditionalImage;

                        if(move_uploaded_file($_FILES['additional_images']['tmp_name'][$i], $upload_to)) {
                            // Get the next display order
                            $order_query = mysqli_query($conn, "SELECT MAX(display_order) as max_order FROM product_images WHERE product_id = '$product_id'");
                            $next_order = 1;
                            if($order_query && $order_row = mysqli_fetch_assoc($order_query)) {
                                $next_order = ($order_row['max_order'] ?? 0) + 1;
                            }

                            // Insert into product_images table
                            $imageData = [
                                'product_id' => $product_id,
                                'image' => $finalAdditionalImage,
                                'display_order' => $next_order
                            ];

                            insert('product_images', $imageData);
                            $uploadCount++;
                        }
                    }
                }
            }
        }

        // Handle variant images upload
        if(isset($_FILES['variant_images'])) {
            $upload_path = "../../../uploads/products/";

            // Ensure upload directory exists
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
            }

            // Loop through each color's images
            foreach($_FILES['variant_images']['name'] as $colorId => $files) {
                if(is_array($files)) {
                    foreach($files as $index => $filename) {
                        if(!empty($filename)) {
                            // Get the uploaded file details
                            $tmp_name = $_FILES['variant_images']['tmp_name'][$colorId][$index];
                            $file_type = $_FILES['variant_images']['type'][$colorId][$index];
                            $file_size = $_FILES['variant_images']['size'][$colorId][$index];
                            $file_error = $_FILES['variant_images']['error'][$colorId][$index];

                            // Check if it's a valid image
                            if($file_error === 0 && in_array($file_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])) {
                                // Generate a unique filename
                                $finalVariantImage = time() . '_variant_' . $colorId . '_' . $index . '_' . $filename;
                                $upload_to = $upload_path . $finalVariantImage;

                                if(move_uploaded_file($tmp_name, $upload_to)) {
                                    // Insert into product_variant_images table
                                    $variantImageData = [
                                        'product_id' => $product_id,
                                        'color_id' => $colorId,
                                        'image' => $finalVariantImage,
                                        'display_order' => $index + 1
                                    ];

                                    // Insert the variant image
                                    insert('product_variant_images', $variantImageData);
                                }
                            }
                        }
                    }
                }
            }
        }

        redirect('products.php', 'Product Updated Successfully!');
    } else {
        redirect('products-edit.php?id='.$product_id, 'Something Went Wrong!');
    }
}

// Delete Product Image
if(isset($_GET['action']) && $_GET['action'] == 'delete_product_image' && isset($_GET['id']) && isset($_GET['product_id'])) {
    $image_id = validate($_GET['id']);
    $product_id = validate($_GET['product_id']);

    // Get the image filename
    $image_query = mysqli_query($conn, "SELECT * FROM product_images WHERE id = '$image_id' AND product_id = '$product_id'");
    if($image_query && mysqli_num_rows($image_query) > 0) {
        $image_data = mysqli_fetch_assoc($image_query);
        $image_filename = $image_data['image'];

        // Delete the image file
        $image_path = "../../../uploads/products/" . $image_filename;
        if(file_exists($image_path)) {
            unlink($image_path);
        }

        // Delete the database record
        $delete_query = mysqli_query($conn, "DELETE FROM product_images WHERE id = '$image_id'");
        if($delete_query) {
            redirect('products-edit.php?id='.$product_id, 'Product image deleted successfully!');
        } else {
            redirect('products-edit.php?id='.$product_id, 'Failed to delete product image!');
        }
    } else {
        redirect('products-edit.php?id='.$product_id, 'Image not found!');
    }
}

// Featured Products Management
if(isset($_POST['saveUpdateFeatured']))
{
    $featured = isset($_POST['featured']) ? $_POST['featured'] : array();

    // Debug information
    error_log("Featured products: " . print_r($featured, true));

    mysqli_begin_transaction($conn);

    try {
        // First, set all products as not featured
        $stmt1 = mysqli_prepare($conn, "UPDATE products SET featured = 0");
        mysqli_stmt_execute($stmt1);

        if (!empty($featured)) {
            // Then, set selected products as featured
            foreach ($featured as $productId) {
                $stmt2 = mysqli_prepare($conn, "UPDATE products SET featured = 1 WHERE id = ?");
                mysqli_stmt_bind_param($stmt2, "i", $productId);
                mysqli_stmt_execute($stmt2);
            }
        }

        mysqli_commit($conn);
        redirect('products.php', 'Featured Products Updated Successfully!');
    } catch (Exception $e) {
        mysqli_rollback($conn);
        error_log("Error updating featured products: " . $e->getMessage());
        redirect('products.php', 'Something Went Wrong: ' . $e->getMessage());
    }
}

// Product Label Management
if(isset($_POST['saveLabel'])) {
    $name = validate($_POST['name']);
    $background_color = validate($_POST['background_color']);
    $text_color = validate($_POST['text_color']);
    $status = isset($_POST['status']) ? 1 : 0;

    if($name != '') {
        $data = [
            'name' => $name,
            'background_color' => $background_color,
            'text_color' => $text_color,
            'status' => $status
        ];

        $result = insert('product_labels', $data);
        if($result) {
            redirect('labels.php', 'Label Created Successfully!');
        } else {
            redirect('labels-create.php', 'Something Went Wrong!');
        }
    } else {
        redirect('labels-create.php', 'Please fill required fields.');
    }
}

if(isset($_POST['updateLabel'])) {
    $labelId = validate($_POST['labelId']);
    $name = validate($_POST['name']);
    $background_color = validate($_POST['background_color']);
    $text_color = validate($_POST['text_color']);
    $status = isset($_POST['status']) ? 1 : 0;

    if($name != '') {
        $data = [
            'name' => $name,
            'background_color' => $background_color,
            'text_color' => $text_color,
            'status' => $status
        ];

        $result = update('product_labels', $labelId, $data);
        if($result) {
            redirect('labels.php', 'Label Updated Successfully!');
        } else {
            redirect('labels-edit.php?id='.$labelId, 'Something Went Wrong!');
        }
    } else {
        redirect('labels-edit.php?id='.$labelId, 'Please fill required fields.');
    }
}

// Delete Label (AJAX handler)
if(isset($_POST['action']) && $_POST['action'] == 'delete_label') {
    $id = validate($_POST['label_id']);

    // Check if label exists
    $check_label = mysqli_query($conn, "SELECT id FROM product_labels WHERE id='$id'");
    if(mysqli_num_rows($check_label) > 0) {
        // Delete label
        $delete_query = "DELETE FROM product_labels WHERE id='$id'";
        $delete_query_run = mysqli_query($conn, $delete_query);

        if($delete_query_run) {
            echo json_encode(['status' => 200, 'message' => 'Label Deleted Successfully']);
        } else {
            echo json_encode(['status' => 500, 'message' => 'Something Went Wrong']);
        }
    } else {
        echo json_encode(['status' => 404, 'message' => 'Label Not Found']);
    }
    exit();
}

// Delete Product (AJAX handler)
if(isset($_POST['action']) && $_POST['action'] == 'delete_product') {
    $productId = validate($_POST['product_id']);
    $product = getById('products', $productId);

    if($product['status'] == 200) {
        // Delete image if exists
        if($product['data']['image'] != '') {
            $imagePath = "../../../uploads/products/" . $product['data']['image'];
            if(file_exists($imagePath)) {
                unlink($imagePath);
            }
        }

        $result = delete('products', $productId);
        echo json_encode(['status' => $result ? 'success' : 'error']);
        exit;
    }

    echo json_encode(['status' => 'error']);
    exit;
}

// Product Criteria - Colors
if(isset($_POST['saveColor'])) {
    $name = validate($_POST['name']);
    $color_code = validate($_POST['color_code']);
    $description = validate($_POST['description']);
    $status = isset($_POST['status']) ? 1 : 0;

    // Add # to color code if not present
    if(!empty($color_code) && substr($color_code, 0, 1) !== '#') {
        $color_code = '#' . $color_code;
    }

    if($name != '') {
        $data = [
            'name' => $name,
            'color_code' => $color_code,
            'description' => $description,
            'status' => $status
        ];

        $result = insert('product_colors', $data);
        if($result) {
            redirect('criteria.php?tab=colors', 'Color Created Successfully!');
        } else {
            redirect('criteria-create.php?type=color', 'Something Went Wrong!');
        }
    } else {
        redirect('criteria-create.php?type=color', 'Please fill required fields.');
    }
}

if(isset($_POST['updateColor'])) {
    $id = validate($_POST['id']);
    $name = validate($_POST['name']);
    $color_code = validate($_POST['color_code']);
    $description = validate($_POST['description']);
    $status = isset($_POST['status']) ? 1 : 0;

    // Add # to color code if not present
    if(!empty($color_code) && substr($color_code, 0, 1) !== '#') {
        $color_code = '#' . $color_code;
    }

    if($name != '') {
        $data = [
            'name' => $name,
            'color_code' => $color_code,
            'description' => $description,
            'status' => $status
        ];

        $result = update('product_colors', $id, $data);
        if($result) {
            redirect('criteria.php?tab=colors', 'Color Updated Successfully!');
        } else {
            redirect('criteria-edit.php?type=color&id='.$id, 'Something Went Wrong!');
        }
    } else {
        redirect('criteria-edit.php?type=color&id='.$id, 'Please fill required fields.');
    }
}

// Product Criteria - Sizes
if(isset($_POST['saveSize'])) {
    $name = validate($_POST['name']);
    $description = validate($_POST['description']);
    $status = isset($_POST['status']) ? 1 : 0;

    if($name != '') {
        $data = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        $result = insert('product_sizes', $data);
        if($result) {
            redirect('criteria.php?tab=sizes', 'Size Created Successfully!');
        } else {
            redirect('criteria-create.php?type=size', 'Something Went Wrong!');
        }
    } else {
        redirect('criteria-create.php?type=size', 'Please fill required fields.');
    }
}

if(isset($_POST['updateSize'])) {
    $id = validate($_POST['id']);
    $name = validate($_POST['name']);
    $description = validate($_POST['description']);
    $status = isset($_POST['status']) ? 1 : 0;

    if($name != '') {
        $data = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        $result = update('product_sizes', $id, $data);
        if($result) {
            redirect('criteria.php?tab=sizes', 'Size Updated Successfully!');
        } else {
            redirect('criteria-edit.php?type=size&id='.$id, 'Something Went Wrong!');
        }
    } else {
        redirect('criteria-edit.php?type=size&id='.$id, 'Please fill required fields.');
    }
}

// Delete criteria (color or size)
if(isset($_GET['action']) && $_GET['action'] == 'delete') {
    $type = validate($_GET['type']);
    $id = validate($_GET['id']);

    if($type == 'color') {
        $tableName = 'product_colors';
        $redirectUrl = 'criteria.php?tab=colors';
        $successMsg = 'Color Deleted Successfully!';
    } elseif($type == 'size') {
        $tableName = 'product_sizes';
        $redirectUrl = 'criteria.php?tab=sizes';
        $successMsg = 'Size Deleted Successfully!';
    } else {
        redirect('criteria.php', 'Invalid request');
        exit;
    }

    $result = delete($tableName, $id);
    if($result) {
        redirect($redirectUrl, $successMsg);
    } else {
        redirect($redirectUrl, 'Something Went Wrong!');
    }
}
?>
