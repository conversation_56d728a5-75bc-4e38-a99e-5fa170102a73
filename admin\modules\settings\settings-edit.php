<?php include('includes/header.php'); ?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Settings
                <a href="settings.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">

            <?php alertMessage(); ?>

            <form action="code.php" method="POST">

                <?php
                    $paramValue = checkParamId('id');
                    if(!is_numeric($paramValue)){
                        echo '<h5>'.$paramValue.'</h5>';
                        return false;
                    }

                    $settings = getById('settings', $paramValue);
                    if($settings['status'] == 200)
                    {
                        ?>

                <input type="hidden" name="settingsId" value="<?= $settings['data']['id']; ?>" />

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="">Company Name *</label>
                        <input type="text" name="companyname" required value="<?= $settings['data']['companyname']; ?>"
                            class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Contact Number</label>
                        <input type="text" name="contactnumber" required
                            value="<?= $settings['data']['contactnumber']; ?>" class="form-control" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <label for="">Address </label>
                        <input type="text" name="address" required value="<?= $settings['data']['address']; ?>"
                            class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Vat Registration # </label>
                        <input type="text" name="vat_reg" value="<?= $settings['data']['vat_reg']; ?>"
                            class="form-control" />
                    </div>
                    <div class="col-md-2 mb-2">
                        <label for="">Vat Percentage % </label>
                        <input type="text" name="vat_percentage" value="<?= $settings['data']['vat_percentage']; ?>"
                            class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3 text-end">
                        <label for="">Bank</label>
                        <input type="text" name="bank" value="<?= $settings['data']['bank']; ?>"
                            class="form-control" />
                    </div>
                    <div class="col-md-3 mb-3 text-end">
                        <label for="">Acc Name</label>
                        <input type="text" name="bank_acc_name" value="<?= $settings['data']['bank_acc_name']; ?>"
                            class="form-control" />
                    </div>
                    <div class="col-md-3 mb-3 text-end">
                        <label for="">Acc Type</label>
                        <input type="text" name="bank_acc_type" value="<?= $settings['data']['bank_acc_type']; ?>"
                            class="form-control" />
                    </div>
                    <div class="col-md-3 mb-3 text-end">
                        <label for="">Acc No</label>
                        <input type="text" name="bank_acc_no" value="<?= $settings['data']['bank_acc_no']; ?>"
                            class="form-control" />
                    </div>
                    <div class="col-md-3 mb-3 text-end">
                        <label for="">Branch Code</label>
                        <input type="text" name="bank_branch_code" value="<?= $settings['data']['bank_branch_code']; ?>"
                            class="form-control" />
                    </div>
                    <br />
                    <div>
                    <button type="submit" name="updateSettings" class="btn btn-primary">Update</button>
                </div>
        </div>
        <?php
                    }
                    else
                    {
                        echo '<h5>'.$settings['message'].'</h5>';
                        return false;
                    }
                ?>


        </form>
    </div>
</div>
</div>

<?php include('includes/footer.php'); ?>