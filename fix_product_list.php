<?php
// Include database connection
include('admin/config/dbcon.php');

// Get product ID 10 with all its details
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.id = 10";

$result = mysqli_query($conn, $sql);
$product = mysqli_fetch_assoc($result);

// Get variants
$variantsQuery = "SELECT pv.*, pc.name as color_name, pc.color_code, ps.name as size_name
                  FROM product_variants pv
                  LEFT JOIN product_colors pc ON pv.color_id = pc.id
                  LEFT JOIN product_sizes ps ON pv.size_id = ps.id
                  WHERE pv.product_id = 10 AND pv.status = 0";
$variantsResult = mysqli_query($conn, $variantsQuery);
$variants = mysqli_fetch_all($variantsResult, MYSQLI_ASSOC);

// Process variants - IMPROVED APPROACH
$colors = [];
$sizes = [];
$variantMap = []; // To store price for each color-size combination

// First, collect all unique colors and sizes
foreach ($variants as $variant) {
    // Store the variant in our map for quick lookup
    $variantMap[$variant['color_id'] . '_' . $variant['size_id']] = $variant;
    
    if (!empty($variant['color_id'])) {
        $colorExists = false;
        foreach ($colors as $existingColor) {
            if ($existingColor['id'] == $variant['color_id']) {
                $colorExists = true;
                break;
            }
        }
        
        if (!$colorExists) {
            $colors[] = [
                'id' => $variant['color_id'],
                'name' => $variant['color_name'],
                'color_code' => $variant['color_code']
            ];
        }
    }
    
    if (!empty($variant['size_id'])) {
        $sizeExists = false;
        foreach ($sizes as $existingSize) {
            if ($existingSize['id'] == $variant['size_id']) {
                $sizeExists = true;
                break;
            }
        }
        
        if (!$sizeExists) {
            $sizes[] = [
                'id' => $variant['size_id'],
                'name' => $variant['size_name']
            ];
        }
    }
}

// Now, find the additional price for each color with the smallest size
foreach ($colors as &$color) {
    $minAdditionalPrice = PHP_FLOAT_MAX;
    
    foreach ($sizes as $size) {
        $key = $color['id'] . '_' . $size['id'];
        if (isset($variantMap[$key])) {
            $variant = $variantMap[$key];
            if ($variant['additional_price'] < $minAdditionalPrice) {
                $minAdditionalPrice = $variant['additional_price'];
            }
        }
    }
    
    $color['additional_price'] = ($minAdditionalPrice != PHP_FLOAT_MAX) ? $minAdditionalPrice : 0;
}

// Find the additional price for each size with the smallest color price
foreach ($sizes as &$size) {
    $minAdditionalPrice = PHP_FLOAT_MAX;
    
    foreach ($colors as $color) {
        $key = $color['id'] . '_' . $size['id'];
        if (isset($variantMap[$key])) {
            $variant = $variantMap[$key];
            if ($variant['additional_price'] < $minAdditionalPrice) {
                $minAdditionalPrice = $variant['additional_price'];
            }
        }
    }
    
    $size['additional_price'] = ($minAdditionalPrice != PHP_FLOAT_MAX) ? $minAdditionalPrice : 0;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Product List</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .color-option, .size-option {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .color-swatch {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 1px solid #ddd;
            transition: all 0.2s ease;
        }
        .size-box {
            min-width: 50px;
            height: 50px;
            border: 1px solid #ddd;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.2s ease;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Fixed Product List for Product ID 10</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title"><?= htmlspecialchars($product['name']) ?></h2>
                        <p><strong>Category:</strong> <?= htmlspecialchars($product['category_name']) ?></p>
                        <h3 id="product-price">R <?= number_format($product['sales_price'], 2) ?></h3>
                        
                        <!-- Variant details will be displayed here -->
                        <div id="variant-details" class="mt-2"></div>
                        
                        <div class="product-description mb-4">
                            <?= html_entity_decode($product['description']) ?>
                        </div>
                        
                        <div class="product-variants-section mt-4">
                            <h4 class="mb-3">Available Options</h4>
                            
                            <?php if(!empty($colors)): ?>
                            <div class="mb-3">
                                <label class="form-label">Colors:</label>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php foreach($colors as $color): ?>
                                        <div class="color-option" data-color-id="<?= $color['id'] ?>" data-additional-price="<?= $color['additional_price'] ?>">
                                            <div class="color-swatch" style="background-color: <?= $color['color_code'] ?>;"
                                                 title="<?= htmlspecialchars($color['name']) ?><?= $color['additional_price'] > 0 ? ' (+R '.number_format($color['additional_price'], 2).')' : '' ?>"></div>
                                            <small class="d-block mt-1 text-center">
                                                <?= htmlspecialchars($color['name']) ?>
                                                <?php if($color['additional_price'] > 0): ?>
                                                    <span class="text-danger">(+R <?= number_format($color['additional_price'], 2) ?>)</span>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if(!empty($sizes)): ?>
                            <div class="mb-3">
                                <label class="form-label">Sizes:</label>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php foreach($sizes as $size): ?>
                                        <div class="size-option" data-size-id="<?= $size['id'] ?>" data-additional-price="<?= $size['additional_price'] ?>">
                                            <div class="size-box">
                                                <span><?= htmlspecialchars($size['name']) ?></span>
                                                <?php if($size['additional_price'] > 0): ?>
                                                    <span class="text-danger" style="font-size: 10px; font-weight: normal;">+R <?= number_format($size['additional_price'], 2) ?>)</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mt-4">
                            <button type="button" class="btn btn-danger" id="add-to-quote">
                                <i class="bi bi-cart-plus"></i> Add with Selected Options
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h2>Debug Information</h2>
                
                <h3>Colors with Additional Prices</h3>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Color</th>
                            <th>Additional Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($colors as $color): ?>
                            <tr>
                                <td><?= htmlspecialchars($color['name']) ?></td>
                                <td>R <?= number_format($color['additional_price'], 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <h3>Sizes with Additional Prices</h3>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Size</th>
                            <th>Additional Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sizes as $size): ?>
                            <tr>
                                <td><?= htmlspecialchars($size['name']) ?></td>
                                <td>R <?= number_format($size['additional_price'], 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <h3>All Variants</h3>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Color</th>
                            <th>Size</th>
                            <th>Additional Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($variants as $variant): ?>
                            <tr>
                                <td><?= htmlspecialchars($variant['color_name']) ?></td>
                                <td><?= htmlspecialchars($variant['size_name']) ?></td>
                                <td>R <?= number_format($variant['additional_price'], 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script>
        // Global variables to store selected variants
        let selectedColorId = null;
        let selectedSizeId = null;
        let selectedColorPrice = 0;
        let selectedSizePrice = 0;
        let basePrice = <?= $product['sales_price'] ?>;
        let variantMap = <?= json_encode($variantMap) ?>;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Color selection
            const colorOptions = document.querySelectorAll('.color-option');
            colorOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove active class from all color options
                    colorOptions.forEach(opt => {
                        opt.querySelector('.color-swatch').style.boxShadow = 'none';
                        opt.querySelector('.color-swatch').style.transform = 'scale(1)';
                    });
                    
                    // Add active class to selected color option
                    this.querySelector('.color-swatch').style.boxShadow = '0 0 0 3px rgba(220,53,69,.5)';
                    this.querySelector('.color-swatch').style.transform = 'scale(1.1)';
                    
                    // Store selected color ID and price
                    selectedColorId = this.getAttribute('data-color-id');
                    selectedColorPrice = parseFloat(this.getAttribute('data-additional-price') || 0);
                    
                    // Update displayed price
                    updateDisplayPrice();
                });
            });
            
            // Size selection
            const sizeOptions = document.querySelectorAll('.size-option');
            sizeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove active class from all size options
                    sizeOptions.forEach(opt => {
                        opt.querySelector('.size-box').style.boxShadow = 'none';
                        opt.querySelector('.size-box').style.backgroundColor = '#f8f9fa';
                        opt.querySelector('.size-box').style.transform = 'scale(1)';
                    });
                    
                    // Add active class to selected size option
                    this.querySelector('.size-box').style.boxShadow = '0 0 0 3px rgba(220,53,69,.5)';
                    this.querySelector('.size-box').style.backgroundColor = '#f8d7da';
                    this.querySelector('.size-box').style.transform = 'scale(1.05)';
                    
                    // Store selected size ID and price
                    selectedSizeId = this.getAttribute('data-size-id');
                    selectedSizePrice = parseFloat(this.getAttribute('data-additional-price') || 0);
                    
                    // Update displayed price
                    updateDisplayPrice();
                });
            });
        });
        
        // Function to update the displayed price based on selected variants
        function updateDisplayPrice() {
            // Check if we have both color and size selected
            if (selectedColorId && selectedSizeId) {
                // Look up the exact variant price
                const variantKey = selectedColorId + '_' + selectedSizeId;
                const variant = variantMap[variantKey];
                
                if (variant) {
                    // Use the exact variant price
                    const totalPrice = basePrice + parseFloat(variant.additional_price);
                    
                    // Format the price
                    const formattedPrice = 'R ' + totalPrice.toFixed(2);
                    
                    // Update the price display
                    const priceElement = document.getElementById('product-price');
                    if (priceElement) {
                        priceElement.textContent = formattedPrice;
                    }
                    
                    // Show variant details
                    let variantDetails = '';
                    
                    if (parseFloat(variant.additional_price) > 0) {
                        variantDetails += '<div class="alert alert-info mb-3">';
                        variantDetails += '<p class="mb-1"><strong>Selected Options:</strong></p>';
                        variantDetails += '<p class="mb-1">Color: ' + variant.color_name + '</p>';
                        variantDetails += '<p class="mb-1">Size: ' + variant.size_name + '</p>';
                        variantDetails += '<p class="mb-1">Additional Price: +R ' + parseFloat(variant.additional_price).toFixed(2) + '</p>';
                        variantDetails += '<p class="mb-0"><strong>Total: R ' + totalPrice.toFixed(2) + '</strong></p>';
                        variantDetails += '</div>';
                    }
                    
                    // Update variant details display
                    const variantDetailsElement = document.getElementById('variant-details');
                    if (variantDetailsElement) {
                        variantDetailsElement.innerHTML = variantDetails;
                    }
                }
            } else {
                // Use individual color and size prices
                const totalPrice = basePrice + selectedColorPrice + selectedSizePrice;
                
                // Format the price
                const formattedPrice = 'R ' + totalPrice.toFixed(2);
                
                // Update the price display
                const priceElement = document.getElementById('product-price');
                if (priceElement) {
                    priceElement.textContent = formattedPrice;
                }
                
                // Show variant details
                let variantDetails = '';
                
                if (selectedColorPrice > 0 || selectedSizePrice > 0) {
                    variantDetails += '<div class="alert alert-info mb-3">';
                    variantDetails += '<p class="mb-1"><strong>Selected Options:</strong></p>';
                    
                    if (selectedColorPrice > 0) {
                        variantDetails += '<p class="mb-1">Color: +R ' + selectedColorPrice.toFixed(2) + '</p>';
                    }
                    
                    if (selectedSizePrice > 0) {
                        variantDetails += '<p class="mb-1">Size: +R ' + selectedSizePrice.toFixed(2) + '</p>';
                    }
                    
                    variantDetails += '<p class="mb-0"><strong>Total: R ' + totalPrice.toFixed(2) + '</strong></p>';
                    variantDetails += '</div>';
                }
                
                // Update variant details display
                const variantDetailsElement = document.getElementById('variant-details');
                if (variantDetailsElement) {
                    variantDetailsElement.innerHTML = variantDetails;
                }
            }
        }
    </script>
</body>
</html>
