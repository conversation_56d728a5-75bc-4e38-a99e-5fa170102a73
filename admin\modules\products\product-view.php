<?php
require '../../config/function.php';
require '../../config/dbcon.php';
include('../../includes/header.php');
require_once('product_variant_functions.php');

// Check authentication
if(!isset($_SESSION['loggedIn'])) {
    redirect('../../auth/login.php', 'Login to continue...');
}

// Get product ID from URL
$productId = isset($_GET['id']) ? validate($_GET['id']) : null;

if(!$productId) {
    redirect('products.php', 'Invalid product ID');
    exit();
}

// Fetch product details
function getProductDetails($productId) {
    global $conn;

    $query = "SELECT p.*, c.name as category_name
              FROM products p
              LEFT JOIN categories c ON p.category_id = c.id
              WHERE p.id = ?";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "i", $productId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if(mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }

    return false;
}

$product = getProductDetails($productId);

if(!$product) {
    redirect('products.php', 'Product not found');
    exit();
}

// Get product variants and bulk pricing
$variants = getProductVariants($productId);
$bulkPricing = getProductBulkPricing($productId);
?>

<style>
    .product-description {
        padding: 15px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .product-description h1,
    .product-description h2,
    .product-description h3,
    .product-description h4 {
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .product-description p {
        margin-bottom: 1rem;
        line-height: 1.6;
    }

    .product-description ul,
    .product-description ol {
        margin-bottom: 1rem;
        padding-left: 2rem;
    }

    .product-description img {
        max-width: 100%;
        height: auto;
        margin: 1rem 0;
    }
</style>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Product Details
                <a href="products.php" class="btn btn-primary float-end">
                    <i class="fas fa-arrow-left"></i> Back to Products
                </a>
            </h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <?php if(!empty($product['image']) && file_exists("../../../uploads/products/{$product['image']}")) : ?>
                        <img src="../../../uploads/products/<?= $product['image'] ?>"
                             class="img-fluid rounded shadow"
                             style="max-height: 300px; object-fit: contain;"
                             alt="<?= htmlspecialchars($product['name']) ?>">
                    <?php else : ?>
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 300px;">
                            <span class="text-muted">No Image Available</span>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-8">
                    <h2 class="mb-3"><?= htmlspecialchars($product['name']) ?></h2>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Product Information</h5>
                                    <table class="table table-borderless mb-0">
                                        <tr>
                                            <td><strong>Category:</strong></td>
                                            <td><?= htmlspecialchars($product['category_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Barcode:</strong></td>
                                            <td><?= htmlspecialchars($product['barcode']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <?php if($product['status'] == 0): ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-eye"></i> Visible
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-eye-slash"></i> Hidden
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Featured:</strong></td>
                                            <td>
                                                <?php if($product['featured'] == 1): ?>
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-star"></i> Featured
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="far fa-star"></i> Not Featured
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">Pricing Information</h5>
                                    <table class="table table-borderless mb-0">
                                        <tr>
                                            <td><strong>Cost Price:</strong></td>
                                            <td>R <?= number_format($product['cost_price'], 2) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Sales Price:</strong></td>
                                            <td>R <?= number_format($product['sales_price'], 2) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>VAT (<?= $product['vat_percentage'] ?>%):</strong></td>
                                            <td>
                                                <?php
                                                $calculated_vat = ($product['sales_price'] * $product['vat_percentage']) / 100;
                                                ?>
                                                R <?= number_format($calculated_vat, 2) ?>
                                            </td>
                                        </tr>
                                        <?php if(!empty($product['discounted_price']) && $product['discounted_price'] > 0): ?>
                                        <tr>
                                            <td><strong>Discounted Price:</strong></td>
                                            <td class="text-success fw-bold">
                                                R <?= number_format($product['discounted_price'], 2) ?>
                                                <small class="text-muted">(Original: R <?= number_format($product['sales_price'], 2) ?>)</small>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Final Price (with VAT):</strong></td>
                                            <td class="fw-bold">
                                                <?php
                                                $discounted_final_price = $product['discounted_price'] + (($product['discounted_price'] * $product['vat_percentage']) / 100);
                                                ?>
                                                R <?= number_format($discounted_final_price, 2) ?>
                                            </td>
                                        </tr>
                                        <?php else: ?>
                                        <tr>
                                            <td><strong>Final Price:</strong></td>
                                            <td class="fw-bold">
                                                <?php
                                                $final_price = $product['sales_price'] + $calculated_vat;
                                                ?>
                                                R <?= number_format($final_price, 2) ?>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Inventory</h5>
                            <div class="d-flex align-items-center">
                                <div class="me-4">
                                    <span class="fs-1 fw-bold"><?= $product['quantity'] ?></span>
                                    <span class="text-muted">units in stock</span>
                                </div>

                                <?php if($product['quantity'] <= 5): ?>
                                    <div class="alert alert-warning mb-0">
                                        <i class="fas fa-exclamation-triangle"></i> Low stock alert
                                    </div>
                                <?php elseif($product['quantity'] > 20): ?>
                                    <div class="alert alert-success mb-0">
                                        <i class="fas fa-check-circle"></i> Good stock level
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle"></i> Moderate stock level
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card bg-light mt-4">
                <div class="card-body">
                    <h5 class="card-title">Product Description</h5>
                    <div class="product-description">
                        <?php echo html_entity_decode($product['description']); ?>
                    </div>
                </div>
            </div>

            <?php if($variants): ?>
            <div class="card bg-light mt-4">
                <div class="card-body">
                    <h5 class="card-title">Product Variants</h5>
                    <p class="text-muted">This product has <?= count($variants) ?> variant(s) available.</p>

                    <!-- Variant Images Section -->
                    <?php
                    // Get variant images
                    $variant_images_query = "SELECT pvi.*, pc.name as color_name, pc.color_code
                                           FROM product_variant_images pvi
                                           LEFT JOIN product_colors pc ON pvi.color_id = pc.id
                                           WHERE pvi.product_id = ?
                                           ORDER BY pvi.color_id, pvi.display_order";
                    $stmt = mysqli_prepare($conn, $variant_images_query);
                    mysqli_stmt_bind_param($stmt, "i", $productId);
                    mysqli_stmt_execute($stmt);
                    $variant_images_result = mysqli_stmt_get_result($stmt);
                    $variant_images = mysqli_fetch_all($variant_images_result, MYSQLI_ASSOC);

                    if($variant_images): ?>
                    <div class="mb-4">
                        <h6>Variant Images</h6>
                        <div class="row">
                            <?php
                            $grouped_images = [];
                            foreach($variant_images as $img) {
                                $grouped_images[$img['color_id']][] = $img;
                            }

                            foreach($grouped_images as $color_id => $images): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card">
                                    <div class="card-header bg-primary text-white py-2">
                                        <div class="d-flex align-items-center">
                                            <?php if(!empty($images[0]['color_code'])): ?>
                                                <span style="display: inline-block; width: 20px; height: 20px; background-color: <?= $images[0]['color_code'] ?>; border: 1px solid #fff; border-radius: 4px; margin-right: 8px;"></span>
                                            <?php endif; ?>
                                            <small><?= htmlspecialchars($images[0]['color_name']) ?></small>
                                        </div>
                                    </div>
                                    <div class="card-body p-2">
                                        <div class="row g-1">
                                            <?php foreach($images as $img): ?>
                                            <div class="col-6">
                                                <?php if(file_exists("../../../uploads/products/{$img['image']}")): ?>
                                                    <img src="../../../uploads/products/<?= $img['image'] ?>"
                                                         class="img-fluid rounded"
                                                         style="height: 80px; width: 100%; object-fit: cover; cursor: pointer;"
                                                         onclick="showImageModal('<?= $img['image'] ?>', '<?= htmlspecialchars($images[0]['color_name']) ?>')"
                                                         alt="<?= htmlspecialchars($images[0]['color_name']) ?> variant">
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 80px;">
                                                        <small class="text-muted">Image not found</small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th>SKU</th>
                                    <th>Color</th>
                                    <th>Size</th>
                                    <th>Additional Price</th>
                                    <th>Total Price</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($variants as $variant): ?>
                                <tr>
                                    <td>
                                        <code><?= htmlspecialchars($variant['sku'] ?? 'N/A') ?></code>
                                    </td>
                                    <td>
                                        <?php if(!empty($variant['color_name'])): ?>
                                            <div class="d-flex align-items-center">
                                                <?php if(!empty($variant['color_code'])): ?>
                                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: <?= $variant['color_code'] ?>; border: 1px solid #ddd; border-radius: 4px; margin-right: 5px;"></span>
                                                <?php endif; ?>
                                                <?= htmlspecialchars($variant['color_name']) ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if(!empty($variant['size_name'])): ?>
                                            <?= htmlspecialchars($variant['size_name']) ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($variant['additional_price'] > 0): ?>
                                            <span class="text-success">+R <?= number_format($variant['additional_price'], 2) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $basePrice = $product['vat_percentage'] > 0 ? $product['price'] : $product['sales_price'];
                                        $totalPrice = $basePrice + $variant['additional_price'];
                                        ?>
                                        <strong>R <?= number_format($totalPrice, 2) ?></strong>
                                    </td>
                                    <td>
                                        <?= $variant['quantity'] ?>
                                        <?php if($variant['quantity'] <= 5): ?>
                                            <span class="badge bg-warning text-dark">Low Stock</span>
                                        <?php elseif($variant['quantity'] == 0): ?>
                                            <span class="badge bg-danger">Out of Stock</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($variant['status'] == 0): ?>
                                            <span class="badge bg-success">Visible</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Hidden</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if($bulkPricing): ?>
            <div class="card bg-light mt-4">
                <div class="card-body">
                    <h5 class="card-title">Bulk Pricing</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th>Quantity Range</th>
                                    <th>Price per Unit</th>
                                    <th>Discount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $basePrice = $product['sales_price'];
                                foreach($bulkPricing as $pricing):
                                    $discount = $basePrice - $pricing['price'];
                                    $discountPercentage = ($discount / $basePrice) * 100;
                                ?>
                                <tr>
                                    <td>
                                        <?php if($pricing['min_quantity'] == $pricing['max_quantity']): ?>
                                            <?= $pricing['min_quantity'] ?>
                                        <?php else: ?>
                                            <?= $pricing['min_quantity'] ?> - <?= $pricing['max_quantity'] ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>R <?= number_format($pricing['price'], 2) ?></td>
                                    <td>
                                        <?php if($discount > 0): ?>
                                            <span class="text-success">
                                                Save R <?= number_format($discount, 2) ?> (<?= number_format($discountPercentage, 1) ?>%)
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- SEO Information -->
            <?php if(!empty($product['seo_title']) || !empty($product['meta_description']) || !empty($product['seo_keywords']) || !empty($product['seo_slug'])): ?>
            <div class="card bg-light mt-4">
                <div class="card-body">
                    <h5 class="card-title">SEO Information</h5>
                    <div class="row">
                        <?php if(!empty($product['seo_title'])): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">SEO Title:</label>
                            <p class="mb-0"><?= htmlspecialchars($product['seo_title']) ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if(!empty($product['seo_slug'])): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">SEO Slug:</label>
                            <p class="mb-0"><code><?= htmlspecialchars($product['seo_slug']) ?></code></p>
                        </div>
                        <?php endif; ?>

                        <?php if(!empty($product['meta_description'])): ?>
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">Meta Description:</label>
                            <p class="mb-0"><?= htmlspecialchars($product['meta_description']) ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if(!empty($product['seo_keywords'])): ?>
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">SEO Keywords:</label>
                            <p class="mb-0"><?= htmlspecialchars($product['seo_keywords']) ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if(!empty($product['meta_robots'])): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Meta Robots:</label>
                            <p class="mb-0"><span class="badge bg-info"><?= htmlspecialchars($product['meta_robots']) ?></span></p>
                        </div>
                        <?php endif; ?>

                        <?php if(!empty($product['canonical_url'])): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Canonical URL:</label>
                            <p class="mb-0"><a href="<?= htmlspecialchars($product['canonical_url']) ?>" target="_blank"><?= htmlspecialchars($product['canonical_url']) ?></a></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <div class="mt-4">
                <a href="products-edit.php?id=<?= $product['id'] ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit Product
                </a>
                <a href="products.php" class="btn btn-secondary">
                    <i class="fas fa-list"></i> Back to Products List
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Variant Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="Variant Image">
            </div>
        </div>
    </div>
</div>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Function to show image in modal
function showImageModal(imageName, colorName) {
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('imageModalLabel');

    modalImage.src = '../../../uploads/products/' + imageName;
    modalTitle.textContent = colorName + ' - Variant Image';

    modal.show();
}
</script>

<?php include('../../includes/footer.php'); ?>
