<?php
// Include database connection
require '../../config/function.php';

// Output any errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Checking product_variant_images table</h1>";

// Check if the table exists
$table_check = mysqli_query($conn, "SHOW TABLES LIKE 'product_variant_images'");
if(mysqli_num_rows($table_check) > 0) {
    echo "<p>Table exists!</p>";
    
    // Get table structure
    $structure = mysqli_query($conn, "DESCRIBE product_variant_images");
    if($structure) {
        echo "<h2>Table Structure:</h2>";
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while($row = mysqli_fetch_assoc($structure)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Error getting table structure: " . mysqli_error($conn) . "</p>";
    }
    
    // Check for any records
    $records = mysqli_query($conn, "SELECT COUNT(*) as count FROM product_variant_images");
    if($records) {
        $count = mysqli_fetch_assoc($records)['count'];
        echo "<p>Number of records: " . $count . "</p>";
    } else {
        echo "<p>Error counting records: " . mysqli_error($conn) . "</p>";
    }
} else {
    echo "<p>Table does not exist!</p>";
    
    // Create the table
    echo "<h2>Creating table...</h2>";
    
    $create_table = "CREATE TABLE `product_variant_images` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `product_id` int(11) NOT NULL,
      `color_id` int(11) NOT NULL,
      `image` varchar(255) NOT NULL,
      `display_order` int(11) DEFAULT 0,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `product_id` (`product_id`),
      KEY `color_id` (`color_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    if(mysqli_query($conn, $create_table)) {
        echo "<p>Table created successfully!</p>";
    } else {
        echo "<p>Error creating table: " . mysqli_error($conn) . "</p>";
    }
}

// Check the insert function
echo "<h2>Testing insert function</h2>";

function test_insert() {
    global $conn;
    
    // Get a sample product ID
    $product_query = mysqli_query($conn, "SELECT id FROM products LIMIT 1");
    if($product_query && mysqli_num_rows($product_query) > 0) {
        $product_id = mysqli_fetch_assoc($product_query)['id'];
        
        // Get a sample color ID
        $color_query = mysqli_query($conn, "SELECT id FROM product_colors LIMIT 1");
        if($color_query && mysqli_num_rows($color_query) > 0) {
            $color_id = mysqli_fetch_assoc($color_query)['id'];
            
            // Test data
            $test_data = [
                'product_id' => $product_id,
                'color_id' => $color_id,
                'image' => 'test_image.jpg',
                'display_order' => 1
            ];
            
            echo "<p>Test data: " . json_encode($test_data) . "</p>";
            
            // Try to insert
            try {
                $result = insert('product_variant_images', $test_data);
                echo "<p>Insert result: " . ($result ? "Success" : "Failed") . "</p>";
            } catch (Exception $e) {
                echo "<p>Exception during insert: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>No colors found for testing</p>";
        }
    } else {
        echo "<p>No products found for testing</p>";
    }
}

test_insert();
?>
