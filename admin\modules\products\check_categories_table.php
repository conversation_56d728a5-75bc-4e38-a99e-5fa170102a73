<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Check the structure of the categories table
$query = "DESCRIBE categories";
$result = mysqli_query($conn, $query);

if ($result) {
    echo "<h2>Categories Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Show the data in the categories table
    $data_query = "SELECT * FROM categories";
    $data_result = mysqli_query($conn, $data_query);
    
    if ($data_result) {
        echo "<h2>Categories Table Data</h2>";
        echo "<table border='1'>";
        
        // Get column names
        $fields = mysqli_fetch_fields($data_result);
        echo "<tr>";
        foreach ($fields as $field) {
            echo "<th>" . $field->name . "</th>";
        }
        echo "</tr>";
        
        // Get data
        while ($row = mysqli_fetch_assoc($data_result)) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . $value . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Error fetching data: " . mysqli_error($conn) . "</p>";
    }
} else {
    echo "<p>Error: " . mysqli_error($conn) . "</p>";
}
?>
