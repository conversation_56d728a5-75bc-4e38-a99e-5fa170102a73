<?php
// Include database connection
require '../../config/function.php';

// Output any errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Create a log file
$log_file = __DIR__ . '/simple_product_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - Test started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Display a form for testing
if (!isset($_POST['test_submit'])) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Product Test</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    </head>
    <body>
        <div class="container mt-5">
            <h1>Simple Product Test</h1>

            <form action="" method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="category_id" class="form-label">Category ID</label>
                    <input type="number" class="form-control" id="category_id" name="category_id" value="1" required>
                </div>

                <div class="mb-3">
                    <label for="name" class="form-label">Product Name</label>
                    <input type="text" class="form-control" id="name" name="name" value="Test Product" required>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="3">Test Description</textarea>
                </div>

                <div class="mb-3">
                    <label for="cost_price" class="form-label">Cost Price</label>
                    <input type="number" class="form-control" id="cost_price" name="cost_price" value="100" step="0.01" required>
                </div>

                <div class="mb-3">
                    <label for="sales_price" class="form-label">Sales Price</label>
                    <input type="number" class="form-control" id="sales_price" name="sales_price" value="150" step="0.01" required>
                </div>

                <div class="mb-3">
                    <label for="quantity" class="form-label">Quantity</label>
                    <input type="number" class="form-control" id="quantity" name="quantity" value="10" required>
                </div>

                <div class="mb-3">
                    <label for="image" class="form-label">Product Image</label>
                    <input type="file" class="form-control" id="image" name="image">
                </div>

                <button type="submit" name="test_submit" class="btn btn-primary">Test Product Creation</button>
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Process the form submission
try {
    log_message("Form submitted");

    // Check if this is the main product form or our test form
    if(isset($_POST['test_submit'])) {
        log_message("Test form submitted");
    } else if(isset($_POST['saveProduct'])) {
        log_message("Main product form detected");

        // Add the saveProduct field for processing
        $_POST['test_submit'] = true;

        // Get form data
        $category_id = validate($_POST['category_id']);
        $name = validate($_POST['name']);
        $description = $_POST['description']; // Don't validate HTML content
        $cost_price = validate($_POST['cost_price']);
        $sales_price = validate($_POST['sales_price']);
        $quantity = validate($_POST['quantity']);

        log_message("Basic product data", [
            'category_id' => $category_id,
            'name' => $name,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'quantity' => $quantity
        ]);

        // Get VAT percentage from settings
        $vat_query = mysqli_query($conn, "SELECT value FROM settings WHERE name='vat_percentage'");
        $vat_percentage = 15; // Default value

        if($vat_query && mysqli_num_rows($vat_query) > 0) {
            $vat_percentage = mysqli_fetch_assoc($vat_query)['value'];
        }

        // Calculate VAT
        $vatT = ($sales_price * $vat_percentage) / 100;
        $price = $sales_price + $vatT;

        log_message("Price calculations", [
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price
        ]);

        // Handle image upload
        $finalImage = "";
        if (isset($_FILES['image']) && !empty($_FILES['image']['name'])) {
            log_message("Processing image upload", $_FILES['image']);

            $upload_path = "../../../uploads/products/";

            // Ensure upload directory exists
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
                log_message("Created upload directory");
            }

            // Get file details
            $filename = $_FILES['image']['name'];
            $tmp_name = $_FILES['image']['tmp_name'];
            $file_type = $_FILES['image']['type'];
            $file_error = $_FILES['image']['error'];

            // Check if it's a valid image
            if ($file_error === 0 && in_array($file_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])) {
                log_message("Valid image type");

                // Generate a unique filename
                $finalImage = time() . '_' . $filename;
                $upload_to = $upload_path . $finalImage;

                log_message("Uploading to: $upload_to");

                // Upload the file
                if (move_uploaded_file($tmp_name, $upload_to)) {
                    log_message("File uploaded successfully");
                } else {
                    log_message("Failed to move uploaded file");
                    $finalImage = ""; // Reset if upload failed
                }
            } else {
                log_message("Invalid image type or file error");
            }
        }

        // Prepare product data
        $product_data = [
            'category_id' => $category_id,
            'name' => $name,
            'description' => $description,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price,
            'quantity' => $quantity,
            'barcode' => isset($_POST['barcode']) ? validate($_POST['barcode']) : '', // Empty barcode
            'image' => $finalImage,
            'status' => isset($_POST['status']) ? 1 : 0, // Visible by default
            'featured' => isset($_POST['featured']) ? 1 : 0, // Not featured by default
            'product_type_id' => isset($_POST['product_type_id']) ? validate($_POST['product_type_id']) : 1, // Standard product
            'can_be_component' => isset($_POST['can_be_component']) ? 1 : 0 // Not a component
        ];

        log_message("Product data prepared", $product_data);

        // Insert the product
        log_message("Inserting product");
        $result = insert('products', $product_data);

        if ($result) {
            $product_id = mysqli_insert_id($conn);
            log_message("Product inserted successfully with ID: $product_id");

            echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
                <h3>Success!</h3>
                <p>The product was created successfully with ID: $product_id</p>
                <p><a href='simple_product_test.php'>Create another product</a></p>
                <p><a href='products.php'>View all products</a></p>
            </div>";
        } else {
            log_message("Product insertion failed");
            echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
                <h3>Error!</h3>
                <p>Failed to insert the product into the database.</p>
                <p>MySQL Error: " . mysqli_error($conn) . "</p>
                <p><a href='simple_product_test.php'>Try again</a></p>
            </div>";
        }
    }
} catch (Exception $e) {
    log_message("Exception: " . $e->getMessage());
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>An exception occurred: " . $e->getMessage() . "</p>
        <p><a href='simple_product_test.php'>Try again</a></p>
    </div>";
} catch (Error $e) {
    log_message("Error: " . $e->getMessage());
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>An error occurred: " . $e->getMessage() . "</p>
        <p><a href='simple_product_test.php'>Try again</a></p>
    </div>";
}
?>
