<?php
require '../../config/function.php';

// Check authentication
if(!isset($_SESSION['loggedIn'])) {
    redirect('../../auth/login.php', 'Login to continue...');
}

if($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect('products.php', 'Invalid request method');
}

$product_id = validate($_POST['product_id']);
$color_id = validate($_POST['color_id']);

// Validate inputs
if(empty($product_id) || empty($color_id)) {
    redirect("variant-images.php?id=$product_id", 'Invalid product or color ID');
}

// Handle image upload
if(isset($_POST['upload_image'])) {
    if(!isset($_FILES['variant_image']) || $_FILES['variant_image']['error'] !== 0) {
        redirect("variant-images.php?id=$product_id", 'Please select a valid image file');
    }

    $file = $_FILES['variant_image'];
    $upload_path = "../../../uploads/products/";

    // Validate file type
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if(!in_array($file['type'], $allowed_types)) {
        redirect("variant-images.php?id=$product_id", 'Invalid file type. Please upload JPEG, PNG, GIF, or WebP images only.');
    }

    // Validate file size (max 5MB)
    if($file['size'] > 5 * 1024 * 1024) {
        redirect("variant-images.php?id=$product_id", 'File size too large. Maximum size is 5MB.');
    }

    // Create upload directory if it doesn't exist
    if(!file_exists($upload_path)) {
        mkdir($upload_path, 0777, true);
    }

    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $new_filename = time() . '_variant_' . $product_id . '_' . $color_id . '.' . $file_extension;
    $upload_to = $upload_path . $new_filename;

    // Upload the file
    if(move_uploaded_file($file['tmp_name'], $upload_to)) {
        // Delete existing image if it exists
        $existing_query = "SELECT image FROM product_variant_images WHERE product_id = ? AND color_id = ?";
        $existing_stmt = mysqli_prepare($conn, $existing_query);
        mysqli_stmt_bind_param($existing_stmt, "ii", $product_id, $color_id);
        mysqli_stmt_execute($existing_stmt);
        $existing_result = mysqli_stmt_get_result($existing_stmt);
        
        if($existing_result && mysqli_num_rows($existing_result) > 0) {
            $existing_image = mysqli_fetch_assoc($existing_result);
            $old_file = $upload_path . $existing_image['image'];
            if(file_exists($old_file)) {
                unlink($old_file);
            }
            
            // Update existing record
            $update_query = "UPDATE product_variant_images SET image = ?, created_at = NOW() WHERE product_id = ? AND color_id = ?";
            $update_stmt = mysqli_prepare($conn, $update_query);
            mysqli_stmt_bind_param($update_stmt, "sii", $new_filename, $product_id, $color_id);
            
            if(mysqli_stmt_execute($update_stmt)) {
                redirect("variant-images.php?id=$product_id", 'Variant image updated successfully!');
            } else {
                // Delete uploaded file if database update fails
                unlink($upload_to);
                redirect("variant-images.php?id=$product_id", 'Failed to update image in database');
            }
        } else {
            // Insert new record
            $insert_query = "INSERT INTO product_variant_images (product_id, color_id, image, display_order) VALUES (?, ?, ?, 1)";
            $insert_stmt = mysqli_prepare($conn, $insert_query);
            mysqli_stmt_bind_param($insert_stmt, "iis", $product_id, $color_id, $new_filename);
            
            if(mysqli_stmt_execute($insert_stmt)) {
                redirect("variant-images.php?id=$product_id", 'Variant image uploaded successfully!');
            } else {
                // Delete uploaded file if database insert fails
                unlink($upload_to);
                redirect("variant-images.php?id=$product_id", 'Failed to save image to database');
            }
        }
    } else {
        redirect("variant-images.php?id=$product_id", 'Failed to upload image file');
    }
}

// Handle image deletion
if(isset($_POST['delete_image'])) {
    // Get existing image info
    $existing_query = "SELECT image FROM product_variant_images WHERE product_id = ? AND color_id = ?";
    $existing_stmt = mysqli_prepare($conn, $existing_query);
    mysqli_stmt_bind_param($existing_stmt, "ii", $product_id, $color_id);
    mysqli_stmt_execute($existing_stmt);
    $existing_result = mysqli_stmt_get_result($existing_stmt);
    
    if($existing_result && mysqli_num_rows($existing_result) > 0) {
        $existing_image = mysqli_fetch_assoc($existing_result);
        
        // Delete from database
        $delete_query = "DELETE FROM product_variant_images WHERE product_id = ? AND color_id = ?";
        $delete_stmt = mysqli_prepare($conn, $delete_query);
        mysqli_stmt_bind_param($delete_stmt, "ii", $product_id, $color_id);
        
        if(mysqli_stmt_execute($delete_stmt)) {
            // Delete physical file
            $file_path = "../../../uploads/products/" . $existing_image['image'];
            if(file_exists($file_path)) {
                unlink($file_path);
            }
            redirect("variant-images.php?id=$product_id", 'Variant image deleted successfully!');
        } else {
            redirect("variant-images.php?id=$product_id", 'Failed to delete image from database');
        }
    } else {
        redirect("variant-images.php?id=$product_id", 'Image not found');
    }
}

// If we get here, something went wrong
redirect("variant-images.php?id=$product_id", 'Invalid action');
?>
