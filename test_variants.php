<?php
// Include database connection
include('admin/config/dbcon.php');

// Get product ID 10 with all its details
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.id = 10";

$result = mysqli_query($conn, $sql);
$product = mysqli_fetch_assoc($result);

// Get variants
$variantsQuery = "SELECT pv.*, pc.name as color_name, pc.color_code, ps.name as size_name
                  FROM product_variants pv
                  LEFT JOIN product_colors pc ON pv.color_id = pc.id
                  LEFT JOIN product_sizes ps ON pv.size_id = ps.id
                  WHERE pv.product_id = 10 AND pv.status = 0";
$variantsResult = mysqli_query($conn, $variantsQuery);
$variants = mysqli_fetch_all($variantsResult, MYSQLI_ASSOC);

// Process variants
$colors = [];
$sizes = [];

foreach ($variants as $variant) {
    if (!empty($variant['color_id'])) {
        $colorExists = false;
        foreach ($colors as $existingColor) {
            if ($existingColor['id'] == $variant['color_id']) {
                $colorExists = true;
                break;
            }
        }
        
        if (!$colorExists) {
            $colors[] = [
                'id' => $variant['color_id'],
                'name' => $variant['color_name'],
                'color_code' => $variant['color_code'],
                'additional_price' => $variant['additional_price']
            ];
        }
    }
    
    if (!empty($variant['size_id'])) {
        $sizeExists = false;
        foreach ($sizes as $existingSize) {
            if ($existingSize['id'] == $variant['size_id']) {
                $sizeExists = true;
                break;
            }
        }
        
        if (!$sizeExists) {
            $sizes[] = [
                'id' => $variant['size_id'],
                'name' => $variant['size_name'],
                'additional_price' => $variant['additional_price']
            ];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Variants</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Variants for Product ID 10</h1>
        
        <h2>Product Details</h2>
        <p><strong>Name:</strong> <?= htmlspecialchars($product['name']) ?></p>
        <p><strong>Category:</strong> <?= htmlspecialchars($product['category_name']) ?></p>
        <p><strong>Price:</strong> R <?= number_format($product['sales_price'], 2) ?></p>
        
        <h2>Colors</h2>
        <div class="row">
            <?php foreach ($colors as $color): ?>
                <div class="col-md-3 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><?= htmlspecialchars($color['name']) ?></h5>
                            <div style="width: 50px; height: 50px; background-color: <?= $color['color_code'] ?>; border: 1px solid #ddd;"></div>
                            <p class="card-text">
                                Additional Price: R <?= number_format($color['additional_price'], 2) ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <h2>Sizes</h2>
        <div class="row">
            <?php foreach ($sizes as $size): ?>
                <div class="col-md-3 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><?= htmlspecialchars($size['name']) ?></h5>
                            <p class="card-text">
                                Additional Price: R <?= number_format($size['additional_price'], 2) ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <h2>All Variants</h2>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Color</th>
                    <th>Size</th>
                    <th>Additional Price</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($variants as $variant): ?>
                    <tr>
                        <td><?= htmlspecialchars($variant['color_name']) ?></td>
                        <td><?= htmlspecialchars($variant['size_name']) ?></td>
                        <td>R <?= number_format($variant['additional_price'], 2) ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</body>
</html>
