<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';
require '../../config/dbcon.php';
require '../../includes/header.php';

// Get product ID
$paramValue = checkParamId('id');
if(!is_numeric($paramValue)){
    redirect('products.php', 'Please provide a valid ID');
    exit;
}

$product = getById('products', $paramValue);
if($product['status'] != 200){
    redirect('products.php', 'No such product found');
    exit;
}

// Get selected colors
$selected_colors = [];
$selected_colors_query = mysqli_query($conn, "SELECT DISTINCT color_id FROM product_variants WHERE product_id = {$product['data']['id']} AND color_id IS NOT NULL");
if($selected_colors_query) {
    while($row = mysqli_fetch_assoc($selected_colors_query)) {
        $selected_colors[] = $row['color_id'];
    }
}

// Get selected sizes
$selected_sizes = [];
$selected_sizes_query = mysqli_query($conn, "SELECT DISTINCT size_id FROM product_variants WHERE product_id = {$product['data']['id']} AND size_id IS NOT NULL");
if($selected_sizes_query) {
    while($row = mysqli_fetch_assoc($selected_sizes_query)) {
        $selected_sizes[] = $row['size_id'];
    }
}

// Check if product has variants
$has_variants = !empty($selected_colors) || !empty($selected_sizes);
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Product: <?= $product['data']['name']; ?>
                <a href="products.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <form action="code.php" method="POST" enctype="multipart/form-data" id="mainProductForm">
                <!-- Main form for product details -->

                <!-- Hidden form for variant updates -->
                <div id="variantUpdateFormContainer" style="display:none;">
                    <form action="update_variants.php" method="POST" id="variantUpdateForm">
                        <input type="hidden" name="product_id" value="<?= $product['data']['id']; ?>">
                        <!-- Variant fields will be added dynamically -->
                    </form>
                </div>

                <input type="hidden" name="product_id" value="<?= $product['data']['id']; ?>">

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label>Select Category *</label>
                        <select name="category_id" class="form-select">
                            <option value="">Select Category *</option>
                            <?php
                            $categories = getAll('categories');
                            if($categories['status'] == 200){
                                foreach($categories['data'] as $category){
                                    $selected = $category['id'] == $product['data']['category_id'] ? 'selected' : '';
                                    echo "<option value='{$category['id']}' $selected>{$category['name']}</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label>Product Name *</label>
                        <input type="text" name="name" value="<?= $product['data']['name']; ?>" required class="form-control" />
                    </div>

                    <div class="col-md-12 mb-3">
                        <label>Description</label>
                        <textarea name="description" class="form-control" rows="3"><?= $product['data']['description']; ?></textarea>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label>Price *</label>
                        <input type="text" name="price" value="<?= $product['data']['price']; ?>" required class="form-control" />
                    </div>

                    <div class="col-md-4 mb-3">
                        <label>Quantity *</label>
                        <input type="text" name="quantity" value="<?= $product['data']['quantity']; ?>" required class="form-control" />
                    </div>

                    <div class="col-md-4 mb-3">
                        <label>Image</label>
                        <input type="file" name="image" class="form-control" />
                        <input type="hidden" name="old_image" value="<?= $product['data']['image']; ?>" />
                    </div>

                    <div class="col-md-6 mb-3">
                        <label>Status</label>
                        <br/>
                        <input type="checkbox" name="status" <?= $product['data']['status'] == 0 ? 'checked':''; ?> style="width:30px;height:30px;" />
                    </div>

                    <div class="col-md-6 mb-3">
                        <label>Trending</label>
                        <br/>
                        <input type="checkbox" name="trending" <?= $product['data']['trending'] == 0 ? 'checked':''; ?> style="width:30px;height:30px;" />
                    </div>

                    <div class="col-md-12 mb-3">
                        <button type="submit" name="updateProduct" class="btn btn-primary">Update</button>
                    </div>
                </div>
            </form>

            <hr>

            <!-- Product Variants Section -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Product Variants</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="enableVariants" <?= $has_variants ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="enableVariants">
                                    Enable product variants (colors and sizes)
                                </label>
                            </div>

                            <div id="variantsContainer" style="display: <?= $has_variants ? 'block' : 'none'; ?>;">
                                <div class="mb-3">
                                    <h6>Available Colors</h6>
                                    <?php
                                    $colors_query = mysqli_query($conn, "SELECT * FROM product_colors WHERE status = 0 ORDER BY name ASC");
                                    if(mysqli_num_rows($colors_query) > 0) {
                                        while($color = mysqli_fetch_assoc($colors_query)) {
                                            $is_selected = in_array($color['id'], $selected_colors);
                                            ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="variant_colors[]" value="<?= $color['id'] ?>" id="color<?= $color['id'] ?>" <?= $is_selected ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="color<?= $color['id'] ?>">
                                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: <?= $color['color_code'] ?>; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                                                    <?= htmlspecialchars($color['name']) ?>
                                                </label>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>

                                <div class="mb-3">
                                    <h6>Available Sizes</h6>
                                    <?php
                                    $sizes_query = mysqli_query($conn, "SELECT * FROM product_sizes WHERE status = 0 ORDER BY name ASC");
                                    if(mysqli_num_rows($sizes_query) > 0) {
                                        while($size = mysqli_fetch_assoc($sizes_query)) {
                                            $is_selected = in_array($size['id'], $selected_sizes);
                                            ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="variant_sizes[]" value="<?= $size['id'] ?>" id="size<?= $size['id'] ?>" <?= $is_selected ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="size<?= $size['id'] ?>">
                                                    <?= htmlspecialchars($size['name']) ?>
                                                </label>
                                            </div>
                                            <?php
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Current Product Variants</h5>
                                <div>
                                    <button type="button" id="refreshVariantsBtn" class="btn btn-sm btn-primary me-2">
                                        <i class="fas fa-sync-alt me-1"></i> Refresh Variants
                                    </button>
                                    <button type="button" id="updateVariantsBtn" class="btn btn-sm btn-success">
                                        <i class="fas fa-save me-1"></i> Update Variants
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="autoRefreshVariants" checked>
                                <label class="form-check-label" for="autoRefreshVariants">
                                    Auto-refresh variants when colors or sizes are changed
                                </label>
                            </div>
                            <div id="variantsTableContainer">
                                <!-- Variants will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include('../../includes/footer.php'); ?>

<script>
// Function to refresh the variants table
function refreshVariantsTable() {
    console.log('Refreshing variants table...');
    const productId = document.querySelector('input[name="product_id"]').value;
    console.log('Product ID:', productId);

    const colorCheckboxes = document.querySelectorAll('input[name="variant_colors[]"]:checked');
    console.log('Checked color checkboxes:', colorCheckboxes.length);

    const sizeCheckboxes = document.querySelectorAll('input[name="variant_sizes[]"]:checked');
    console.log('Checked size checkboxes:', sizeCheckboxes.length);

    // Get selected color IDs
    const colorIds = [];
    colorCheckboxes.forEach(function(checkbox) {
        colorIds.push(checkbox.value);
    });
    console.log('Selected color IDs:', colorIds);

    // Get selected size IDs
    const sizeIds = [];
    sizeCheckboxes.forEach(function(checkbox) {
        sizeIds.push(checkbox.value);
    });
    console.log('Selected size IDs:', sizeIds);

    // Build the URL
    let url = `dynamic_variants.php?product_id=${productId}`;

    if (colorIds.length > 0) {
        colorIds.forEach(function(id) {
            url += `&colors[]=${id}`;
        });
    }

    if (sizeIds.length > 0) {
        sizeIds.forEach(function(id) {
            url += `&sizes[]=${id}`;
        });
    }

    console.log('Request URL:', url);

    // Fetch the updated variants table
    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.error) {
                console.error('Error:', data.error);
                return;
            }

            // Update the variants table
            const variantsTableContainer = document.getElementById('variantsTableContainer');
            if (variantsTableContainer) {
                variantsTableContainer.innerHTML = data.html;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
        });
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Script initialized');

    // Get elements
    const enableVariantsCheckbox = document.getElementById('enableVariants');
    const variantsContainer = document.getElementById('variantsContainer');
    const refreshVariantsBtn = document.getElementById('refreshVariantsBtn');
    const updateVariantsBtn = document.getElementById('updateVariantsBtn');
    const variantUpdateForm = document.getElementById('variantUpdateForm');
    const autoRefreshCheckbox = document.getElementById('autoRefreshVariants');
    const colorCheckboxes = document.querySelectorAll('input[name="variant_colors[]"]');
    const sizeCheckboxes = document.querySelectorAll('input[name="variant_sizes[]"]');

    console.log('Elements found:', {
        enableVariantsCheckbox,
        variantsContainer,
        refreshVariantsBtn,
        updateVariantsBtn,
        variantUpdateForm,
        autoRefreshCheckbox,
        colorCheckboxes: colorCheckboxes.length,
        sizeCheckboxes: sizeCheckboxes.length
    });

    // Toggle variants container
    if (enableVariantsCheckbox && variantsContainer) {
        enableVariantsCheckbox.addEventListener('change', function() {
            variantsContainer.style.display = this.checked ? 'block' : 'none';
        });
    }

    // Add event listener to refresh button
    if (refreshVariantsBtn) {
        refreshVariantsBtn.addEventListener('click', function() {
            console.log('Refresh button clicked');
            refreshVariantsTable();
        });
    }

    // Add event listeners to color checkboxes
    colorCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            console.log('Color checkbox changed:', this.id, 'Checked:', this.checked);
            if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
                console.log('Auto-refreshing variants...');
                refreshVariantsTable();
            }
        });
    });

    // Add event listeners to size checkboxes
    sizeCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            console.log('Size checkbox changed:', this.id, 'Checked:', this.checked);
            if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
                console.log('Auto-refreshing variants...');
                refreshVariantsTable();
            }
        });
    });

    // Handle variant updates
    if (updateVariantsBtn && variantUpdateForm) {
        updateVariantsBtn.addEventListener('click', function() {
            console.log('Update button clicked');

            // Clear any existing fields
            while (variantUpdateForm.children.length > 1) {
                variantUpdateForm.removeChild(variantUpdateForm.lastChild);
            }

            // Get all variant fields
            const variantIds = document.querySelectorAll('input[name="variant_ids[]"]');
            const variantColorIds = document.querySelectorAll('input[name="variant_color_ids[]"]');
            const variantSizeIds = document.querySelectorAll('input[name="variant_size_ids[]"]');
            const variantAdditionalPrices = document.querySelectorAll('input[name="variant_additional_prices[]"]');
            const variantQuantities = document.querySelectorAll('input[name="variant_quantities[]"]');
            const variantStatuses = document.querySelectorAll('input[name="variant_status[]"]');

            console.log('Variant fields found:', {
                variantIds: variantIds.length,
                variantColorIds: variantColorIds.length,
                variantSizeIds: variantSizeIds.length,
                variantAdditionalPrices: variantAdditionalPrices.length,
                variantQuantities: variantQuantities.length,
                variantStatuses: variantStatuses.length
            });

            // Add them to the variant update form
            variantIds.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantColorIds.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantSizeIds.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantAdditionalPrices.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantQuantities.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantStatuses.forEach(function(input) {
                if (input.checked) {
                    const clone = input.cloneNode(true);
                    variantUpdateForm.appendChild(clone);
                }
            });

            // Submit the form
            console.log('Submitting variant update form');
            variantUpdateForm.submit();
        });
    }

    // Initial refresh
    console.log('Performing initial refresh...');
    setTimeout(function() {
        refreshVariantsTable();
    }, 500); // Slight delay to ensure DOM is fully loaded
});
</script>
