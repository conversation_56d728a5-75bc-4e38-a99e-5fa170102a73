<?php
header('Content-Type: application/json');

require 'admin/config/function.php';

$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
$color_id = isset($_GET['color_id']) ? intval($_GET['color_id']) : 0;

if (!$product_id || !$color_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid parameters']);
    exit;
}

// First, try to get variant-specific image from product_variant_images table
$variant_image_query = "SELECT image FROM product_variant_images 
                       WHERE product_id = ? AND color_id = ? 
                       ORDER BY display_order ASC, id ASC 
                       LIMIT 1";

$stmt = mysqli_prepare($conn, $variant_image_query);
mysqli_stmt_bind_param($stmt, 'ii', $product_id, $color_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($result && mysqli_num_rows($result) > 0) {
    $row = mysqli_fetch_assoc($result);
    $image_path = "uploads/products/" . $row['image'];
    
    // Check if the file actually exists
    if (file_exists($image_path)) {
        echo json_encode([
            'success' => true,
            'image' => $row['image'],
            'source' => 'variant_specific'
        ]);
        exit;
    }
}

// If no variant-specific image found, try to get from product_images table with variant_id
$product_images_query = "SELECT pi.image FROM product_images pi
                        JOIN product_variants pv ON pi.variant_id = pv.id
                        WHERE pv.product_id = ? AND pv.color_id = ?
                        ORDER BY pi.display_order ASC, pi.id ASC
                        LIMIT 1";

$stmt2 = mysqli_prepare($conn, $product_images_query);
mysqli_stmt_bind_param($stmt2, 'ii', $product_id, $color_id);
mysqli_stmt_execute($stmt2);
$result2 = mysqli_stmt_get_result($stmt2);

if ($result2 && mysqli_num_rows($result2) > 0) {
    $row = mysqli_fetch_assoc($result2);
    $image_path = "uploads/products/" . $row['image'];
    
    // Check if the file actually exists
    if (file_exists($image_path)) {
        echo json_encode([
            'success' => true,
            'image' => $row['image'],
            'source' => 'product_images_variant'
        ]);
        exit;
    }
}

// If still no image found, return the main product image
$main_image_query = "SELECT image FROM products WHERE id = ?";
$stmt3 = mysqli_prepare($conn, $main_image_query);
mysqli_stmt_bind_param($stmt3, 'i', $product_id);
mysqli_stmt_execute($stmt3);
$result3 = mysqli_stmt_get_result($stmt3);

if ($result3 && mysqli_num_rows($result3) > 0) {
    $row = mysqli_fetch_assoc($result3);
    if (!empty($row['image'])) {
        $image_path = "uploads/products/" . $row['image'];
        
        if (file_exists($image_path)) {
            echo json_encode([
                'success' => true,
                'image' => $row['image'],
                'source' => 'main_product'
            ]);
            exit;
        }
    }
}

// No image found
echo json_encode([
    'success' => false,
    'message' => 'No image found for this variant'
]);
?>
