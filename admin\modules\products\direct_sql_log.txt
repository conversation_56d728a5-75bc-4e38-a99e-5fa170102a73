2025-04-13 14:47:57 - <PERSON><PERSON><PERSON> started
2025-04-13 14:47:57 - Basic product data prepared - Data: Array
(
    [category_id] => 1
    [name] => Direct SQL Product 20250413144757
    [cost_price] => 100
    [sales_price] => 150
    [vat_percentage] => 0
    [vatT] => 0
    [price] => 150
    [quantity] => 10
    [barcode] => TEST3368
    [image] => default.jpg
    [status] => 0
)

2025-04-13 14:47:57 - SQL Query - Data: INSERT INTO products (
        category_id, name, description, barcode, cost_price, sales_price, 
        vat_percentage, vatT, price, quantity, image, status, created_at,
        featured, is_featured, product_type_id, can_be_component
    ) VALUES (
        '1', 'Direct SQL Product 20250413144757', 'This is a test product created on 2025-04-13 14:47:57', 'TEST3368', '100', '150',
        '0', '0', '150', '10', 'default.jpg', '0', '2025-04-13 14:47:57',
        '0', '0', '1', '0'
    )
2025-04-13 14:47:57 - Query result - Data: Array
(
    [success] => true
    [product_id] => 25
    [error] => 
)

