<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include necessary files
require '../../config/function.php';
require '../../config/dbcon.php';

// Get product ID from query string
$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
$colors = isset($_GET['colors']) ? $_GET['colors'] : [];
$sizes = isset($_GET['sizes']) ? $_GET['sizes'] : [];

// Log request
$log_file = __DIR__ . '/debug_variants_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - Request: " . print_r($_GET, true) . "\n", FILE_APPEND);

// Get existing variants for this product
$existing_variants = [];
$existing_query = "SELECT * FROM product_variants WHERE product_id = $product_id";
$existing_result = mysqli_query($conn, $existing_query);

if($existing_result && mysqli_num_rows($existing_result) > 0) {
    while($row = mysqli_fetch_assoc($existing_result)) {
        $key = $row['color_id'] . '-' . $row['size_id'];
        $existing_variants[$key] = $row;
    }
}

// Get color and size information
$color_data = [];
if(!empty($colors)) {
    $color_ids_str = implode(',', $colors);
    $colors_query = "SELECT * FROM product_colors WHERE id IN ($color_ids_str)";
    $colors_result = mysqli_query($conn, $colors_query);
    
    if($colors_result && mysqli_num_rows($colors_result) > 0) {
        while($row = mysqli_fetch_assoc($colors_result)) {
            $color_data[$row['id']] = $row;
        }
    }
}

$size_data = [];
if(!empty($sizes)) {
    $size_ids_str = implode(',', $sizes);
    $sizes_query = "SELECT * FROM product_sizes WHERE id IN ($size_ids_str)";
    $sizes_result = mysqli_query($conn, $sizes_query);
    
    if($sizes_result && mysqli_num_rows($sizes_result) > 0) {
        while($row = mysqli_fetch_assoc($sizes_result)) {
            $size_data[$row['id']] = $row;
        }
    }
}

// Build HTML table
$html = '<div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>Color</th>
                        <th>Size</th>
                        <th>Additional Price</th>
                        <th>Quantity</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>';

// If both colors and sizes are selected, create combinations
if(!empty($color_data) && !empty($size_data)) {
    foreach($color_data as $color_id => $color) {
        foreach($size_data as $size_id => $size) {
            $key = $color_id . '-' . $size_id;
            $variant = isset($existing_variants[$key]) ? $existing_variants[$key] : null;
            
            $variant_id = $variant ? $variant['id'] : 'new-' . $key;
            $additional_price = $variant ? $variant['additional_price'] : '0.00';
            $quantity = $variant ? $variant['quantity'] : '0';
            $status = $variant ? ($variant['status'] == 0 ? 'checked' : '') : 'checked';
            
            $html .= '<tr>
                        <td>
                            <span style="display: inline-block; width: 20px; height: 20px; background-color: ' . $color['color_code'] . '; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                            ' . htmlspecialchars($color['name']) . '
                        </td>
                        <td>' . htmlspecialchars($size['name']) . '</td>
                        <td>
                            <input type="hidden" name="variant_ids[]" value="' . $variant_id . '">
                            <input type="hidden" name="variant_color_ids[]" value="' . $color_id . '">
                            <input type="hidden" name="variant_size_ids[]" value="' . $size_id . '">
                            <input type="number" name="variant_additional_prices[]" class="form-control" value="' . $additional_price . '" min="0" step="0.01">
                        </td>
                        <td>
                            <input type="number" name="variant_quantities[]" class="form-control" value="' . $quantity . '" min="0">
                        </td>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="variant_status[]" value="' . $variant_id . '" id="variantStatus' . $variant_id . '" ' . $status . '>
                                <label class="form-check-label" for="variantStatus' . $variant_id . '">
                                    Visible
                                </label>
                            </div>
                        </td>
                    </tr>';
        }
    }
}
// If only colors are selected
else if(!empty($color_data) && empty($size_data)) {
    foreach($color_data as $color_id => $color) {
        $key = $color_id . '-';
        $variant = isset($existing_variants[$key]) ? $existing_variants[$key] : null;
        
        $variant_id = $variant ? $variant['id'] : 'new-' . $key;
        $additional_price = $variant ? $variant['additional_price'] : '0.00';
        $quantity = $variant ? $variant['quantity'] : '0';
        $status = $variant ? ($variant['status'] == 0 ? 'checked' : '') : 'checked';
        
        $html .= '<tr>
                    <td>
                        <span style="display: inline-block; width: 20px; height: 20px; background-color: ' . $color['color_code'] . '; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                        ' . htmlspecialchars($color['name']) . '
                    </td>
                    <td>N/A</td>
                    <td>
                        <input type="hidden" name="variant_ids[]" value="' . $variant_id . '">
                        <input type="hidden" name="variant_color_ids[]" value="' . $color_id . '">
                        <input type="hidden" name="variant_size_ids[]" value="">
                        <input type="number" name="variant_additional_prices[]" class="form-control" value="' . $additional_price . '" min="0" step="0.01">
                    </td>
                    <td>
                        <input type="number" name="variant_quantities[]" class="form-control" value="' . $quantity . '" min="0">
                    </td>
                    <td>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="variant_status[]" value="' . $variant_id . '" id="variantStatus' . $variant_id . '" ' . $status . '>
                            <label class="form-check-label" for="variantStatus' . $variant_id . '">
                                Visible
                            </label>
                        </div>
                    </td>
                </tr>';
    }
}
// If only sizes are selected
else if(empty($color_data) && !empty($size_data)) {
    foreach($size_data as $size_id => $size) {
        $key = '-' . $size_id;
        $variant = isset($existing_variants[$key]) ? $existing_variants[$key] : null;
        
        $variant_id = $variant ? $variant['id'] : 'new-' . $key;
        $additional_price = $variant ? $variant['additional_price'] : '0.00';
        $quantity = $variant ? $variant['quantity'] : '0';
        $status = $variant ? ($variant['status'] == 0 ? 'checked' : '') : 'checked';
        
        $html .= '<tr>
                    <td>N/A</td>
                    <td>' . htmlspecialchars($size['name']) . '</td>
                    <td>
                        <input type="hidden" name="variant_ids[]" value="' . $variant_id . '">
                        <input type="hidden" name="variant_color_ids[]" value="">
                        <input type="hidden" name="variant_size_ids[]" value="' . $size_id . '">
                        <input type="number" name="variant_additional_prices[]" class="form-control" value="' . $additional_price . '" min="0" step="0.01">
                    </td>
                    <td>
                        <input type="number" name="variant_quantities[]" class="form-control" value="' . $quantity . '" min="0">
                    </td>
                    <td>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="variant_status[]" value="' . $variant_id . '" id="variantStatus' . $variant_id . '" ' . $status . '>
                            <label class="form-check-label" for="variantStatus' . $variant_id . '">
                                Visible
                            </label>
                        </div>
                    </td>
                </tr>';
    }
}
// If no colors or sizes are selected
else {
    $html .= '<tr><td colspan="5" class="text-center">No variants selected. Please select colors and/or sizes above.</td></tr>';
}

$html .= '</tbody></table></div>';

// Return the HTML
echo json_encode(['html' => $html]);
?>
