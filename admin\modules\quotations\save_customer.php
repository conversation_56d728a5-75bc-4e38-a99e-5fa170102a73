<?php
// Include necessary files
require '../../config/dbcon.php';
require_once 'customer_functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Function to validate input
function validate($input) {
    global $conn;
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input);
    $input = mysqli_real_escape_string($conn, $input);
    return $input;
}

// Check if quotation ID is provided
if (isset($_POST['quotation_id'])) {
    $quotation_id = validate($_POST['quotation_id']);
    
    // Get quotation details
    $query = "SELECT * FROM quotations WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $quotation_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) > 0) {
        $quotation = mysqli_fetch_assoc($result);
        
        // Check if customer already exists
        $existing_customer = checkCustomerExists($quotation['email'], $quotation['phone']);
        
        if ($existing_customer) {
            // Customer already exists, link quotation to customer
            if (linkQuotationToCustomer($quotation_id, $existing_customer['id'])) {
                $_SESSION['message'] = "Quotation linked to existing customer: " . $existing_customer['name'] . " (" . $existing_customer['client_code'] . ")";
            } else {
                $_SESSION['message'] = "Failed to link quotation to existing customer";
            }
        } else {
            // Save new customer
            $customer = saveCustomerFromQuotation($quotation['name'], $quotation['email'], $quotation['phone']);
            
            if ($customer) {
                // Link quotation to new customer
                if (linkQuotationToCustomer($quotation_id, $customer['id'])) {
                    $_SESSION['message'] = "New customer created and linked to quotation: " . $customer['name'] . " (" . $customer['client_code'] . ")";
                    
                    // Redirect to customers page
                    echo "<script>window.location.href='../customers/customers.php';</script>";
                    exit;
                } else {
                    $_SESSION['message'] = "Customer created but failed to link to quotation";
                }
            } else {
                $_SESSION['message'] = "Failed to create customer";
            }
        }
    } else {
        $_SESSION['message'] = "Quotation not found";
    }
} else {
    $_SESSION['message'] = "Invalid request";
}

// Redirect back to quotation view
$return_url = isset($_POST['return_url']) ? validate($_POST['return_url']) : 'quotations.php';
echo "<script>window.location.href='" . $return_url . "';</script>";
exit;
?>
