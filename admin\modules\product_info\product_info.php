<?php
// Start the session at the very beginning
session_start();

// Process delete action before any output
if(isset($_POST['delete_btn'])) {
    require_once('../../config/dbcon.php');

    $id = mysqli_real_escape_string($conn, $_POST['delete_id']);
    $query = "DELETE FROM product_more_information WHERE id='$id'";
    $query_run = mysqli_query($conn, $query);

    if($query_run) {
        $_SESSION['success'] = "Product inquiry deleted successfully";
    } else {
        $_SESSION['error'] = "Failed to delete product inquiry";
    }

    // Redirect before any output
    header('Location: product_info.php');
    exit();
}

// Process toggle read status action
if(isset($_POST['toggle_read_btn'])) {
    require_once('../../config/dbcon.php');

    $id = mysqli_real_escape_string($conn, $_POST['item_id']);
    $current_status = mysqli_real_escape_string($conn, $_POST['current_status']);
    $new_status = $current_status == 1 ? 0 : 1;

    $query = "UPDATE product_more_information SET read_status='$new_status' WHERE id='$id'";
    $query_run = mysqli_query($conn, $query);

    if($query_run) {
        $status_text = $new_status == 1 ? "marked as read" : "marked as unread";
        $_SESSION['success'] = "Product inquiry $status_text successfully";
    } else {
        $_SESSION['error'] = "Failed to update read status";
    }

    // Redirect before any output
    header('Location: product_info.php');
    exit();
}

// Process save customer action before any output
if(isset($_POST['save_customer_btn'])) {
    require_once('../../config/dbcon.php');

    $inquiry_id = mysqli_real_escape_string($conn, $_POST['inquiry_id']);

    // Get customer info from product_more_information table
    $query = "SELECT name, email, number FROM product_more_information WHERE id='$inquiry_id'";
    $result = mysqli_query($conn, $query);

    if($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $name = $row['name'];
        $email = $row['email'];
        $phone = $row['number'];

        // Generate random client code (CT######)
        $client_code = 'CT' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);

        // Check if customer already exists with this email or phone
        $check_query = "SELECT * FROM customers WHERE email='$email' OR phone='$phone'";
        $check_result = mysqli_query($conn, $check_query);

        if($check_result && mysqli_num_rows($check_result) > 0) {
            $_SESSION['warning'] = "Customer with this email or phone already exists";
        } else {
            // Insert into customers table
            $insert_query = "INSERT INTO customers (name, email, phone, status, client_code)
                             VALUES ('$name', '$email', '$phone', 0, '$client_code')";
            $insert_result = mysqli_query($conn, $insert_query);

            if($insert_result) {
                $_SESSION['success'] = "Customer saved successfully with client code: $client_code";
                header('Location: ../customers/customers.php');
                exit();
            } else {
                $_SESSION['error'] = "Failed to save customer: " . mysqli_error($conn);
            }
        }
    } else {
        $_SESSION['error'] = "Failed to retrieve customer information";
    }

    // Redirect before any output
    header('Location: product_info.php');
    exit();
}

// Include header after processing actions
include('../../includes/header.php');
checkUserRoles(['admin', 'user']);

// Fetch all product inquiries
$query = "SELECT * FROM product_more_information ORDER BY created_at DESC";
$query_run = mysqli_query($conn, $query);
?>

<style>
    .action-btn {
        min-width: 120px;
        text-align: center;
        white-space: nowrap;
    }
</style>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Product Inquiries
                <a href="../../index.php" class="btn btn-dark float-end me-2">Dashboard <i class="fas fa-tachometer-alt"></i></a>
            </h4>
        </div>
        <div class="card-body">
            <?php include('../../includes/message.php'); ?>
            <div class="table-responsive">
                <table id="datatablesSimple" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Product</th>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Message</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if(mysqli_num_rows($query_run) > 0) {
                            while($row = mysqli_fetch_assoc($query_run)) {
                        ?>
                        <tr class="<?= isset($row['read_status']) && $row['read_status'] == 0 ? 'table-warning' : ''; ?>">
                            <td><?= $row['id']; ?></td>
                            <td><?= htmlspecialchars($row['product_name']); ?></td>
                            <td><?= htmlspecialchars($row['name']); ?></td>
                            <td><?= htmlspecialchars($row['number']); ?></td>
                            <td><?= htmlspecialchars($row['email']); ?></td>
                            <td>
                                <div style="max-height: 100px; overflow-y: auto;">
                                    <?= htmlspecialchars($row['message']); ?>
                                </div>
                            </td>
                            <td><?= date('d M Y H:i', strtotime($row['created_at'])); ?></td>
                            <td>
                                <?php if(!isset($row['read_status']) || $row['read_status'] == 0): ?>
                                    <span class="badge bg-warning text-dark">Unread</span>
                                <?php else: ?>
                                    <span class="badge bg-success">Read</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="d-flex gap-2">
                                    <form action="product_info.php" method="POST" class="me-1">
                                        <input type="hidden" name="item_id" value="<?= $row['id']; ?>">
                                        <input type="hidden" name="current_status" value="<?= isset($row['read_status']) ? $row['read_status'] : 0; ?>">
                                        <button type="submit" name="toggle_read_btn" class="btn <?= !isset($row['read_status']) || $row['read_status'] == 0 ? 'btn-success' : 'btn-warning'; ?> btn-sm action-btn">
                                            <i class="fas <?= !isset($row['read_status']) || $row['read_status'] == 0 ? 'fa-check' : 'fa-undo'; ?>"></i>
                                            <?= !isset($row['read_status']) || $row['read_status'] == 0 ? 'Mark as Read' : 'Mark as Unread'; ?>
                                        </button>
                                    </form>
                                    <form action="product_info.php" method="POST" class="me-1">
                                        <input type="hidden" name="inquiry_id" value="<?= $row['id']; ?>">
                                        <button type="submit" name="save_customer_btn" class="btn btn-success btn-sm action-btn">
                                            <i class="fas fa-user-plus"></i> Save Customer
                                        </button>
                                    </form>
                                    <form action="product_info.php" method="POST">
                                        <input type="hidden" name="delete_id" value="<?= $row['id']; ?>">
                                        <button type="submit" name="delete_btn" class="btn btn-danger btn-sm action-btn" onclick="return confirm('Are you sure you want to delete this inquiry?')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php
                            }
                        } else {
                        ?>
                        <tr>
                            <td colspan="9" class="text-center">No Product Inquiries Found</td>
                        </tr>
                        <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
include('../../includes/footer.php');
?>
