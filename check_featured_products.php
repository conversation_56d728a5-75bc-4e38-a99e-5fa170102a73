<?php
require 'admin/config/function.php';

echo "<h2>Featured Products Analysis</h2>";

// Check the structure of the products table
echo "<h3>Products Table Structure (featured fields only):</h3>";
$structure_query = "DESCRIBE products";
$structure_result = mysqli_query($conn, $structure_query);

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

while($row = mysqli_fetch_assoc($structure_result)) {
    if (strpos($row['Field'], 'featured') !== false) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
}

echo "</table>";

// Check current featured products
echo "<h3>Current Featured Products:</h3>";

$featured_query = "SELECT id, name, featured, is_featured, status 
                   FROM products 
                   WHERE status = 0 AND (featured = 1 OR is_featured = 1)
                   ORDER BY id DESC";

$featured_result = mysqli_query($conn, $featured_query);

if($featured_result && mysqli_num_rows($featured_result) > 0) {
    echo "<p><strong>Total featured products found: " . mysqli_num_rows($featured_result) . "</strong></p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>featured</th><th>is_featured</th><th>status</th></tr>";
    
    while($product = mysqli_fetch_assoc($featured_result)) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td>" . $product['featured'] . "</td>";
        echo "<td>" . $product['is_featured'] . "</td>";
        echo "<td>" . $product['status'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No featured products found.</p>";
}

// Check all products with featured flags
echo "<h3>All Products with Featured Flags:</h3>";

$all_featured_query = "SELECT id, name, featured, is_featured, status 
                       FROM products 
                       WHERE (featured = 1 OR is_featured = 1)
                       ORDER BY status, id DESC";

$all_featured_result = mysqli_query($conn, $all_featured_query);

if($all_featured_result && mysqli_num_rows($all_featured_result) > 0) {
    echo "<p><strong>Total products with featured flags: " . mysqli_num_rows($all_featured_result) . "</strong></p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>featured</th><th>is_featured</th><th>status</th><th>Status Text</th></tr>";
    
    while($product = mysqli_fetch_assoc($all_featured_result)) {
        $status_text = $product['status'] == 0 ? 'Visible' : 'Hidden';
        $row_color = $product['status'] == 0 ? '' : 'style="background-color: #ffcccc;"';
        
        echo "<tr $row_color>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td>" . $product['featured'] . "</td>";
        echo "<td>" . $product['is_featured'] . "</td>";
        echo "<td>" . $product['status'] . "</td>";
        echo "<td>" . $status_text . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No products with featured flags found.</p>";
}

// Test the exact query used in product_list.php
echo "<h3>Testing product_list.php Query:</h3>";

$test_query = "SELECT p.id, p.name, p.featured, p.is_featured, p.status,
               c.name as category_name,
               (SELECT COUNT(*) FROM product_variants WHERE product_id = p.id) as has_variants,
               (SELECT COUNT(*) FROM product_bulk_pricing WHERE product_id = p.id) as has_bulk_pricing
               FROM products p
               LEFT JOIN categories c ON p.category_id = c.id
               WHERE p.status = 0 AND (p.featured = 1 OR p.is_featured = 1)";

$test_result = mysqli_query($conn, $test_query);

if($test_result && mysqli_num_rows($test_result) > 0) {
    echo "<p><strong>Query result count: " . mysqli_num_rows($test_result) . "</strong></p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Category</th><th>featured</th><th>is_featured</th><th>Variants</th><th>Bulk Pricing</th></tr>";
    
    while($product = mysqli_fetch_assoc($test_result)) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td>" . htmlspecialchars($product['category_name']) . "</td>";
        echo "<td>" . $product['featured'] . "</td>";
        echo "<td>" . $product['is_featured'] . "</td>";
        echo "<td>" . $product['has_variants'] . "</td>";
        echo "<td>" . $product['has_bulk_pricing'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No results from test query.</p>";
}

echo "<p><a href='product_list.php'>← Back to Product List</a></p>";
?>
