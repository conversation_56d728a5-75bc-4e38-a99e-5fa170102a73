<?php
// Include database connection
require '../../config/function.php';

// Output any errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Create a log file
$log_file = __DIR__ . '/variant_images_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - Test started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        $log_message .= " - Data: " . print_r($data, true);
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Display a form for testing
if (!isset($_POST['test_submit'])) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Variant Images</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    </head>
    <body>
        <div class="container mt-5">
            <h1>Test Variant Images Upload</h1>
            
            <form action="" method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="product_id" class="form-label">Product ID</label>
                    <input type="number" class="form-control" id="product_id" name="product_id" value="1" required>
                </div>
                
                <div class="mb-3">
                    <label for="color_id" class="form-label">Color ID</label>
                    <input type="number" class="form-control" id="color_id" name="color_id" value="1" required>
                </div>
                
                <div class="mb-3">
                    <label for="variant_image" class="form-label">Variant Image</label>
                    <input type="file" class="form-control" id="variant_image" name="variant_image" required>
                </div>
                
                <button type="submit" name="test_submit" class="btn btn-primary">Test Upload</button>
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Process the form submission
try {
    log_message("Form submitted");
    
    // Get form data
    $product_id = validate($_POST['product_id']);
    $color_id = validate($_POST['color_id']);
    
    log_message("Product ID: $product_id, Color ID: $color_id");
    
    // Check if file was uploaded
    if (isset($_FILES['variant_image']) && !empty($_FILES['variant_image']['name'])) {
        log_message("File uploaded", $_FILES['variant_image']);
        
        $upload_path = "../../../uploads/products/";
        
        // Ensure upload directory exists
        if (!file_exists($upload_path)) {
            mkdir($upload_path, 0777, true);
            log_message("Created upload directory: $upload_path");
        }
        
        // Get file details
        $filename = $_FILES['variant_image']['name'];
        $tmp_name = $_FILES['variant_image']['tmp_name'];
        $file_type = $_FILES['variant_image']['type'];
        $file_size = $_FILES['variant_image']['size'];
        $file_error = $_FILES['variant_image']['error'];
        
        log_message("File details", [
            'filename' => $filename,
            'tmp_name' => $tmp_name,
            'type' => $file_type,
            'size' => $file_size,
            'error' => $file_error
        ]);
        
        // Check if it's a valid image
        if ($file_error === 0 && in_array($file_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])) {
            log_message("Valid image type");
            
            // Generate a unique filename
            $finalVariantImage = time() . '_variant_test_' . $filename;
            $upload_to = $upload_path . $finalVariantImage;
            
            log_message("Uploading to: $upload_to");
            
            // Upload the file
            if (move_uploaded_file($tmp_name, $upload_to)) {
                log_message("File uploaded successfully");
                
                // Insert into product_variant_images table
                $variantImageData = [
                    'product_id' => $product_id,
                    'color_id' => $color_id,
                    'image' => $finalVariantImage,
                    'display_order' => 1
                ];
                
                log_message("Variant image data", $variantImageData);
                
                // Insert the variant image
                $result = insert('product_variant_images', $variantImageData);
                
                if ($result) {
                    log_message("Insert successful");
                    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
                        <h3>Success!</h3>
                        <p>The variant image was uploaded and saved successfully.</p>
                        <p>Image path: $upload_to</p>
                        <p><a href='test_variant_images.php'>Test another image</a></p>
                    </div>";
                } else {
                    log_message("Insert failed");
                    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
                        <h3>Error!</h3>
                        <p>Failed to insert the variant image into the database.</p>
                        <p><a href='test_variant_images.php'>Try again</a></p>
                    </div>";
                }
            } else {
                log_message("Failed to move uploaded file");
                echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
                    <h3>Error!</h3>
                    <p>Failed to move the uploaded file.</p>
                    <p><a href='test_variant_images.php'>Try again</a></p>
                </div>";
            }
        } else {
            log_message("Invalid image type or file error", [
                'error' => $file_error,
                'type' => $file_type
            ]);
            echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
                <h3>Error!</h3>
                <p>Invalid image type or file error.</p>
                <p><a href='test_variant_images.php'>Try again</a></p>
            </div>";
        }
    } else {
        log_message("No file uploaded");
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Error!</h3>
            <p>No file was uploaded.</p>
            <p><a href='test_variant_images.php'>Try again</a></p>
        </div>";
    }
} catch (Exception $e) {
    log_message("Exception: " . $e->getMessage());
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>An exception occurred: " . $e->getMessage() . "</p>
        <p><a href='test_variant_images.php'>Try again</a></p>
    </div>";
} catch (Error $e) {
    log_message("Error: " . $e->getMessage());
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>An error occurred: " . $e->getMessage() . "</p>
        <p><a href='test_variant_images.php'>Try again</a></p>
    </div>";
}
?>
