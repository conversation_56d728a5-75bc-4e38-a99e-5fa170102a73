<?php 
include('includes/header.php');
require_once 'includes/functions.php';

// Validate and sanitize supplier ID
$supplierId = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if(!$supplierId) {
    redirect('suppliers.php', 'Invalid supplier ID provided');
}

// Get supplier details
$supplier = getById('supplier', $supplierId);
if($supplier['status'] !== 200) {
    redirect('suppliers.php', 'Supplier not found');
}
$supplierData = $supplier['data'];

// Handle form submission
if($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        validateToken();

        // Validate and sanitize input
        $items = $_POST['items'] ?? [];
        $quantities = $_POST['quantities'] ?? [];
        $prices = $_POST['prices'] ?? [];
        
        if(empty($items)) {
            throw new Exception('At least one item is required');
        }

        // Begin transaction
        mysqli_begin_transaction($conn);

        // Create order header
        $orderData = [
            'supplier_id' => $supplierId,
            'order_date' => date('Y-m-d H:i:s'),
            'status' => 'pending',
            'created_by' => $_SESSION['loggedInUser']['id'],
            'created_at' => date('Y-m-d H:i:s')
        ];

        $orderId = insert('supplier_orders', $orderData);
        if(!$orderId) {
            throw new Exception('Failed to create order');
        }

        // Insert order items
        $totalAmount = 0;
        foreach($items as $index => $item) {
            if(empty($item)) continue;

            $quantity = filter_var($quantities[$index], FILTER_VALIDATE_FLOAT) ?: 0;
            $price = filter_var($prices[$index], FILTER_VALIDATE_FLOAT) ?: 0;
            $amount = $quantity * $price;
            $totalAmount += $amount;

            $itemData = [
                'order_id' => $orderId,
                'item_name' => $item,
                'quantity' => $quantity,
                'unit_price' => $price,
                'amount' => $amount
            ];

            if(!insert('supplier_order_items', $itemData)) {
                throw new Exception('Failed to add order item');
            }
        }

        // Update order total
        $updateResult = update('supplier_orders', 
            ['total_amount' => $totalAmount], 
            ['id' => $orderId]
        );

        if(!$updateResult) {
            throw new Exception('Failed to update order total');
        }

        // Commit transaction
        mysqli_commit($conn);
        redirect("supplier-view-order.php?id=$supplierId", 'Order created successfully');

    } catch (Exception $e) {
        mysqli_rollback($conn);
        $error = $e->getMessage();
    }
}
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Create Order - <?= htmlspecialchars($supplierData['supplier_company_name']) ?></h4>
                <a href="supplier-view-order.php?id=<?= $supplierId ?>" class="btn btn-danger">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php 
            if(isset($error)) {
                echo "<div class='alert alert-danger'>$error</div>";
            }
            alertMessage(); 
            ?>

            <form action="" method="POST" id="orderForm">
                <?= getCSRFToken() ?>
                
                <div class="table-responsive">
                    <table class="table table-bordered" id="itemsTable">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Amount</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <input type="text" name="items[]" class="form-control" required>
                                </td>
                                <td>
                                    <input type="number" name="quantities[]" class="form-control quantity" 
                                           step="0.01" min="0" required>
                                </td>
                                <td>
                                    <input type="number" name="prices[]" class="form-control price" 
                                           step="0.01" min="0" required>
                                </td>
                                <td>
                                    <input type="text" class="form-control amount" readonly>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-danger btn-sm removeRow" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end"><strong>Total:</strong></td>
                                <td>
                                    <input type="text" id="totalAmount" class="form-control" readonly>
                                </td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <div class="mb-3">
                    <button type="button" id="addRow" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add Item
                    </button>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Order
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Calculate row amount
    function calculateRowAmount(row) {
        const quantity = parseFloat(row.find('.quantity').val()) || 0;
        const price = parseFloat(row.find('.price').val()) || 0;
        const amount = quantity * price;
        row.find('.amount').val(amount.toFixed(2));
        calculateTotal();
    }

    // Calculate total amount
    function calculateTotal() {
        let total = 0;
        $('.amount').each(function() {
            total += parseFloat($(this).val()) || 0;
        });
        $('#totalAmount').val(total.toFixed(2));
    }

    // Add new row
    $('#addRow').click(function() {
        const newRow = $('#itemsTable tbody tr:first').clone();
        newRow.find('input').val('');
        newRow.find('.removeRow').prop('disabled', false);
        $('#itemsTable tbody').append(newRow);
    });

    // Remove row
    $(document).on('click', '.removeRow', function() {
        if($('#itemsTable tbody tr').length > 1) {
            $(this).closest('tr').remove();
            calculateTotal();
        }
    });

    // Calculate amount on input change
    $(document).on('input', '.quantity, .price', function() {
        calculateRowAmount($(this).closest('tr'));
    });

    // Form validation
    $('#orderForm').submit(function(e) {
        let valid = true;
        const rows = $('#itemsTable tbody tr');

        rows.each(function() {
            const item = $(this).find('input[name="items[]"]').val();
            const quantity = $(this).find('input[name="quantities[]"]').val();
            const price = $(this).find('input[name="prices[]"]').val();

            if(!item || !quantity || !price) {
                valid = false;
                return false;
            }
        });

        if(!valid) {
            e.preventDefault();
            alert('Please fill all required fields');
        }
    });
});
</script>

<?php include('includes/footer.php'); ?>
