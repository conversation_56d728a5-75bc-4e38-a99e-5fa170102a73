<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Product Management
if(isset($_POST['saveProduct']))
{
    $category_id = validate($_POST['category_id']);
    $name = validate($_POST['name']);
    // Don't use validate() for description as it contains HTML
    $description = $_POST['description'];
    $cost_price = validate($_POST['cost_price']);
    $sales_price = validate($_POST['sales_price']);
    $vat_percentage = validate($_POST['vat_percentage']);
    $vatT = validate($_POST['vatT']) ?: 0; // Get vatT from form or calculate if not provided
    $price = validate($_POST['price']) ?: 0;
    $quantity = validate($_POST['quantity']);
    $barcode = validate($_POST['barcode']);
    $status = isset($_POST['status']) == true ? 1 : 0;
    $featured = isset($_POST['featured']) == true ? 1 : 0;
    $product_type_id = validate($_POST['product_type_id']) ?: 1; // Default to Standard (1) if not provided
    $can_be_component = isset($_POST['can_be_component']) ? 1 : 0;

    // Calculate vatT if not provided
    if(empty($vatT) && !empty($sales_price) && !empty($vat_percentage)) {
        $vatT = $sales_price * ($vat_percentage / 100);
    }

    // Handle image upload
    $finalImage = "default.jpg"; // Default image if none is uploaded
    if(isset($_FILES['image']['name']) && !empty($_FILES['image']['name'])) {
        $upload_path = "../../../uploads/products/";
        $image = $_FILES['image']['name'];

        if($image != "") {
            $finalImage = time() . '_' . $image;
            $upload_to = $upload_path . $finalImage;

            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
            }

            move_uploaded_file($_FILES['image']['tmp_name'], $upload_to);
        }
    }

    $data = [
        'category_id' => $category_id,
        'name' => $name,
        'description' => $description,
        'cost_price' => $cost_price,
        'sales_price' => $sales_price,
        'vat_percentage' => $vat_percentage,
        'vatT' => $vatT, // Add vatT field
        'price' => $price,
        'quantity' => $quantity,
        'barcode' => $barcode,
        'image' => $finalImage,
        'status' => $status,
        'featured' => $featured,
        'product_type_id' => $product_type_id,
        'can_be_component' => $can_be_component,
        'created_at' => date('Y-m-d H:i:s')
    ];

    $result = insert('products', $data);
    $product_id = mysqli_insert_id($conn);

    if($result && $product_id) {
        // Success - output message instead of redirecting
        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Success!</h3>
            <p>The product was created successfully with ID: $product_id</p>
            <p><a href='products.php'>View all products</a></p>
        </div>";
    } else {
        // Failure - output error message instead of redirecting
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Error!</h3>
            <p>Failed to insert the product into the database.</p>
            <p>MySQL Error: " . mysqli_error($conn) . "</p>
            <p><a href='products-create.php'>Try again</a></p>
        </div>";
    }
}
?>
