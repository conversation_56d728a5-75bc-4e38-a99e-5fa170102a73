<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

// Include necessary files
require '../../config/function.php';
require '../../config/dbcon.php';

// Get product ID from query string
$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
$colors = isset($_GET['colors']) ? $_GET['colors'] : [];
$sizes = isset($_GET['sizes']) ? $_GET['sizes'] : [];

// Log request
$log_file = __DIR__ . '/simple_variants_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - Request: " . print_r($_GET, true) . "\n", FILE_APPEND);

// Build a simple HTML table
$html = '<div class="table-responsive">
<table class="table table-bordered table-striped">
    <thead>
        <tr>
            <th>Color</th>
            <th>Size</th>
            <th>Additional Price</th>
            <th>Quantity</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>';

// If colors and/or sizes are selected
if (!empty($colors) || !empty($sizes)) {
    // Generate combinations
    $combinations = [];
    
    if (empty($colors)) {
        // Only sizes
        foreach ($sizes as $size_id) {
            $combinations[] = [
                'color_id' => null,
                'size_id' => $size_id
            ];
        }
    } elseif (empty($sizes)) {
        // Only colors
        foreach ($colors as $color_id) {
            $combinations[] = [
                'color_id' => $color_id,
                'size_id' => null
            ];
        }
    } else {
        // Both colors and sizes
        foreach ($colors as $color_id) {
            foreach ($sizes as $size_id) {
                $combinations[] = [
                    'color_id' => $color_id,
                    'size_id' => $size_id
                ];
            }
        }
    }
    
    // For each combination, check if it exists in the database
    foreach ($combinations as $combo) {
        $color_id = $combo['color_id'];
        $size_id = $combo['size_id'];
        
        // Get color and size names
        $color_name = 'N/A';
        $color_code = '#FFFFFF';
        $size_name = 'N/A';
        
        if ($color_id) {
            $color_query = mysqli_query($conn, "SELECT name, color_code FROM product_colors WHERE id = $color_id");
            if ($color_query && $color = mysqli_fetch_assoc($color_query)) {
                $color_name = $color['name'];
                $color_code = $color['color_code'];
            }
        }
        
        if ($size_id) {
            $size_query = mysqli_query($conn, "SELECT name FROM product_sizes WHERE id = $size_id");
            if ($size_query && $size = mysqli_fetch_assoc($size_query)) {
                $size_name = $size['name'];
            }
        }
        
        // Check if variant exists
        $variant_query = mysqli_query($conn, "SELECT * FROM product_variants 
                                            WHERE product_id = $product_id 
                                            AND " . ($color_id ? "color_id = $color_id" : "color_id IS NULL") . " 
                                            AND " . ($size_id ? "size_id = $size_id" : "size_id IS NULL"));
        
        $variant = mysqli_fetch_assoc($variant_query);
        $variant_id = $variant ? $variant['id'] : 0;
        $additional_price = $variant ? $variant['additional_price'] : 0;
        $quantity = $variant ? $variant['quantity'] : 0;
        $status = $variant ? $variant['status'] : 0;
        
        // Add row to HTML
        $html .= '<tr>
                <td>' . ($color_id ? '<span style="display: inline-block; width: 20px; height: 20px; background-color: ' . $color_code . '; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>' . htmlspecialchars($color_name) : 'N/A') . '</td>
                <td>' . ($size_id ? htmlspecialchars($size_name) : 'N/A') . '</td>
                <td>
                    <input type="hidden" name="variant_ids[]" value="' . $variant_id . '">
                    <input type="hidden" name="variant_color_ids[]" value="' . ($color_id ?: '') . '">
                    <input type="hidden" name="variant_size_ids[]" value="' . ($size_id ?: '') . '">
                    <input type="number" step="0.01" name="variant_additional_prices[]" class="form-control" value="' . $additional_price . '">
                </td>
                <td>
                    <input type="number" name="variant_quantities[]" class="form-control" value="' . $quantity . '" min="0">
                </td>
                <td>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="variant_status[]" value="' . $variant_id . '" id="variantStatus' . $variant_id . '" ' . ($status == 0 ? 'checked' : '') . '>
                        <label class="form-check-label" for="variantStatus' . $variant_id . '">
                            Visible
                        </label>
                    </div>
                </td>
            </tr>';
    }
} else {
    $html .= '<tr><td colspan="5" class="text-center">No variants selected. Please select colors and/or sizes above.</td></tr>';
}

$html .= '</tbody></table></div>';

// Return the HTML
echo json_encode(['html' => $html]);
?>
