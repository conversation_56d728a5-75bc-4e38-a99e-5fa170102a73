<?php
// Include database connection
include('../../config/dbcon.php');

// Create product_variants table
$sql_variants = "CREATE TABLE IF NOT EXISTS `product_variants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) DEFAULT NULL,
  `size_id` int(11) DEFAULT NULL,
  `additional_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `color_id` (`color_id`),
  KEY `size_id` (`size_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

// Create product_bulk_pricing table
$sql_bulk_pricing = "CREATE TABLE IF NOT EXISTS `product_bulk_pricing` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `min_quantity` int(11) NOT NULL,
  `max_quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

// Execute the SQL statements
if (mysqli_query($conn, $sql_variants)) {
    echo "Table 'product_variants' created successfully or already exists.<br>";
} else {
    echo "Error creating table 'product_variants': " . mysqli_error($conn) . "<br>";
}

if (mysqli_query($conn, $sql_bulk_pricing)) {
    echo "Table 'product_bulk_pricing' created successfully or already exists.<br>";
} else {
    echo "Error creating table 'product_bulk_pricing': " . mysqli_error($conn) . "<br>";
}

// Close the connection
mysqli_close($conn);

echo "<p>Database setup completed.</p>";
echo "<p><a href='products-create.php'>Go to Product Creation</a></p>";
?>
