<?php
require_once dirname(__DIR__) . '/config/config.php';
require dirname(__DIR__) . '/config/function.php';
require __DIR__ . '/authentication.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>BizBox Business Assistant 2.0</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Other CSS -->
    <link href="https://cdn.jsdelivr.net/npm/simple-datatables@7.1.2/dist/style.min.css" rel="stylesheet" />
    <link href="<?= $BASE_URL ?>/admin/assets/css/styles.css" rel="stylesheet" />
    <script src="https://use.fontawesome.com/releases/v6.3.0/js/all.js" crossorigin="anonymous"></script>

    <!-- Custom CSS for dropdown fix -->
    <style>
    #userDropdownMenu {
        position: absolute;
        right: 0;
        left: auto;
        top: 100%;
        z-index: 1000;
        min-width: 12rem;
        padding: 0.5rem 0;
        margin: 0.125rem 0 0;
        font-size: 1rem;
        color: #212529;
        text-align: left;
        list-style: none;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0,0,0,.15);
        border-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    #userDropdown.active .nav-link {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }

    #userDropdownMenu li {
        list-style: none;
    }

    #userDropdownMenu .dropdown-item {
        display: block;
        width: 100%;
        padding: 0.25rem 1.5rem;
        clear: both;
        font-weight: 400;
        color: #212529;
        text-align: inherit;
        white-space: nowrap;
        background-color: transparent;
        border: 0;
        text-decoration: none;
    }

    #userDropdownMenu .dropdown-item:hover,
    #userDropdownMenu .dropdown-item:focus {
        color: #16181b;
        text-decoration: none;
        background-color: #f8f9fa;
    }

    #userDropdownMenu .dropdown-divider {
        height: 0;
        margin: 0.5rem 0;
        overflow: hidden;
        border-top: 1px solid #e9ecef;
    }
    </style>

    <script>
    // Initialize tooltips and popovers only (dropdown handled separately with jQuery)
    $(document).ready(function() {
        // Initialize all tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();

        // Initialize all popovers
        $('[data-bs-toggle="popover"]').popover();
    });
    </script>
</head>
<body class="sb-nav-fixed">
    <?php include('navbar.php'); ?>
    <div id="layoutSidenav">
        <?php include('sidebar.php'); ?>
        <div id="layoutSidenav_content">
            <main>

