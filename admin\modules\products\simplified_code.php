<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Create a log file
$log_file = __DIR__ . '/simplified_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - <PERSON>ript started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Product Management - Simplified
if(isset($_POST['saveProduct'])) {
    try {
        log_message("saveProduct handler started");
        
        // Basic product data
        $category_id = validate($_POST['category_id']);
        $name = validate($_POST['name']);
        $description = $_POST['description']; // Don't validate HTML content
        $cost_price = validate($_POST['cost_price']);
        $sales_price = validate($_POST['sales_price']);
        $quantity = validate($_POST['quantity']);
        $barcode = isset($_POST['barcode']) ? validate($_POST['barcode']) : '';
        $status = isset($_POST['status']) ? validate($_POST['status']) : 0;
        $featured = isset($_POST['featured']) ? validate($_POST['featured']) : 0;
        $product_type_id = isset($_POST['product_type_id']) ? validate($_POST['product_type_id']) : 1;
        $can_be_component = isset($_POST['can_be_component']) ? validate($_POST['can_be_component']) : 0;
        
        log_message("Basic product data", [
            'category_id' => $category_id,
            'name' => $name,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'quantity' => $quantity
        ]);
        
        // Calculate VAT
        $vat_percentage = getVatPercentage();
        $vatT = ($sales_price * $vat_percentage) / 100;
        $price = $sales_price + $vatT;
        
        log_message("Price calculations", [
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price
        ]);
        
        // Handle image upload
        $finalImage = "";
        if(isset($_FILES['image']) && !empty($_FILES['image']['name'])) {
            log_message("Processing image upload");
            
            $upload_path = "../../../uploads/products/";
            
            // Ensure upload directory exists
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
            }
            
            $filename = $_FILES['image']['name'];
            $tmp_name = $_FILES['image']['tmp_name'];
            
            // Generate a unique filename
            $finalImage = time() . '_' . $filename;
            $upload_to = $upload_path . $finalImage;
            
            log_message("Uploading to: $upload_to");
            
            if(move_uploaded_file($tmp_name, $upload_to)) {
                log_message("File uploaded successfully");
            } else {
                log_message("Failed to move uploaded file");
                $finalImage = ""; // Reset if upload failed
            }
        }
        
        // Prepare product data
        $data = [
            'category_id' => $category_id,
            'name' => $name,
            'description' => $description,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price,
            'quantity' => $quantity,
            'barcode' => $barcode,
            'image' => $finalImage,
            'status' => $status,
            'featured' => $featured,
            'product_type_id' => $product_type_id,
            'can_be_component' => $can_be_component
        ];
        
        log_message("Product data prepared", $data);
        
        // Insert the product
        log_message("Inserting product");
        $result = insert('products', $data);
        $product_id = mysqli_insert_id($conn);
        
        log_message("Product inserted with ID", $product_id);
        
        if($result && $product_id) {
            log_message("About to redirect to products.php");
            redirect('products.php', 'Product Created Successfully!');
        } else {
            log_message("Product insertion failed");
            redirect('products-create.php', 'Something Went Wrong!');
        }
    } catch (Exception $e) {
        log_message("Exception: " . $e->getMessage());
        redirect('products-create.php', 'Error: ' . $e->getMessage());
    } catch (Error $e) {
        log_message("Error: " . $e->getMessage());
        redirect('products-create.php', 'Error: ' . $e->getMessage());
    }
}
?>
