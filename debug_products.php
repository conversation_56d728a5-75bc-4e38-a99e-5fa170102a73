<?php
include('admin/config/dbcon.php');

// Get the category filter from URL
$category_filter = isset($_GET['category']) ? (string)$_GET['category'] : '0';
// Handle empty string case
if ($category_filter === '') {
    $category_filter = '0';
}

echo "<h2>Debug Products Array</h2>";
echo "<p>Category Filter: '" . htmlspecialchars($category_filter) . "'</p>";

// Function to get products
function getProducts($category_filter) {
    global $conn;
    
    // Build the query
    $sql = "SELECT p.id, p.name, p.category_id, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 0";
    
    // Add category filter if selected
    if ($category_filter !== '0' && !empty($category_filter)) {
        $sql .= " AND (p.category_id = '" . mysqli_real_escape_string($conn, $category_filter) . "')";
    }
    
    $sql .= " ORDER BY p.name ASC";
    
    echo "<p>SQL Query: " . htmlspecialchars($sql) . "</p>";
    
    // Execute the query
    $result = mysqli_query($conn, $sql);
    
    if ($result) {
        $products = mysqli_fetch_all($result, MYSQLI_ASSOC);
        echo "<p>Found " . count($products) . " products</p>";
        return $products;
    } else {
        echo "<p>Query error: " . mysqli_error($conn) . "</p>";
        return [];
    }
}

// Get products
$products = getProducts($category_filter);

// Display the products
if (!empty($products)) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Category ID</th><th>Category Name</th></tr>";
    foreach ($products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['name'] . "</td>";
        echo "<td>" . $product['category_id'] . "</td>";
        echo "<td>" . $product['category_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No products found</p>";
}

// Show links to test
echo "<h3>Test Links</h3>";
echo "<ul>";
echo "<li><a href='debug_products.php'>All Categories</a></li>";
echo "<li><a href='debug_products.php?category=1'>Category 1</a></li>";
echo "<li><a href='debug_products.php?category=3'>Category 3</a></li>";
echo "<li><a href='debug_products.php?category=4'>Category 4</a></li>";
echo "</ul>";

// Back to product list
echo "<p><a href='product_list.php'>Back to Product List</a></p>";
?>
