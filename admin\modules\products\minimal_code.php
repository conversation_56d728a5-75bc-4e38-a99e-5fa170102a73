<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Create a log file
$log_file = __DIR__ . '/debug_minimal.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - <PERSON><PERSON><PERSON> started\n", FILE_APPEND);

require '../../config/function.php';

// Log that we've included the function file
file_put_contents($log_file, date('Y-m-d H:i:s') . " - Function file included\n", FILE_APPEND);

// Product Management - Minimal version
file_put_contents($log_file, date('Y-m-d H:i:s') . " - Checking for saveProduct\n", FILE_APPEND);

if(isset($_POST['saveProduct'])) {
    file_put_contents($log_file, date('Y-m-d H:i:s') . " - saveProduct found\n", FILE_APPEND);

    try {
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Inside try block\n", FILE_APPEND);
        // Basic product data
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Getting basic product data\n", FILE_APPEND);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - POST data: " . print_r($_POST, true) . "\n", FILE_APPEND);

        $category_id = validate($_POST['category_id']);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Category ID: $category_id\n", FILE_APPEND);

        $name = validate($_POST['name']);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Name: $name\n", FILE_APPEND);

        $description = $_POST['description']; // Don't validate HTML content
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Description length: " . strlen($description) . "\n", FILE_APPEND);

        $cost_price = validate($_POST['cost_price']);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Cost price: $cost_price\n", FILE_APPEND);

        $sales_price = validate($_POST['sales_price']);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Sales price: $sales_price\n", FILE_APPEND);

        // Get VAT percentage from settings
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Getting VAT percentage\n", FILE_APPEND);

        // Get VAT percentage from settings table
        $vat_query = mysqli_query($conn, "SELECT vat_percentage FROM settings LIMIT 1");
        $vat_percentage = 0; // Default value

        if($vat_query && mysqli_num_rows($vat_query) > 0) {
            $vat_percentage = mysqli_fetch_assoc($vat_query)['vat_percentage'];
        }

        file_put_contents($log_file, date('Y-m-d H:i:s') . " - VAT percentage from settings: $vat_percentage\n", FILE_APPEND);

        // Calculate VAT
        $vatT = ($sales_price * $vat_percentage) / 100;
        $price = $sales_price + $vatT;

        file_put_contents($log_file, date('Y-m-d H:i:s') . " - VAT amount: $vatT, Final price: $price\n", FILE_APPEND);

        $quantity = validate($_POST['quantity']);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Quantity: $quantity\n", FILE_APPEND);

        $barcode = validate($_POST['barcode']);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Barcode: $barcode\n", FILE_APPEND);

        $status = isset($_POST['status']) ? 1 : 0;
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Status: $status\n", FILE_APPEND);

        $featured = isset($_POST['featured']) ? 1 : 0;
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Featured: $featured\n", FILE_APPEND);

        $product_type_id = isset($_POST['product_type_id']) ? validate($_POST['product_type_id']) : 1;
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Product type ID: $product_type_id\n", FILE_APPEND);

        $can_be_component = isset($_POST['can_be_component']) ? 1 : 0;
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Can be component: $can_be_component\n", FILE_APPEND);

        // Handle main image upload
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Handling image upload\n", FILE_APPEND);

        $finalImage = "";
        if(isset($_FILES['image']['name']) && !empty($_FILES['image']['name'])) {
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - Image found: " . $_FILES['image']['name'] . "\n", FILE_APPEND);

            $upload_path = "../../../uploads/products/";
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - Upload path: $upload_path\n", FILE_APPEND);

            // Ensure upload directory exists
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
                file_put_contents($log_file, date('Y-m-d H:i:s') . " - Created upload directory\n", FILE_APPEND);
            }

            $image = $_FILES['image']['name'];
            $tmp_name = $_FILES['image']['tmp_name'];

            // Generate a unique filename
            $finalImage = time() . '_' . $image;
            $upload_to = $upload_path . $finalImage;

            file_put_contents($log_file, date('Y-m-d H:i:s') . " - Final image path: $upload_to\n", FILE_APPEND);

            // Upload the file
            $upload_result = move_uploaded_file($tmp_name, $upload_to);
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - Upload result: " . ($upload_result ? "Success" : "Failed") . "\n", FILE_APPEND);
        } else {
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - No image uploaded\n", FILE_APPEND);
        }

        // Prepare product data
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Preparing product data\n", FILE_APPEND);

        $data = [
            'category_id' => $category_id,
            'name' => $name,
            'description' => $description,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price,
            'quantity' => $quantity,
            'barcode' => $barcode,
            'image' => $finalImage,
            'status' => $status,
            'featured' => $featured,
            'product_type_id' => $product_type_id,
            'can_be_component' => $can_be_component
        ];

        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Product data prepared: " . print_r($data, true) . "\n", FILE_APPEND);

        // Insert the product
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Inserting product\n", FILE_APPEND);

        $result = insert('products', $data);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Insert result: " . ($result ? "Success" : "Failed") . "\n", FILE_APPEND);

        $product_id = mysqli_insert_id($conn);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Product ID: $product_id\n", FILE_APPEND);

        if($result && $product_id) {
            // Success - redirect to products page
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - Success, redirecting to products.php\n", FILE_APPEND);

            // Instead of redirecting, let's output a success message
            echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
                <h3>Success!</h3>
                <p>The product was created successfully with ID: $product_id</p>
                <p><a href='products.php'>View all products</a></p>
            </div>";
            exit;

            // redirect('products.php', 'Product Created Successfully!');
        } else {
            // Failure - redirect back to create page
            file_put_contents($log_file, date('Y-m-d H:i:s') . " - Failed, redirecting to products-create.php\n", FILE_APPEND);

            // Instead of redirecting, let's output an error message
            echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
                <h3>Error!</h3>
                <p>Failed to insert the product into the database.</p>
                <p>MySQL Error: " . mysqli_error($conn) . "</p>
                <p><a href='products-create.php'>Try again</a></p>
            </div>";
            exit;

            // redirect('products-create.php', 'Something Went Wrong!');
        }
    } catch (Exception $e) {
        // Handle exceptions
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Exception: " . $e->getMessage() . "\n", FILE_APPEND);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Stack trace: " . $e->getTraceAsString() . "\n", FILE_APPEND);

        // Output error message
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Error!</h3>
            <p>An exception occurred: " . $e->getMessage() . "</p>
            <p><a href='products-create.php'>Try again</a></p>
        </div>";
        exit;

        // redirect('products-create.php', 'Error: ' . $e->getMessage());
    } catch (Error $e) {
        // Handle errors
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Error: " . $e->getMessage() . "\n", FILE_APPEND);
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Stack trace: " . $e->getTraceAsString() . "\n", FILE_APPEND);

        // Output error message
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Error!</h3>
            <p>An error occurred: " . $e->getMessage() . "</p>
            <p><a href='products-create.php'>Try again</a></p>
        </div>";
        exit;

        // redirect('products-create.php', 'Error: ' . $e->getMessage());
    }
}
?>
