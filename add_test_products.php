<?php
include('admin/config/dbcon.php');

echo "<h2>Adding Test Products with Different Categories</h2>";

// Function to add a test product
function addTestProduct($name, $category_id, $description = "Test product description") {
    global $conn;
    
    // Check if product already exists
    $check_sql = "SELECT id FROM products WHERE name = ?";
    $check_stmt = mysqli_prepare($conn, $check_sql);
    mysqli_stmt_bind_param($check_stmt, "s", $name);
    mysqli_stmt_execute($check_stmt);
    $result = mysqli_stmt_get_result($check_stmt);
    
    if (mysqli_num_rows($result) > 0) {
        echo "<p>Product '$name' already exists. Skipping.</p>";
        return false;
    }
    
    // Insert new product
    $sql = "INSERT INTO products (name, category_id, description, barcode, cost_price, sales_price, vat_percentage, vatT, price, quantity, image, status, created_at, featured) 
            VALUES (?, ?, ?, '0000', 100.00, 115.00, 15, 17.25, 132.25, 10, 'default.jpg', 0, NOW(), 1)";
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "sss", $name, $category_id, $description);
    
    if (mysqli_stmt_execute($stmt)) {
        $product_id = mysqli_insert_id($conn);
        echo "<p>Added product: $name (ID: $product_id) with category ID: $category_id</p>";
        return true;
    } else {
        echo "<p>Error adding product: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

// Add test products with different categories
$test_products = [
    ["Test Product - Category 1", "1", "This is a test product in Category 1 for testing the category filter."],
    ["Test Product - Category 3", "3", "This is a test product in Category 3 for testing the category filter."]
];

foreach ($test_products as $product) {
    addTestProduct($product[0], $product[1], $product[2]);
}

// Show link to product list
echo "<p><a href='product_list.php'>Go to Product List</a></p>";

// Show link to test category filter
echo "<p><a href='product_list.php?category=1'>View Category 1 Products</a></p>";
echo "<p><a href='product_list.php?category=3'>View Category 3 Products</a></p>";
?>
