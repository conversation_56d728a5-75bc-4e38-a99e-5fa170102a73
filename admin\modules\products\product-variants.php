<?php
session_start();
require_once '../../config/function.php';

// Get product ID
$product_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if(!$product_id) {
    $_SESSION['message'] = "Invalid product ID";
    header("Location: products.php");
    exit(0);
}

// Get product details
$product_query = "SELECT * FROM products WHERE id = ?";
$stmt = mysqli_prepare($conn, $product_query);
mysqli_stmt_bind_param($stmt, "i", $product_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if(!$result || mysqli_num_rows($result) == 0) {
    $_SESSION['message'] = "Product not found";
    header("Location: products.php");
    exit(0);
}

$product = mysqli_fetch_assoc($result);

include('../../includes/header.php');
?>

<div class="container-fluid px-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4>
                            <i class="fas fa-cogs"></i> 
                            Manage Variants: <?= htmlspecialchars($product['name']) ?>
                        </h4>
                        <div>
                            <a href="products-edit.php?id=<?= $product_id ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Product Edit
                            </a>
                            <a href="product-view.php?id=<?= $product_id ?>" class="btn btn-info">
                                <i class="fas fa-eye"></i> View Product
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php include('../../includes/message.php'); ?>
                    
                    <!-- Product Info Summary -->
                    <div class="alert alert-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Product:</strong> <?= htmlspecialchars($product['name']) ?><br>
                                <strong>Category:</strong> <?= htmlspecialchars($product['category_id']) ?><br>
                                <strong>Base Price:</strong> R <?= number_format($product['sales_price'], 2) ?>
                            </div>
                            <div class="col-md-6">
                                <?php
                                // Check if variants actually exist in the database
                                $variant_check_query = "SELECT COUNT(*) as variant_count FROM product_variants WHERE product_id = ?";
                                $variant_check_stmt = mysqli_prepare($conn, $variant_check_query);
                                mysqli_stmt_bind_param($variant_check_stmt, "i", $product['id']);
                                mysqli_stmt_execute($variant_check_stmt);
                                $variant_check_result = mysqli_stmt_get_result($variant_check_stmt);
                                $variant_count = mysqli_fetch_assoc($variant_check_result)['variant_count'];
                                $has_variants = $variant_count > 0;
                                ?>
                                <strong>Variants:</strong>
                                <span class="badge <?= $has_variants ? 'bg-success' : 'bg-secondary' ?>">
                                    <?= $has_variants ? "Yes ($variant_count)" : 'No' ?>
                                </span><br>
                                <strong>Status:</strong> 
                                <span class="badge <?= $product['status'] == 0 ? 'bg-success' : 'bg-danger' ?>">
                                    <?= $product['status'] == 0 ? 'Active' : 'Inactive' ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <?php if($has_variants): ?>
                        <!-- Variant Management Section -->
                        <?php include('integrated_variants.php'); ?>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> No Variants Found</h5>
                            <p>This product does not have any variants yet. You can:</p>
                            <ol>
                                <li>Go to the product edit page to enable variants and add colors/sizes</li>
                                <li>Or use the variant management tools below to create variants</li>
                            </ol>
                            <a href="products-edit.php?id=<?= $product_id ?>" class="btn btn-primary">
                                <i class="fas fa-edit"></i> Edit Product Settings
                            </a>
                        </div>

                        <!-- Show variant creation tools even if no variants exist -->
                        <div class="alert alert-secondary">
                            <h6>Create Variants</h6>
                            <p>You can still use the variant management tools below to create variants for this product.</p>
                        </div>
                        <?php include('integrated_variants.php'); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Additional styles for variant management page */
.variant-management-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.variant-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.variant-stats .stat-item {
    text-align: center;
    padding: 10px;
}

.variant-stats .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
}

.variant-stats .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}
</style>

<?php include('../../includes/footer.php'); ?>
