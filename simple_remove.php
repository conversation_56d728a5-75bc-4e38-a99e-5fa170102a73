<?php
session_start();

// Get the index from the URL
$index = isset($_GET['index']) ? intval($_GET['index']) : -1;

// Check if the index is valid
if ($index >= 0 && isset($_SESSION['quotation_items'])) {
    // Get the keys of the quotation items
    $keys = array_keys($_SESSION['quotation_items']);
    
    // Check if the index exists in the keys array
    if (isset($keys[$index])) {
        // Get the key at the specified index
        $key = $keys[$index];
        
        // Remove the item with this key
        unset($_SESSION['quotation_items'][$key]);
        
        // Set success message
        $_SESSION['message'] = "Item removed successfully!";
        $_SESSION['message_type'] = "success";
    } else {
        // Set error message
        $_SESSION['message'] = "Invalid item index!";
        $_SESSION['message_type'] = "danger";
    }
} else {
    // Set error message
    $_SESSION['message'] = "Invalid request!";
    $_SESSION['message_type'] = "danger";
}

// Redirect back to the quotation page
header("Location: quotation.php");
exit;
?>
