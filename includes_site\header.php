<?php
// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);

// Session and quotation functions should already be included by the parent file
// Just get the quotation count
$quotation_item_count = function_exists('getQuotationItemCount') ? getQuotationItemCount() : 0;
?>
<header id="header" class="header d-flex align-items-center fixed-top">
    <div class="container-fluid container-xl position-relative d-flex align-items-center">
        <a href="index.php" class="logo d-flex align-items-center me-auto">
            <div>
                <!-- <img src="assets/img/logo768x409.png" alt=""> -->
                <h2 class="sitename">Demo Application</h2>
                Anytime, Anywhere Access
            </div>
        </a>

        <nav id="navmenu" class="navmenu">
            <ul>
                <li><a href="index.php<?= ($current_page == 'index.php') ? '#hero' : '' ?>" class="<?= ($current_page == 'index.php') ? 'active' : '' ?>">Home</a></li>
                <li><a href="index.php<?= ($current_page == 'index.php') ? '#about' : '' ?>">About</a></li>
                <li><a href="index.php<?= ($current_page == 'index.php') ? '#services' : '' ?>">Features</a></li>
                <li><a href="services.php" class="<?= ($current_page == 'services.php') ? 'active' : '' ?>">Services</a></li>
                <li><a href="product_list.php" class="<?= ($current_page == 'product_list.php') ? 'active' : '' ?>">Products</a></li>
                <li><a href="custom_builder.php" class="<?= ($current_page == 'custom_builder.php' || $current_page == 'build_product.php') ? 'active' : '' ?>">Custom Builder</a></li>
                <li><a href="index.php<?= ($current_page == 'index.php') ? '#faq' : '' ?>">F.A.Q</a></li>
                <li><a href="index.php<?= ($current_page == 'index.php') ? '#contact' : '' ?>">Contact</a></li>
                <li><a href="request_call_back.php" class="<?= ($current_page == 'request_call_back.php') ? 'active' : '' ?>">Request Call Back</a></li>
                <li style="margin-right: 15px;">
                    <a href="quotation.php" class="position-relative <?= ($current_page == 'quotation.php') ? 'active' : '' ?>" title="View Quotation" style="background-color: #fd7e14; color: #fff; padding: 8px 15px; border-radius: 5px; font-weight: 600; display: inline-block;">
                        <i class="bi bi-cart"></i>
                        <?php if ($quotation_item_count > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6rem; z-index: 5;">
                                <?= $quotation_item_count ?>
                            </span>
                        <?php endif; ?>
                    </a>
                </li>
                <li><a href="login.php" class="login-btn" style="background-color: #007bff; color: #fff; padding: 8px 15px; border-radius: 5px; font-weight: 600;">Login</a></li>
            </ul>
            <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
        </nav>
    </div>
</header>
