<?php
// Include database connection
include('admin/config/dbcon.php');

// Function to update the product_more_information.php file
function updateProductMoreInformationFile() {
    // Find the section in product_more_information.php that processes variants
    $file_path = 'product_more_information.php';
    $file_content = file_get_contents($file_path);
    
    // Find the section that processes variants
    $start_marker = "    // Get unique colors and sizes";
    $end_marker = "    // Get bulk pricing if any";
    
    $start_pos = strpos($file_content, $start_marker);
    $end_pos = strpos($file_content, $end_marker, $start_pos);
    
    if ($start_pos !== false && $end_pos !== false) {
        // Extract the section
        $section_length = $end_pos - $start_pos;
        $old_section = substr($file_content, $start_pos, $section_length);
        
        // Create the new section
        $new_section = "    // Get unique colors and sizes with their additional prices
    \$product['colors'] = [];
    \$product['sizes'] = [];
    
    // First, collect all unique colors and sizes
    \$color_map = []; // To track unique colors
    \$size_map = []; // To track unique sizes
    
    foreach (\$product['variants'] as \$variant) {
        if (!empty(\$variant['color_id'])) {
            \$color_id = \$variant['color_id'];
            if (!isset(\$color_map[\$color_id])) {
                \$color_map[\$color_id] = [
                    'id' => \$color_id,
                    'name' => \$variant['color_name'],
                    'color_code' => \$variant['color_code'],
                    'additional_price' => 0
                ];
            }
            
            // Update additional price if this variant has a higher price
            if (\$variant['additional_price'] > \$color_map[\$color_id]['additional_price']) {
                \$color_map[\$color_id]['additional_price'] = \$variant['additional_price'];
            }
        }
        
        if (!empty(\$variant['size_id'])) {
            \$size_id = \$variant['size_id'];
            if (!isset(\$size_map[\$size_id])) {
                \$size_map[\$size_id] = [
                    'id' => \$size_id,
                    'name' => \$variant['size_name'],
                    'additional_price' => 0
                ];
            }
            
            // Update additional price if this variant has a higher price
            if (\$variant['additional_price'] > \$size_map[\$size_id]['additional_price']) {
                \$size_map[\$size_id]['additional_price'] = \$variant['additional_price'];
            }
        }
    }
    
    // Convert maps to arrays
    \$product['colors'] = array_values(\$color_map);
    \$product['sizes'] = array_values(\$size_map);";
        
        // Replace the old section with the new section
        $new_content = str_replace($old_section, $new_section, $file_content);
        
        // Write the updated content back to the file
        file_put_contents($file_path, $new_content);
        
        return true;
    }
    
    return false;
}

// Update the product_more_information.php file
$result = updateProductMoreInformationFile();

// Display the result
echo "<h1>Fix for product_more_information.php</h1>";
if ($result) {
    echo "<p>Successfully updated the product_more_information.php file!</p>";
} else {
    echo "<p>Failed to update the product_more_information.php file.</p>";
}

echo "<p>Please check the product detail page and quick view modal to see the variant prices.</p>";
echo "<p><a href='product_list.php' class='btn btn-primary'>Go to Product List</a></p>";
?>
