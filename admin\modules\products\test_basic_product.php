<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Basic product data
$category_id = 1; // Use a valid category ID
$name = "Test Product " . date('YmdHis');
$description = "This is a test product created on " . date('Y-m-d H:i:s');
$cost_price = 100.00;
$sales_price = 150.00;
$quantity = 10;
$barcode = "TEST" . rand(1000, 9999);
$vat_percentage = 0; // Default value
$vatT = 0;
$price = $sales_price;
$image = "default.jpg";
$status = 0;
$featured = 0;
$product_type_id = 1;
$can_be_component = 0;
$created_at = date('Y-m-d H:i:s');

// Prepare product data
$data = [
    'category_id' => $category_id,
    'name' => $name,
    'description' => $description,
    'cost_price' => $cost_price,
    'sales_price' => $sales_price,
    'vat_percentage' => $vat_percentage,
    'vatT' => $vatT,
    'price' => $price,
    'quantity' => $quantity,
    'barcode' => $barcode,
    'image' => $image,
    'status' => $status,
    'featured' => $featured,
    'product_type_id' => $product_type_id,
    'can_be_component' => $can_be_component,
    'created_at' => $created_at
];

// Insert the product
$result = insert('products', $data);
$product_id = mysqli_insert_id($conn);

if($result && $product_id) {
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Success!</h3>
        <p>The product was created successfully with ID: $product_id</p>
        <p><a href='products.php'>View all products</a></p>
    </div>";
} else {
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
        <h3>Error!</h3>
        <p>Failed to insert the product into the database.</p>
        <p>MySQL Error: " . mysqli_error($conn) . "</p>
        <p><a href='products-create.php'>Try again</a></p>
    </div>";
}
?>
