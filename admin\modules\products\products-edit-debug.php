<?php
require '../../config/function.php';
require '../../config/dbcon.php';
require '../../includes/header.php';
include('product_audio.php'); // Include the product audio functionality

// Get product ID
$product_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$product = getById('products', $product_id);

// Get selected colors
$selected_colors = [];
$selected_colors_query = mysqli_query($conn, "SELECT DISTINCT color_id FROM product_variants WHERE product_id = {$product_id} AND color_id IS NOT NULL");
if($selected_colors_query) {
    while($row = mysqli_fetch_assoc($selected_colors_query)) {
        $selected_colors[] = $row['color_id'];
    }
}

// Get selected sizes
$selected_sizes = [];
$selected_sizes_query = mysqli_query($conn, "SELECT DISTINCT size_id FROM product_variants WHERE product_id = {$product_id} AND size_id IS NOT NULL");
if($selected_sizes_query) {
    while($row = mysqli_fetch_assoc($selected_sizes_query)) {
        $selected_sizes[] = $row['size_id'];
    }
}

// Get all colors
$colors = [];
$colors_query = mysqli_query($conn, "SELECT * FROM product_colors WHERE status = 0 ORDER BY name ASC");
if($colors_query && mysqli_num_rows($colors_query) > 0) {
    while($row = mysqli_fetch_assoc($colors_query)) {
        $colors[] = $row;
    }
}

// Get all sizes
$sizes = [];
$sizes_query = mysqli_query($conn, "SELECT * FROM product_sizes WHERE status = 0 ORDER BY name ASC");
if($sizes_query && mysqli_num_rows($sizes_query) > 0) {
    while($row = mysqli_fetch_assoc($sizes_query)) {
        $sizes[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Product Variants - Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Edit Product Variants - Debug</h1>
        <p>Product: <?= htmlspecialchars($product['data']['name']) ?> (ID: <?= $product_id ?>)</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Colors and Sizes</h5>
                    </div>
                    <div class="card-body">
                        <input type="hidden" name="product_id" value="<?= $product_id ?>">
                        
                        <h6>Colors</h6>
                        <div class="mb-3">
                            <?php foreach($colors as $color): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="variant_colors[]" value="<?= $color['id'] ?>" id="color<?= $color['id'] ?>" <?= in_array($color['id'], $selected_colors) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="color<?= $color['id'] ?>">
                                        <span style="display: inline-block; width: 20px; height: 20px; background-color: <?= $color['color_code'] ?>; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                                        <?= htmlspecialchars($color['name']) ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <h6>Sizes</h6>
                        <div class="mb-3">
                            <?php foreach($sizes as $size): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="variant_sizes[]" value="<?= $size['id'] ?>" id="size<?= $size['id'] ?>" <?= in_array($size['id'], $selected_sizes) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="size<?= $size['id'] ?>">
                                        <?= htmlspecialchars($size['name']) ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoRefreshVariants" checked>
                            <label class="form-check-label" for="autoRefreshVariants">
                                Auto-refresh variants when colors or sizes are changed
                            </label>
                        </div>
                        
                        <button type="button" id="refreshVariantsBtn" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i> Refresh Variants
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Variants</h5>
                    </div>
                    <div class="card-body">
                        <div id="variantsTableContainer">
                            <!-- Variants will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="products-edit.php?id=<?= $product_id ?>" class="btn btn-secondary">Back to Edit Product</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Function to refresh the variants table
        function refreshVariantsTable() {
            console.log('Refreshing variants table...');
            const productId = document.querySelector('input[name="product_id"]').value;
            console.log('Product ID:', productId);
            
            const colorCheckboxes = document.querySelectorAll('input[name="variant_colors[]"]:checked');
            console.log('Checked color checkboxes:', colorCheckboxes.length);
            
            const sizeCheckboxes = document.querySelectorAll('input[name="variant_sizes[]"]:checked');
            console.log('Checked size checkboxes:', sizeCheckboxes.length);
            
            // Get selected color IDs
            const colorIds = [];
            colorCheckboxes.forEach(function(checkbox) {
                colorIds.push(checkbox.value);
            });
            console.log('Selected color IDs:', colorIds);
            
            // Get selected size IDs
            const sizeIds = [];
            sizeCheckboxes.forEach(function(checkbox) {
                sizeIds.push(checkbox.value);
            });
            console.log('Selected size IDs:', sizeIds);
            
            // Build the URL
            let url = `dynamic_variants.php?product_id=${productId}`;
            
            if (colorIds.length > 0) {
                colorIds.forEach(function(id) {
                    url += `&colors[]=${id}`;
                });
            }
            
            if (sizeIds.length > 0) {
                sizeIds.forEach(function(id) {
                    url += `&sizes[]=${id}`;
                });
            }
            
            console.log('Request URL:', url);
            
            // Fetch the updated variants table
            fetch(url)
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.error) {
                        console.error('Error:', data.error);
                        return;
                    }
                    
                    // Update the variants table
                    const variantsTableContainer = document.getElementById('variantsTableContainer');
                    if (variantsTableContainer) {
                        variantsTableContainer.innerHTML = data.html;
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                });
        }
        
        // Initialize when the DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page initialized');
            
            // Get elements
            const refreshVariantsBtn = document.getElementById('refreshVariantsBtn');
            const autoRefreshCheckbox = document.getElementById('autoRefreshVariants');
            const colorCheckboxes = document.querySelectorAll('input[name="variant_colors[]"]');
            const sizeCheckboxes = document.querySelectorAll('input[name="variant_sizes[]"]');
            
            console.log('Refresh button:', refreshVariantsBtn);
            console.log('Auto-refresh checkbox:', autoRefreshCheckbox);
            console.log('Color checkboxes:', colorCheckboxes.length);
            console.log('Size checkboxes:', sizeCheckboxes.length);
            
            // Add event listener to refresh button
            if (refreshVariantsBtn) {
                console.log('Adding click event to refresh button');
                refreshVariantsBtn.addEventListener('click', function() {
                    console.log('Refresh button clicked');
                    refreshVariantsTable();
                });
            }
            
            // Add event listeners to color and size checkboxes
            colorCheckboxes.forEach(function(checkbox) {
                console.log('Adding change event to color checkbox:', checkbox.id);
                checkbox.addEventListener('change', function() {
                    console.log('Color checkbox changed:', this.id, 'Checked:', this.checked);
                    // Auto-refresh when a checkbox is changed
                    if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
                        console.log('Auto-refreshing variants...');
                        refreshVariantsTable();
                    }
                });
            });
            
            sizeCheckboxes.forEach(function(checkbox) {
                console.log('Adding change event to size checkbox:', checkbox.id);
                checkbox.addEventListener('change', function() {
                    console.log('Size checkbox changed:', this.id, 'Checked:', this.checked);
                    // Auto-refresh when a checkbox is changed
                    if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
                        console.log('Auto-refreshing variants...');
                        refreshVariantsTable();
                    }
                });
            });
            
            // Initial refresh
            console.log('Performing initial refresh...');
            refreshVariantsTable();
        });
    </script>
</body>
</html>
