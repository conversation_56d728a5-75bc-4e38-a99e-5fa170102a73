<?php
require_once('../../config/config.php');
?>

<?php
session_start();
require '../../config/function.php';
require '../../config/dbcon.php';

// Check authentication
if(!isset($_SESSION['loggedIn']))
{
    redirect('../../auth/login.php','Login to continue...');
    exit();
}

include('../../includes/header.php');
include('../../includes/grid_table_top.php');

$stats_query = "SELECT
    COUNT(*) as total_orders,
    COUNT(CASE WHEN EXISTS (
        SELECT 1 FROM order_items oi
        WHERE oi.tracking_no = o.tracking_no
    ) THEN 1 END) as orders_with_items,
    SUM(CASE
        WHEN EXISTS (
            SELECT 1 FROM order_items oi
            WHERE oi.tracking_no = o.tracking_no
        ) AND payment_status = 'Paid'
        THEN 1 ELSE 0
    END) as paid_orders,
    SUM(CASE
        WHEN EXISTS (
            SELECT 1 FROM order_items oi
            WHERE oi.tracking_no = o.tracking_no
        ) AND payment_status = 'Not Paid'
        THEN 1 ELSE 0
    END) as unpaid_orders
FROM orders o";
$stats_result = mysqli_query($conn, $stats_query);
$stats = mysqli_fetch_assoc($stats_result);

// Add order status history tracking
$statusHistory = [
    'Pending' => ['icon' => 'clock', 'next' => ['Processing', 'Cancelled']],
    'Processing' => ['icon' => 'cogs', 'next' => ['Completed', 'Cancelled']],
    'Completed' => ['icon' => 'check-circle', 'next' => []],
    'Cancelled' => ['icon' => 'times-circle', 'next' => []]
];
?>

<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
<!-- Add this after DataTables CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<style>
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 99999;
    min-width: 300px;
    max-width: 400px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.5s ease-out;
    background: #fff;
    padding: 15px;
    border-radius: 4px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Update the table-secondary styling to be more visible */
.table-secondary {
    background-color: #f8f9fa !important; /* Lighter background */
    border-left: 4px solid #dc3545 !important; /* Red indicator */
}

/* Keep the hover effect */
.table-secondary:hover {
    background-color: #e2e3e5 !important;
    cursor: pointer;
}

/* Add subtle box shadow for depth */
.table-secondary td {
    position: relative;
}

.table-secondary td:first-child::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #dc3545;
}

.customer-list-container {
    max-height: 400px;
    overflow-y: auto;
}
.customer-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}
.customer-item:hover {
    background-color: #f8f9fa;
}

.order-row-indicator {
    width: 4px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
}

.table > tbody > tr {
    position: relative;
}

.table > tbody > tr.incomplete-order {
    background-color: rgba(220, 53, 69, 0.05);
}

.table > tbody > tr.paid-order {
    background-color: rgba(40, 167, 69, 0.05);
}

.order-row-indicator.incomplete {
    background-color: #dc3545;
}

.order-row-indicator.paid {
    background-color: #28a745;
}
</style>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Orders List
                <button type="button" id="showCustomerList" class="btn btn-primary float-end">
                    <i class="fas fa-plus"></i> Create New Order
                </button>
            </h4>
        </div>

        <!-- Customer List Section (initially hidden) -->
        <div id="customerListSection" class="card mt-3" style="display: none;">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Select Customer</h4>
                    <button type="button" class="btn-close" id="hideCustomerList" aria-label="Close"></button>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="input-group">
                        <input type="text"
                               id="customerSearch"
                               class="form-control"
                               placeholder="Search customer by name, phone, or email...">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                    </div>
                </div>
                <div id="customerList" class="customer-list-container">
                    <!-- Customers will be loaded here -->
                </div>
            </div>
        </div>

        <div class="card-body">
            <?php alertMessage(); ?>

            <?php
            $orders_query = "SELECT o.*, c.name as customer_name
                      FROM orders o
                      LEFT JOIN customers c ON o.customer_id = c.id
                      ORDER BY o.date DESC";
            $orders_run = mysqli_query($conn, $orders_query);

            if(!$orders_run){
                echo '<h4>Something Went Wrong!</h4>';
                return false;
            }

            if(mysqli_num_rows($orders_run) > 0)
            {
            ?>
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-muted mb-1">Total Orders</h6>
                            <h4 class="mb-0 d-flex align-items-center">
                                <?= $stats['total_orders'] ?>
                                <small class="text-muted ms-2" style="font-size: 14px;">
                                    (Including <?= $stats['total_orders'] - $stats['orders_with_items'] ?> incomplete)
                                </small>
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-primary mb-1">Orders With Items</h6>
                            <h4 class="mb-0"><?= $stats['orders_with_items'] ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-success mb-1">Paid Orders</h6>
                            <h4 class="mb-0"><?= $stats['paid_orders'] ?></h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-danger mb-1">Unpaid Orders</h6>
                            <h4 class="mb-0 d-flex align-items-center">
                                <?= $stats['unpaid_orders'] ?>
                                <small class="text-muted ms-2" style="font-size: 14px;">
                                    (With items only)
                                </small>
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-bordered" id="example">
                    <thead>
                        <tr>
                            <th>Invoice Number</th>
                            <th>Date</th>
                            <th>Tracking Number</th>
                            <th>Total Amount</th>
                            <th>Order Status</th>
                            <th>Payment Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($order = mysqli_fetch_assoc($orders_run)):
                            $tracking_no = $order['tracking_no'];
                            $query = "SELECT COUNT(*) as item_count FROM order_items WHERE tracking_no = ?";
                            $stmt = mysqli_prepare($conn, $query);
                            mysqli_stmt_bind_param($stmt, "s", $tracking_no);
                            mysqli_stmt_execute($stmt);
                            $result = mysqli_stmt_get_result($stmt);
                            $item_count = mysqli_fetch_assoc($result)['item_count'];

                            // Build tooltip content
                            $tooltipContent = [];
                            if($item_count == 0) {
                                $tooltipContent[] = "Incomplete Order - No items added";
                            }
                            if(!empty($order['issue_date'])) {
                                $tooltipContent[] = "Invoice issued: " . date('d M Y', strtotime($order['issue_date']));
                            }
                            if(!empty($order['payment_date'])) {
                                $tooltipContent[] = "Payment received: " . date('d M Y', strtotime($order['payment_date']));
                            }
                            $tooltipTitle = implode(' | ', $tooltipContent);

                            $rowClass = '';
                            if ($item_count == 0) {
                                $rowClass = 'incomplete-order';
                            } elseif ($order['payment_status'] == 'Paid') {
                                $rowClass = 'paid-order';
                            }
                        ?>
                            <tr class="<?= $rowClass ?>" <?= !empty($tooltipTitle) ? 'data-bs-toggle="tooltip" title="' . htmlspecialchars($tooltipTitle) . '"' : '' ?>>
                                <td class="position-relative">
                                    <?php if ($item_count == 0): ?>
                                        <div class="order-row-indicator incomplete"></div>
                                    <?php elseif ($order['payment_status'] == 'Paid'): ?>
                                        <div class="order-row-indicator paid"></div>
                                    <?php endif; ?>
                                    <?= $order['id'] ?>
                                </td>
                                <td><?= date('d M Y', strtotime($order['date'])) ?></td>
                                <td><?= $order['tracking_no'] ?></td>
                                <td>
                                    <?php if($item_count == 0): ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-exclamation-triangle"></i> Incomplete Order
                                        </span>
                                        <a href="orders-create-add-items.php?id=<?= $order['id'] ?>"
                                           class="btn btn-success btn-sm">
                                            <i class="fas fa-plus"></i> Add Items
                                        </a>
                                    <?php else: ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> <?= $item_count ?> Items
                                        </span>
                                        <span class="ms-2">R<?= number_format($order['total_amount'], 2) ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    switch($order['order_status']) {
                                        case 'Pending':
                                            echo '<span class="badge bg-warning">Pending</span>';
                                            break;
                                        case 'Processing':
                                            echo '<span class="badge bg-primary">Processing</span>';
                                            break;
                                        case 'Completed':
                                            echo '<span class="badge bg-success">Completed</span>';
                                            break;
                                        case 'Cancelled':
                                            echo '<span class="badge bg-danger">Cancelled</span>';
                                            break;
                                        default:
                                            echo '<span class="badge bg-secondary">'.$order['order_status'].'</span>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    if($order['payment_status'] == 'Not Paid') {
                                        echo '<span class="badge bg-danger">Not Paid</span>';
                                    } elseif ($order['payment_status'] == 'Paid') {
                                        // Build tooltip content with available information
                                        $tooltipInfo = [];

                                        // Add issue date if available
                                        if (!empty($order['issue_date'])) {
                                            $tooltipInfo[] = 'Invoice issued: ' . date('d M Y', strtotime($order['issue_date']));
                                        }

                                        // Add payment date if available
                                        if (!empty($order['payment_date'])) {
                                            $tooltipInfo[] = 'Paid on: ' . date('d M Y', strtotime($order['payment_date']));
                                        }

                                        // Always show payment mode since it has a default value
                                        $tooltipInfo[] = 'Method: ' . ucfirst(htmlspecialchars($order['payment_mode']));

                                        $tooltipTitle = implode(' | ', $tooltipInfo);

                                        echo '<span class="badge bg-success" data-bs-toggle="tooltip" title="' . $tooltipTitle . '">Paid</span>';
                                    } else {
                                        error_log("Unexpected payment status: " . $order['payment_status']);
                                        echo '<span class="badge bg-warning">Status Error</span>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <div class="d-flex gap-2 flex-wrap">
                                        <a href="orders_items.php?id=<?= $order['id']; ?>&tracking_no=<?= $order['tracking_no']; ?>"
                                           class="btn btn-primary btn-sm" data-bs-toggle="tooltip" title="View Order Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-success btn-sm"
                                                onclick="openStatusModal(<?= $order['id'] ?>, '<?= $order['order_status'] ?>', '<?= $order['payment_status'] ?>')"
                                                data-bs-toggle="tooltip" title="Update Order Status">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                        <a href="order_invoice.php?id=<?= $order['id'];?>&tracking_no=<?= $order['tracking_no']; ?>"
                                           class="btn btn-warning btn-sm <?= ($item_count == 0) ? 'disabled' : '' ?>"
                                           data-bs-toggle="tooltip" title="<?= ($item_count == 0) ? 'Cannot generate invoice for incomplete order' : 'Generate Invoice' ?>">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                        <?php if($item_count > 0): ?>
                                        <button type="button" class="btn btn-info btn-sm" onclick="printOrder(<?= $order['id'] ?>)" data-bs-toggle="tooltip" title="Print Order">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
            <?php
            }
            else
            {
                ?>
                <h4 class="mb-0">No Record found</h4>
                <?php
            }
            ?>
        </div>
    </div>
</div>

<!-- jQuery first -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>

<!-- PDF Generation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<!-- Your custom scripts -->
<script>
// Function to print order
function printOrder(orderId) {
    window.open('order_print.php?id=' + orderId, '_blank', 'width=800,height=600');
}

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Your existing DataTable initialization
    $('#example').DataTable({
        responsive: true,
        order: [[1, 'desc']],
        pageLength: 25,
        language: {
            search: "Search orders:"
        }
    });
});

$(document).ready(function() {
    // Show customer list section
    $('#showCustomerList').click(function() {
        $('#customerListSection').slideDown();
        loadCustomers();
        $('#customerSearch').focus();
    });

    // Hide customer list section
    $('#hideCustomerList').click(function() {
        $('#customerListSection').slideUp();
        $('#customerSearch').val('');
    });

    // Handle customer search with debounce
    let searchTimeout;
    $('#customerSearch').on('keyup', function() {
        clearTimeout(searchTimeout);
        const search = $(this).val();
        searchTimeout = setTimeout(() => {
            loadCustomers(search);
        }, 300);
    });
});

function loadCustomers(search = '') {
    $.ajax({
        url: 'get-customers.php',
        method: 'POST',
        data: { search: search },
        beforeSend: function() {
            $('#customerList').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
        },
        success: function(response) {
            $('#customerList').html(response);
        },
        error: function() {
            $('#customerList').html('<div class="alert alert-danger">Error loading customers</div>');
        }
    });
}

function selectCustomer(customerId) {
    // Add loading state to button
    const $button = $(event.target).closest('button');
    $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Selecting...');

    // Check session and redirect
    $.get('../../includes/check_session.php')
        .done(function(response) {
            if (response.valid) {
                window.location.href = 'create-invoice.php?customer_id=' + customerId;
            } else {
                window.location.href = '/webapp/admin/auth/login.php';
            }
        })
        .fail(function() {
            window.location.href = '/webapp/admin/auth/login.php';
        });
}

function openStatusModal(orderId, currentOrderStatus, currentPaymentStatus) {
    const modal = document.getElementById('updateStatusModal');
    if (!modal) return;

    const orderIdInput = document.getElementById('orderIdInput');
    const orderStatusSelect = modal.querySelector('select[name="order_status"]');
    const paymentStatusSelect = modal.querySelector('select[name="payment_status"]');

    // Normalize payment status for the dropdown
    let normalizedPaymentStatus = currentPaymentStatus;
    if (currentPaymentStatus === 'pending') {
        normalizedPaymentStatus = 'Not Paid';

        // Add 'pending' option if it doesn't exist
        let pendingOption = Array.from(paymentStatusSelect.options).find(opt => opt.value === 'pending');
        if (!pendingOption) {
            const option = document.createElement('option');
            option.value = 'pending';
            option.text = 'Pending';
            paymentStatusSelect.add(option);
        }
    }

    orderIdInput.value = orderId;
    orderStatusSelect.value = currentOrderStatus || 'Pending';
    paymentStatusSelect.value = normalizedPaymentStatus || 'Not Paid';

    // Log for debugging
    console.log(`Opening modal for order ${orderId}:`, {
        orderStatus: currentOrderStatus,
        paymentStatus: currentPaymentStatus,
        normalizedPaymentStatus
    });

    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

function showNotification(message, type = 'success') {
    // Remove any existing notifications
    $('.notification').remove();

    // Create the notification element
    const notification = $(`
        <div class="notification alert alert-${type} alert-dismissible fade show">
            <div class="d-flex align-items-center">
                <strong class="me-2">
                    ${type === 'success'
                        ? '<i class="fas fa-check-circle"></i> Success!'
                        : '<i class="fas fa-exclamation-circle"></i> Error!'}
                </strong>
                ${message}
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `).appendTo('body');

    // Automatically close after 3 seconds
    setTimeout(() => {
        notification.fadeOut(300, function() {
            $(this).remove();
        });
    }, 3000);
}

// Status update form handler is defined below in the modal section

// Initialize jsPDF if it's needed
if (typeof window.jspdf !== 'undefined') {
    window.jsPDF = window.jspdf.jsPDF;
}
</script>

<?php include('../../includes/grid_table_bottom.php'); ?>
<?php include('../../includes/footer.php'); ?>

<!-- Status Update Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Order Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="updateStatusForm">
                <div class="modal-body">
                    <input type="hidden" id="orderIdInput" name="orderId">
                    <div class="mb-3">
                        <label>Order Status</label>
                        <select name="order_status" class="form-select">
                            <option value="Pending">Pending</option>
                            <option value="Processing">Processing</option>
                            <option value="Completed">Completed</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label>Payment Status</label>
                        <select name="payment_status" class="form-select">
                            <option value="Not Paid">Not Paid</option>
                            <option value="Paid">Paid</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#updateStatusForm').on('submit', function(e) {
        e.preventDefault();

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');

        const formData = new FormData(this);

        // Log form data for debugging
        console.log('Submitting order status update:');
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }

        fetch('update_order_status.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => {
                    throw new Error(err.message || 'Server error');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                showNotification(data.message || 'Order status updated successfully', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                throw new Error(data.message || 'Unknown error');
            }
        })
        .catch(error => {
            console.error('Error updating order status:', error);
            showNotification('Error: ' + error.message, 'danger');

            // Reset button
            submitBtn.prop('disabled', false).html(originalText);
        });
    });
</script>

<script>
const BASE_URL = '<?= $BASE_URL ?>';

const voiceControl = new VoiceControl({
    commands: {
        'create invoice': () => {
            console.log('Voice command received: create invoice');
            try {
                $('#showCustomerList').trigger('click');
            } catch (error) {
                console.error('Error executing create invoice command:', error);
            }
        }
    }
});

// Add help tooltip/popup
const helpLink = document.createElement('div');
helpLink.innerHTML = `
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="fas fa-info-circle"></i>
        Voice command available: "create invoice" - Opens customer selection
        <a href="${BASE_URL}/admin/help/voice-commands.php" class="alert-link">View all commands</a>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
`;
document.querySelector('.container-fluid').prepend(helpLink);

</script>
