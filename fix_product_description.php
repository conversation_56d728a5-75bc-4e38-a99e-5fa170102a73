<?php
// Include database connection
require_once('admin/config/dbcon.php');

// Get the product ID from the URL
$product_id = isset($_GET['id']) ? intval($_GET['id']) : 10; // Default to product ID 10

// Get current description
$query = "SELECT description FROM products WHERE id = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, "i", $product_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$product = mysqli_fetch_assoc($result);

echo "<h2>Product ID: $product_id</h2>";
echo "<h3>Current Description (Raw):</h3>";
echo "<pre>" . htmlspecialchars($product['description']) . "</pre>";

// Fix the description by decoding HTML entities
$fixed_description = html_entity_decode($product['description']);

echo "<h3>Fixed Description (Raw):</h3>";
echo "<pre>" . htmlspecialchars($fixed_description) . "</pre>";

// Update the database with the fixed description
$update_query = "UPDATE products SET description = ? WHERE id = ?";
$update_stmt = mysqli_prepare($conn, $update_query);
mysqli_stmt_bind_param($update_stmt, "si", $fixed_description, $product_id);
$success = mysqli_stmt_execute($update_stmt);

if ($success) {
    echo "<div style='color: green; font-weight: bold; margin-top: 20px;'>Description updated successfully!</div>";
} else {
    echo "<div style='color: red; font-weight: bold; margin-top: 20px;'>Error updating description: " . mysqli_error($conn) . "</div>";
}

echo "<div style='margin-top: 20px;'>";
echo "<a href='admin/modules/products/products-edit.php?id=$product_id' style='padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;'>Go to Product Edit Page</a>";
echo "</div>";
?>
