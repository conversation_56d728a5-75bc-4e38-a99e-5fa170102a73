<?php 
include('includes/header.php');
require_once 'includes/functions.php';

// Validate and sanitize input
$id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if(!$id) {
    redirect('suppliers.php', 'Invalid supplier ID provided');
}

// Get supplier details
$supplier = getById('supplier', $id);
if($supplier['status'] !== 200) {
    redirect('suppliers.php', 'Supplier not found');
}
$supplierData = $supplier['data'];
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Supplier Details</h4>
                <div>
                    <a href="suppliers.php" class="btn btn-danger">
                        <i class="fas fa-arrow-left"></i> Back
                    </a>
                    <a href="supplier-edit.php?id=<?= $id ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <tr>
                                    <th width="200">Company Name:</th>
                                    <td><?= htmlspecialchars($supplierData['supplier_company_name']) ?></td>
                                </tr>
                                <tr>
                                    <th>Email:</th>
                                    <td>
                                        <a href="mailto:<?= htmlspecialchars($supplierData['supplier_email']) ?>">
                                            <?= htmlspecialchars($supplierData['supplier_email']) ?>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Phone:</th>
                                    <td>
                                        <a href="tel:<?= htmlspecialchars($supplierData['supplier_phone']) ?>">
                                            <?= htmlspecialchars($supplierData['supplier_phone']) ?>
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <?php
                                        $status = $supplierData['status'] ?? 1;
                                        $statusClass = $status ? 'success' : 'danger';
                                        $statusText = $status ? 'Active' : 'Inactive';
                                        echo "<span class='badge bg-$statusClass'>$statusText</span>";
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Address:</th>
                                    <td><?= nl2br(htmlspecialchars($supplierData['address'] ?? '')) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">Order Statistics</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $orderStats = getSupplierOrderStats($id);
                            ?>
                            <div class="row g-3">
                                <div class="col-sm-6">
                                    <div class="p-3 bg-primary bg-opacity-10 rounded">
                                        <h6>Total Orders</h6>
                                        <h2><?= $orderStats['total'] ?? 0 ?></h2>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="p-3 bg-success bg-opacity-10 rounded">
                                        <h6>Completed Orders</h6>
                                        <h2><?= $orderStats['completed'] ?? 0 ?></h2>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="p-3 bg-warning bg-opacity-10 rounded">
                                        <h6>Pending Orders</h6>
                                        <h2><?= $orderStats['pending'] ?? 0 ?></h2>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="p-3 bg-info bg-opacity-10 rounded">
                                        <h6>Total Amount</h6>
                                        <h2>$<?= number_format($orderStats['total_amount'] ?? 0, 2) ?></h2>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <a href="supplier-view-order.php?id=<?= $id ?>" class="btn btn-primary">
                                    <i class="fas fa-list"></i> View All Orders
                                </a>
                                <a href="supplier-order-add.php?id=<?= $id ?>" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Create New Order
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>
