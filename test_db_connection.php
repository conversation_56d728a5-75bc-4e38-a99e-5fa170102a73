<?php
// Display any PHP errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Testing Database Connection</h1>";

// Try to connect to the database
try {
    $conn = mysqli_connect("localhost", "customap1_webapp", "n?r65P5bWxKpNRAp", "customap1_webapp");
    
    if ($conn) {
        echo "<p style='color:green'>Database connection successful!</p>";
        
        // Test if we can create a simple table
        $test_table = "CREATE TABLE IF NOT EXISTS `test_table` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
        
        if (mysqli_query($conn, $test_table)) {
            echo "<p style='color:green'>Test table created successfully!</p>";
            
            // Now try to create the product_types table
            $sql_product_types = "CREATE TABLE IF NOT EXISTS `product_types` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(100) NOT NULL,
              `description` text DEFAULT NULL,
              `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
            
            if (mysqli_query($conn, $sql_product_types)) {
                echo "<p style='color:green'>product_types table created successfully!</p>";
                
                // Insert default product types
                $sql_insert_types = "INSERT INTO `product_types` (`name`, `description`, `status`) 
                                     SELECT * FROM (SELECT 'Standard', 'Regular product', 0) AS tmp
                                     WHERE NOT EXISTS (
                                         SELECT name FROM `product_types` WHERE name = 'Standard'
                                     ) LIMIT 1;
                                     
                                     INSERT INTO `product_types` (`name`, `description`, `status`) 
                                     SELECT * FROM (SELECT 'Buildable', 'Product that can be built from components', 0) AS tmp
                                     WHERE NOT EXISTS (
                                         SELECT name FROM `product_types` WHERE name = 'Buildable'
                                     ) LIMIT 1;
                                     
                                     INSERT INTO `product_types` (`name`, `description`, `status`) 
                                     SELECT * FROM (SELECT 'Component', 'Can be used as a component in buildable products', 0) AS tmp
                                     WHERE NOT EXISTS (
                                         SELECT name FROM `product_types` WHERE name = 'Component'
                                     ) LIMIT 1;";
                
                if (mysqli_multi_query($conn, $sql_insert_types)) {
                    echo "<p style='color:green'>Default product types inserted successfully!</p>";
                    
                    // Clear results to execute next query
                    while (mysqli_next_result($conn)) {;}
                    
                    // Check if the product types were inserted
                    $result = mysqli_query($conn, "SELECT * FROM product_types");
                    if (mysqli_num_rows($result) > 0) {
                        echo "<h2>Product Types:</h2>";
                        echo "<ul>";
                        while ($row = mysqli_fetch_assoc($result)) {
                            echo "<li>" . $row['name'] . " - " . $row['description'] . "</li>";
                        }
                        echo "</ul>";
                    }
                } else {
                    echo "<p style='color:red'>Error inserting default product types: " . mysqli_error($conn) . "</p>";
                }
            } else {
                echo "<p style='color:red'>Error creating product_types table: " . mysqli_error($conn) . "</p>";
            }
        } else {
            echo "<p style='color:red'>Error creating test table: " . mysqli_error($conn) . "</p>";
        }
    } else {
        echo "<p style='color:red'>Database connection failed: " . mysqli_connect_error() . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>Exception: " . $e->getMessage() . "</p>";
}

echo "<p><a href='create_product_builder_tables.php' class='btn btn-primary'>Run Full Table Creation Script</a></p>";
echo "<p><a href='admin/index.php' class='btn btn-secondary'>Go to Admin Dashboard</a></p>";
?>
