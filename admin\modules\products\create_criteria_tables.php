<?php
// Include database connection
include('../../config/dbcon.php');

// Create product_colors table
$sql_colors = "CREATE TABLE IF NOT EXISTS `product_colors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

// Create product_sizes table
$sql_sizes = "CREATE TABLE IF NOT EXISTS `product_sizes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

// Execute the SQL statements
if (mysqli_query($conn, $sql_colors)) {
    echo "Table 'product_colors' created successfully or already exists.<br>";
} else {
    echo "Error creating table 'product_colors': " . mysqli_error($conn) . "<br>";
}

if (mysqli_query($conn, $sql_sizes)) {
    echo "Table 'product_sizes' created successfully or already exists.<br>";
} else {
    echo "Error creating table 'product_sizes': " . mysqli_error($conn) . "<br>";
}

// Close the connection
mysqli_close($conn);

echo "<p>Database setup completed.</p>";
echo "<p><a href='criteria.php'>Go to Criteria Management</a></p>";
?>
