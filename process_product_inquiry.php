<?php
include('admin/config/dbcon.php');

// Check if form is submitted
if(isset($_POST['product_name'])) {
    // Get form data
    $product_name = mysqli_real_escape_string($conn, $_POST['product_name']);
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $number = mysqli_real_escape_string($conn, $_POST['number']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $message = mysqli_real_escape_string($conn, $_POST['message']);
    
    // Insert into database
    $query = "INSERT INTO product_more_information (product_name, name, number, email, message) 
              VALUES ('$product_name', '$name', '$number', '$email', '$message')";
    
    $query_run = mysqli_query($conn, $query);
    
    if($query_run) {
        // Redirect to WhatsApp with the message
        $whatsapp_number = "27797869698";
        $whatsapp_message = "Hello, I'm interested in the product: " . urlencode($product_name) . ". My name is " . urlencode($name) . ".";
        $whatsapp_url = "https://wa.me/" . $whatsapp_number . "?text=" . $whatsapp_message;
        
        header("Location: $whatsapp_url");
        exit();
    } else {
        // If failed, redirect back with error
        header("Location: product_list.php?error=failed");
        exit();
    }
}

// If not submitted, redirect back
header("Location: product_list.php");
exit();
?>
