<?php 
include('includes/header.php');
require_once 'includes/functions.php';

// Validate and sanitize input
$id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if(!$id) {
    redirect('suppliers.php', 'Invalid supplier ID provided');
}

// Get supplier details
$supplier = getById('supplier', $id);
if($supplier['status'] !== 200) {
    redirect('suppliers.php', 'Supplier not found');
}
$supplierData = $supplier['data'];
?>

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.2/css/dataTables.bootstrap5.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    Orders for <?= htmlspecialchars($supplierData['supplier_company_name']) ?>
                </h4>
                <div>
                    <a href="suppliers.php" class="btn btn-danger">
                        <i class="fas fa-arrow-left"></i> Back
                    </a>
                    <a href="supplier-order-add.php?id=<?= $id ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Order
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <?php
            try {
                $orders = getSupplierOrders($id);
                if($orders && !empty($orders)):
            ?>
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="ordersTable">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Date</th>
                            <th>Items</th>
                            <th>Total Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($orders as $order): ?>
                        <tr>
                            <td><?= htmlspecialchars($order['order_id']) ?></td>
                            <td><?= date('d M Y', strtotime($order['order_date'])) ?></td>
                            <td><?= $order['item_count'] ?></td>
                            <td>$<?= number_format($order['total_amount'], 2) ?></td>
                            <td>
                                <?php
                                $statusClass = match($order['status']) {
                                    'completed' => 'success',
                                    'pending' => 'warning',
                                    'cancelled' => 'danger',
                                    default => 'secondary'
                                };
                                echo "<span class='badge bg-$statusClass'>" . ucfirst($order['status']) . "</span>";
                                ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="order-view.php?id=<?= $order['id'] ?>" 
                                       class="btn btn-info btn-sm" 
                                       title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="order-edit.php?id=<?= $order['id'] ?>" 
                                       class="btn btn-primary btn-sm"
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if($order['status'] !== 'completed'): ?>
                                    <button type="button" 
                                            class="btn btn-danger btn-sm"
                                            onclick="deleteOrder(<?= $order['id'] ?>)"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
                <div class="alert alert-info">No orders found for this supplier.</div>
            <?php 
                endif;
            } catch (Exception $e) {
                logError('Error fetching supplier orders: ' . $e->getMessage());
                echo '<div class="alert alert-danger">An error occurred while fetching orders.</div>';
            }
            ?>
        </div>
    </div>
</div>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/2.0.2/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/2.0.2/js/dataTables.bootstrap5.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>

<script>
$(document).ready(function() {
    $('#ordersTable').DataTable({
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'copyHtml5',
                text: '<i class="fas fa-copy"></i> Copy',
                className: 'btn btn-secondary btn-sm'
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdfHtml5',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm',
                orientation: 'landscape'
            }
        ],
        order: [[1, 'desc']],
        pageLength: 25
    });
});

function deleteOrder(orderId) {
    if(confirm('Are you sure you want to delete this order?')) {
        window.location.href = `order-delete.php?id=${orderId}`;
    }
}
</script>

</div>
