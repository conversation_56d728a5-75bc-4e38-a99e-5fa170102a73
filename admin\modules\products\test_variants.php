<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Variants</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Variants</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Colors and Sizes</h5>
                    </div>
                    <div class="card-body">
                        <input type="hidden" name="product_id" value="29">
                        
                        <h6>Colors</h6>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="variant_colors[]" value="1" id="color1" checked>
                                <label class="form-check-label" for="color1">
                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: #FF0000; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                                    Red
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="variant_colors[]" value="2" id="color2" checked>
                                <label class="form-check-label" for="color2">
                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: #00FF00; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                                    Green
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="variant_colors[]" value="3" id="color3" checked>
                                <label class="form-check-label" for="color3">
                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: #000000; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                                    Black
                                </label>
                            </div>
                        </div>
                        
                        <h6>Sizes</h6>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="variant_sizes[]" value="1" id="size1" checked>
                                <label class="form-check-label" for="size1">
                                    Small
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="variant_sizes[]" value="2" id="size2" checked>
                                <label class="form-check-label" for="size2">
                                    Small Medium
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoRefreshVariants" checked>
                            <label class="form-check-label" for="autoRefreshVariants">
                                Auto-refresh variants when colors or sizes are changed
                            </label>
                        </div>
                        
                        <button type="button" id="refreshVariantsBtn" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i> Refresh Variants
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Variants</h5>
                    </div>
                    <div class="card-body">
                        <div id="variantsTableContainer">
                            <!-- Variants will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Function to refresh the variants table
        function refreshVariantsTable() {
            console.log('Refreshing variants table...');
            const productId = document.querySelector('input[name="product_id"]').value;
            console.log('Product ID:', productId);
            
            const colorCheckboxes = document.querySelectorAll('input[name="variant_colors[]"]:checked');
            console.log('Checked color checkboxes:', colorCheckboxes.length);
            
            const sizeCheckboxes = document.querySelectorAll('input[name="variant_sizes[]"]:checked');
            console.log('Checked size checkboxes:', sizeCheckboxes.length);
            
            // Get selected color IDs
            const colorIds = [];
            colorCheckboxes.forEach(function(checkbox) {
                colorIds.push(checkbox.value);
            });
            console.log('Selected color IDs:', colorIds);
            
            // Get selected size IDs
            const sizeIds = [];
            sizeCheckboxes.forEach(function(checkbox) {
                sizeIds.push(checkbox.value);
            });
            console.log('Selected size IDs:', sizeIds);
            
            // Build the URL
            let url = `dynamic_variants.php?product_id=${productId}`;
            
            if (colorIds.length > 0) {
                colorIds.forEach(function(id) {
                    url += `&colors[]=${id}`;
                });
            }
            
            if (sizeIds.length > 0) {
                sizeIds.forEach(function(id) {
                    url += `&sizes[]=${id}`;
                });
            }
            
            console.log('Request URL:', url);
            
            // Fetch the updated variants table
            fetch(url)
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.error) {
                        console.error('Error:', data.error);
                        return;
                    }
                    
                    // Update the variants table
                    const variantsTableContainer = document.getElementById('variantsTableContainer');
                    if (variantsTableContainer) {
                        variantsTableContainer.innerHTML = data.html;
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                });
        }
        
        // Initialize when the DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page initialized');
            
            // Get elements
            const refreshVariantsBtn = document.getElementById('refreshVariantsBtn');
            const autoRefreshCheckbox = document.getElementById('autoRefreshVariants');
            const colorCheckboxes = document.querySelectorAll('input[name="variant_colors[]"]');
            const sizeCheckboxes = document.querySelectorAll('input[name="variant_sizes[]"]');
            
            // Add event listener to refresh button
            if (refreshVariantsBtn) {
                console.log('Adding click event to refresh button');
                refreshVariantsBtn.addEventListener('click', function() {
                    console.log('Refresh button clicked');
                    refreshVariantsTable();
                });
            }
            
            // Add event listeners to color and size checkboxes
            colorCheckboxes.forEach(function(checkbox) {
                console.log('Adding change event to color checkbox:', checkbox.id);
                checkbox.addEventListener('change', function() {
                    console.log('Color checkbox changed:', this.id, 'Checked:', this.checked);
                    // Auto-refresh when a checkbox is changed
                    if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
                        console.log('Auto-refreshing variants...');
                        refreshVariantsTable();
                    }
                });
            });
            
            sizeCheckboxes.forEach(function(checkbox) {
                console.log('Adding change event to size checkbox:', checkbox.id);
                checkbox.addEventListener('change', function() {
                    console.log('Size checkbox changed:', this.id, 'Checked:', this.checked);
                    // Auto-refresh when a checkbox is changed
                    if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
                        console.log('Auto-refreshing variants...');
                        refreshVariantsTable();
                    }
                });
            });
            
            // Initial refresh
            console.log('Performing initial refresh...');
            refreshVariantsTable();
        });
    </script>
</body>
</html>
