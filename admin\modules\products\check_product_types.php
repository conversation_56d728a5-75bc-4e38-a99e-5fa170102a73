<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Check if product_types table exists
$sql = "SHOW TABLES LIKE 'product_types'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>product_types table exists.</p>";
    
    // Show table structure
    $sql = "DESCRIBE product_types";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>product_types table structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Show existing product types
    $sql = "SELECT * FROM product_types";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>Existing product types:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Status</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . ($row['status'] == 0 ? 'Visible' : 'Hidden') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>product_types table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE product_types (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description TEXT,
        status TINYINT NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if(mysqli_query($conn, $sql)) {
        echo "<p>product_types table created successfully.</p>";
        
        // Insert default product types
        $types = [
            ['Standard', 'Regular product'],
            ['Buildable', 'Product that can be built from components'],
            ['Component', 'Can be used in buildable products']
        ];
        
        foreach($types as $type) {
            $sql = "INSERT INTO product_types (name, description) VALUES ('{$type[0]}', '{$type[1]}')";
            mysqli_query($conn, $sql);
        }
        
        echo "<p>Default product types created.</p>";
    } else {
        echo "<p>Error creating product_types table: " . mysqli_error($conn) . "</p>";
    }
}

// Now let's fix the product title in the edit page
echo "<h3>Checking products-edit.php file:</h3>";

// Get the content of the file
$file_path = __DIR__ . '/products-edit.php';
$file_content = file_get_contents($file_path);

// Check if the title needs to be updated
if(strpos($file_content, '<h4 class="mb-0">Edit Product') !== false) {
    echo "<p>Found the title section that needs to be updated.</p>";
    
    // Create a backup of the file
    $backup_path = __DIR__ . '/products-edit.php.bak';
    file_put_contents($backup_path, $file_content);
    echo "<p>Created a backup of the file at: " . $backup_path . "</p>";
    
    // Update the title section
    $old_title = '<h4 class="mb-0">Edit Product
                <a href="products.php" class="btn btn-danger float-end">Back</a>
            </h4>';
    
    $new_title = '<h4 class="mb-0">Edit Product: <?= htmlspecialchars($product[\'data\'][\'name\'] ?? \'\') ?>
                <a href="products.php" class="btn btn-danger float-end">Back</a>
            </h4>';
    
    $updated_content = str_replace($old_title, $new_title, $file_content);
    
    // Save the updated file
    if(file_put_contents($file_path, $updated_content)) {
        echo "<p>Successfully updated the title section in products-edit.php</p>";
    } else {
        echo "<p>Failed to update the file.</p>";
    }
} else {
    echo "<p>Could not find the title section in the file.</p>";
}
?>
