<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Check if product_colors table exists
$sql = "SHOW TABLES LIKE 'product_colors'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>product_colors table exists.</p>";
    
    // Show table structure
    $sql = "DESCRIBE product_colors";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>product_colors table structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Show existing colors
    $sql = "SELECT * FROM product_colors";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>Existing product colors:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Hex Code</th><th>Status</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        $hex_code = isset($row['hex_code']) ? $row['hex_code'] : (isset($row['color_code']) ? $row['color_code'] : '#cccccc');
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td style='background-color:" . $hex_code . "'>" . $hex_code . "</td>";
        echo "<td>" . ($row['status'] == 0 ? 'Visible' : 'Hidden') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>product_colors table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE product_colors (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        hex_code VARCHAR(20) NOT NULL DEFAULT '#000000',
        status TINYINT NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if(mysqli_query($conn, $sql)) {
        echo "<p>product_colors table created successfully.</p>";
        
        // Insert some default colors
        $colors = [
            ['Red', '#ff0000'],
            ['Blue', '#0000ff'],
            ['Green', '#00ff00'],
            ['Black', '#000000'],
            ['White', '#ffffff']
        ];
        
        foreach($colors as $color) {
            $sql = "INSERT INTO product_colors (name, hex_code) VALUES ('{$color[0]}', '{$color[1]}')";
            mysqli_query($conn, $sql);
        }
        
        echo "<p>Default colors created.</p>";
    } else {
        echo "<p>Error creating product_colors table: " . mysqli_error($conn) . "</p>";
    }
}

// Check if product_sizes table exists
$sql = "SHOW TABLES LIKE 'product_sizes'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>product_sizes table exists.</p>";
    
    // Show table structure
    $sql = "DESCRIBE product_sizes";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>product_sizes table structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Show existing sizes
    $sql = "SELECT * FROM product_sizes";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>Existing product sizes:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Status</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . ($row['status'] == 0 ? 'Visible' : 'Hidden') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>product_sizes table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE product_sizes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        status TINYINT NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if(mysqli_query($conn, $sql)) {
        echo "<p>product_sizes table created successfully.</p>";
        
        // Insert some default sizes
        $sizes = ['S', 'M', 'L', 'XL', 'XXL'];
        
        foreach($sizes as $size) {
            $sql = "INSERT INTO product_sizes (name) VALUES ('$size')";
            mysqli_query($conn, $sql);
        }
        
        echo "<p>Default sizes created.</p>";
    } else {
        echo "<p>Error creating product_sizes table: " . mysqli_error($conn) . "</p>";
    }
}

// Check if product_variants table exists
$sql = "SHOW TABLES LIKE 'product_variants'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>product_variants table exists.</p>";
    
    // Show table structure
    $sql = "DESCRIBE product_variants";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>product_variants table structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>product_variants table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE product_variants (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        color_id INT NULL,
        size_id INT NULL,
        additional_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        quantity INT NOT NULL DEFAULT 0,
        status TINYINT NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    )";
    
    if(mysqli_query($conn, $sql)) {
        echo "<p>product_variants table created successfully.</p>";
    } else {
        echo "<p>Error creating product_variants table: " . mysqli_error($conn) . "</p>";
    }
}
?>
