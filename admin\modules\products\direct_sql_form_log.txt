2025-04-13 14:49:43 - <PERSON><PERSON><PERSON> started
2025-04-13 14:49:43 - Form not submitted
2025-04-13 14:52:52 - <PERSON>ript started
2025-04-13 14:52:52 - Form submitted - Data: Array
(
    [name] => Dress
    [category_id] => 1
    [cost_price] => 200.00
    [sales_price] => 600.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 600.00
    [quantity] => 25
    [barcode] => 0007
    [description] => 
    [product_type_id] => 1
    [variant_pricing_type] => none
    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-04-13 14:52:52 - Basic product data - Data: Array
(
    [category_id] => 1
    [name] => Dress
    [cost_price] => 200.00
    [sales_price] => 600.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 600.00
    [quantity] => 25
    [barcode] => 0007
    [status] => 0
)

2025-04-13 14:52:52 - Main image uploaded successfully - Data: 1744548772_producthere.jpg
2025-04-13 14:52:52 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '1', 'Dress', '', '0007', '200.00', '600.00',
            '0', '0.00', '600.00', '25', '1744548772_producthere.jpg', '0', '2025-04-13 14:52:52',
            '0', '0', '1', '0'
        )
2025-04-13 14:52:52 - Query result - Data: Array
(
    [success] => true
    [product_id] => 26
    [error] => 
)

2025-04-13 14:52:52 - Product created successfully, redirecting to products.php
2025-04-13 15:01:15 - Script started
2025-04-13 15:01:15 - Form submitted - Data: Array
(
    [name] => Dress Images
    [category_id] => 3
    [cost_price] => 200.00
    [sales_price] => 600.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 600.00
    [quantity] => 25
    [barcode] => 0000
    [description] => 
    [product_type_id] => 1
    [variant_pricing_type] => none
    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-04-13 15:01:15 - Basic product data - Data: Array
(
    [category_id] => 3
    [name] => Dress Images
    [cost_price] => 200.00
    [sales_price] => 600.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 600.00
    [quantity] => 25
    [barcode] => 0000
    [status] => 0
)

2025-04-13 15:01:15 - Main image uploaded successfully - Data: 1744549275_producthere.jpg
2025-04-13 15:01:15 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '3', 'Dress Images', '', '0000', '200.00', '600.00',
            '0', '0.00', '600.00', '25', '1744549275_producthere.jpg', '0', '2025-04-13 15:01:15',
            '0', '0', '1', '0'
        )
2025-04-13 15:01:15 - Query result - Data: Array
(
    [success] => true
    [product_id] => 27
    [error] => 
)

2025-04-13 15:01:15 - Product created successfully with ID: 27
2025-04-13 15:01:15 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [full_path] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
            [1] => image/jpeg
            [2] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\php6216.tmp
            [1] => C:\xampp\tmp\php6217.tmp
            [2] => C:\xampp\tmp\php6218.tmp
        )

    [error] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
        )

    [size] => Array
        (
            [0] => 106140
            [1] => 98139
            [2] => 46596
        )

)

2025-04-13 15:01:15 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress1.jpg
    [final_name] => 1744549275_additional_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1744549275_additional_0_reddress1.jpg
)

2025-04-13 15:01:15 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:01:15 - Uploading additional image - Data: Array
(
    [index] => 1
    [name] => reddress2.jpg
    [final_name] => 1744549275_additional_1_reddress2.jpg
    [upload_to] => ../../../uploads/products/1744549275_additional_1_reddress2.jpg
)

2025-04-13 15:01:15 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:01:15 - Uploading additional image - Data: Array
(
    [index] => 2
    [name] => reddress3.jpg
    [final_name] => 1744549275_additional_2_reddress3.jpg
    [upload_to] => ../../../uploads/products/1744549275_additional_2_reddress3.jpg
)

2025-04-13 15:01:15 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:01:15 - Redirecting to products.php
2025-04-13 15:05:19 - Script started
2025-04-13 15:05:19 - Form submitted - Data: Array
(
    [name] => Dress Labels
    [category_id] => 1
    [cost_price] => 200.00
    [sales_price] => 600.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 600.00
    [quantity] => 25
    [barcode] => 0007
    [description] => <p>Description</p>
    [product_labels] => Array
        (
            [0] => 1
        )

    [product_type_id] => 1
    [variant_pricing_type] => none
    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-04-13 15:05:19 - Basic product data - Data: Array
(
    [category_id] => 1
    [name] => Dress Labels
    [cost_price] => 200.00
    [sales_price] => 600.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 600.00
    [quantity] => 25
    [barcode] => 0007
    [status] => 0
)

2025-04-13 15:05:19 - Main image uploaded successfully - Data: 1744549519_black-dress.webp
2025-04-13 15:05:19 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '1', 'Dress Labels', '<p>Description</p>', '0007', '200.00', '600.00',
            '0', '0.00', '600.00', '25', '1744549519_black-dress.webp', '0', '2025-04-13 15:05:19',
            '0', '0', '1', '0'
        )
2025-04-13 15:05:19 - Query result - Data: Array
(
    [success] => true
    [product_id] => 28
    [error] => 
)

2025-04-13 15:05:19 - Product created successfully with ID: 28
2025-04-13 15:05:19 - Processing product label - Data: Array
(
    [product_id] => 28
    [label_id] => 1
)

2025-04-13 15:05:19 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:05:19 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [full_path] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
            [1] => image/jpeg
            [2] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\php18F8.tmp
            [1] => C:\xampp\tmp\php18F9.tmp
            [2] => C:\xampp\tmp\php18FA.tmp
        )

    [error] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
        )

    [size] => Array
        (
            [0] => 106140
            [1] => 98139
            [2] => 46596
        )

)

2025-04-13 15:05:19 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress1.jpg
    [final_name] => 1744549519_additional_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1744549519_additional_0_reddress1.jpg
)

2025-04-13 15:05:19 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:05:19 - Uploading additional image - Data: Array
(
    [index] => 1
    [name] => reddress2.jpg
    [final_name] => 1744549519_additional_1_reddress2.jpg
    [upload_to] => ../../../uploads/products/1744549519_additional_1_reddress2.jpg
)

2025-04-13 15:05:19 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:05:19 - Uploading additional image - Data: Array
(
    [index] => 2
    [name] => reddress3.jpg
    [final_name] => 1744549519_additional_2_reddress3.jpg
    [upload_to] => ../../../uploads/products/1744549519_additional_2_reddress3.jpg
)

2025-04-13 15:05:19 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:05:19 - Redirecting to products.php
2025-04-13 15:11:49 - Script started
2025-04-13 15:11:49 - Form submitted - Data: Array
(
    [name] => dress size and colour
    [category_id] => 1
    [cost_price] => 200.00
    [sales_price] => 600.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 600.00
    [quantity] => 25
    [barcode] => 0009
    [description] => <p>Description</p>
    [product_labels] => Array
        (
            [0] => 3
        )

    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 3
            [2] => 1
            [3] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 1
            [3] => 2
        )

    [variant_additional_prices] => Array
        (
            [0] => 0
            [1] => 50.00
            [2] => 0
            [3] => 50.00
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 8
            [2] => 10
            [3] => 8
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-04-13 15:11:49 - Basic product data - Data: Array
(
    [category_id] => 1
    [name] => dress size and colour
    [cost_price] => 200.00
    [sales_price] => 600.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 600.00
    [quantity] => 25
    [barcode] => 0009
    [status] => 0
)

2025-04-13 15:11:49 - Main image uploaded successfully - Data: 1744549909_black-dress.webp
2025-04-13 15:11:49 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '1', 'dress size and colour', '<p>Description</p>', '0009', '200.00', '600.00',
            '0', '0.00', '600.00', '25', '1744549909_black-dress.webp', '0', '2025-04-13 15:11:49',
            '0', '0', '1', '0'
        )
2025-04-13 15:11:49 - Query result - Data: Array
(
    [success] => true
    [product_id] => 29
    [error] => 
)

2025-04-13 15:11:49 - Product created successfully with ID: 29
2025-04-13 15:11:49 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-04-13 15:11:49 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
            [1] => 2
        )

    [variant_pricing_type] => additional
)

2025-04-13 15:11:49 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 3
            [2] => 1
            [3] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 1
            [3] => 2
        )

    [additional_prices] => Array
        (
            [0] => 0
            [1] => 50.00
            [2] => 0
            [3] => 50.00
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 8
            [2] => 10
            [3] => 8
        )

)

2025-04-13 15:11:49 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 1
    [additional_price] => 0
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-04-13 15:11:49 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 2
    [additional_price] => 50.00
    [quantity] => 8
    [success] => true
    [error] => 
)

2025-04-13 15:11:49 - Variant with additional price created - Data: Array
(
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 0
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-04-13 15:11:49 - Variant with additional price created - Data: Array
(
    [color_id] => 1
    [size_id] => 2
    [additional_price] => 50.00
    [quantity] => 8
    [success] => true
    [error] => 
)

2025-04-13 15:11:49 - Processing product label - Data: Array
(
    [product_id] => 29
    [label_id] => 3
)

2025-04-13 15:11:49 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:11:49 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [full_path] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
            [1] => image/jpeg
            [2] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\phpBCF.tmp
            [1] => C:\xampp\tmp\phpBD0.tmp
            [2] => C:\xampp\tmp\phpBD1.tmp
        )

    [error] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
        )

    [size] => Array
        (
            [0] => 106140
            [1] => 98139
            [2] => 46596
        )

)

2025-04-13 15:11:49 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress1.jpg
    [final_name] => 1744549909_additional_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1744549909_additional_0_reddress1.jpg
)

2025-04-13 15:11:49 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:11:49 - Uploading additional image - Data: Array
(
    [index] => 1
    [name] => reddress2.jpg
    [final_name] => 1744549909_additional_1_reddress2.jpg
    [upload_to] => ../../../uploads/products/1744549909_additional_1_reddress2.jpg
)

2025-04-13 15:11:49 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:11:49 - Uploading additional image - Data: Array
(
    [index] => 2
    [name] => reddress3.jpg
    [final_name] => 1744549909_additional_2_reddress3.jpg
    [upload_to] => ../../../uploads/products/1744549909_additional_2_reddress3.jpg
)

2025-04-13 15:11:49 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-13 15:11:49 - Redirecting to products.php
2025-04-14 08:02:21 - Script started
2025-04-14 08:02:21 - Form submitted - Data: Array
(
    [name] => Dress All
    [category_id] => 1
    [cost_price] => 190.00
    [sales_price] => 590.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 520.00
    [quantity] => 25
    [barcode] => 0007
    [description] => <p>Description</p>
    [product_labels] => Array
        (
            [0] => 2
        )

    [enable_discounted_price] => 1
    [discounted_price] => 520.00
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 3
            [2] => 1
            [3] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 1
            [3] => 2
        )

    [variant_additional_prices] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
            [3] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 8
            [2] => 10
            [3] => 8
        )

    [enable_bulk_pricing] => 1
    [bulk_min_qty] => Array
        (
            [0] => 6
        )

    [bulk_max_qty] => Array
        (
            [0] => 8
        )

    [bulk_price] => Array
        (
            [0] => 490.00
        )

    [saveProduct] => 1
)

2025-04-14 08:02:21 - Basic product data - Data: Array
(
    [category_id] => 1
    [name] => Dress All
    [cost_price] => 190.00
    [sales_price] => 590.00
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 520.00
    [quantity] => 25
    [barcode] => 0007
    [status] => 0
)

2025-04-14 08:02:21 - Main image uploaded successfully - Data: 1744610541_black-dress.webp
2025-04-14 08:02:21 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '1', 'Dress All', '<p>Description</p>', '0007', '190.00', '590.00',
            '0', '0.00', '520.00', '25', '1744610541_black-dress.webp', '0', '2025-04-14 08:02:21',
            '0', '0', '1', '0'
        )
2025-04-14 08:02:21 - Query result - Data: Array
(
    [success] => true
    [product_id] => 30
    [error] => 
)

2025-04-14 08:02:21 - Product created successfully with ID: 30
2025-04-14 08:02:21 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-04-14 08:02:21 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
            [1] => 2
        )

    [variant_pricing_type] => additional
)

2025-04-14 08:02:21 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 3
            [2] => 1
            [3] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 1
            [3] => 2
        )

    [additional_prices] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
            [3] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 8
            [2] => 10
            [3] => 8
        )

)

2025-04-14 08:02:21 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 1
    [additional_price] => 0
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-04-14 08:02:21 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 2
    [additional_price] => 0
    [quantity] => 8
    [success] => true
    [error] => 
)

2025-04-14 08:02:21 - Variant with additional price created - Data: Array
(
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 0
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-04-14 08:02:21 - Variant with additional price created - Data: Array
(
    [color_id] => 1
    [size_id] => 2
    [additional_price] => 0
    [quantity] => 8
    [success] => true
    [error] => 
)

2025-04-14 08:02:21 - Processing product label - Data: Array
(
    [product_id] => 30
    [label_id] => 2
)

2025-04-14 08:02:21 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-14 08:02:21 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress2.jpg
            [1] => reddress3.jpg
            [2] => reddress4.jpg
        )

    [full_path] => Array
        (
            [0] => reddress2.jpg
            [1] => reddress3.jpg
            [2] => reddress4.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
            [1] => image/jpeg
            [2] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\php38A9.tmp
            [1] => C:\xampp\tmp\php38AA.tmp
            [2] => C:\xampp\tmp\php38AB.tmp
        )

    [error] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
        )

    [size] => Array
        (
            [0] => 98139
            [1] => 46596
            [2] => 55671
        )

)

2025-04-14 08:02:21 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress2.jpg
    [final_name] => 1744610541_additional_0_reddress2.jpg
    [upload_to] => ../../../uploads/products/1744610541_additional_0_reddress2.jpg
)

2025-04-14 08:02:21 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-14 08:02:21 - Uploading additional image - Data: Array
(
    [index] => 1
    [name] => reddress3.jpg
    [final_name] => 1744610541_additional_1_reddress3.jpg
    [upload_to] => ../../../uploads/products/1744610541_additional_1_reddress3.jpg
)

2025-04-14 08:02:21 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-14 08:02:21 - Uploading additional image - Data: Array
(
    [index] => 2
    [name] => reddress4.jpg
    [final_name] => 1744610541_additional_2_reddress4.jpg
    [upload_to] => ../../../uploads/products/1744610541_additional_2_reddress4.jpg
)

2025-04-14 08:02:21 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-04-14 08:02:21 - Redirecting to products.php
2025-06-09 12:13:27 - Script started
2025-06-09 12:13:27 - Form submitted - Data: Array
(
    [name] => Kurthi
    [category_id] => 82
    [cost_price] => 
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 
    [barcode] => 0000
    [description] => 
    [discounted_price] => 
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
        )

    [variant_sizes] => Array
        (
            [0] => 3
        )

    [variant_color_ids] => Array
        (
            [0] => 3
        )

    [variant_size_ids] => Array
        (
            [0] => 3
        )

    [variant_additional_prices] => Array
        (
            [0] => 50
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-06-09 12:13:27 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => Kurthi
    [cost_price] => 
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 
    [barcode] => 0000
    [status] => 0
)

2025-06-09 12:13:27 - Main image uploaded successfully - Data: 1749464007_SKU Ku 3002.jpg
2025-06-09 12:13:27 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'Kurthi', '', '0000', '', '199.95',
            '0', '0.00', '199.95', NULL, '', '1749464007_SKU Ku 3002.jpg', '0', '2025-06-09 12:13:27',
            '0', '0', '1', '0'
        )
2025-06-09 12:13:27 - Exception - Data: Array
(
    [message] => Incorrect decimal value: '' for column `webapp`.`products`.`cost_price` at row 1
    [trace] => #0 C:\xampp\htdocs\webapp\admin\modules\products\direct_sql_form.php(107): mysqli_query(Object(mysqli), 'INSERT INTO pro...')
#1 {main}
)

2025-06-09 12:14:07 - Script started
2025-06-09 12:14:07 - Form submitted - Data: Array
(
    [name] => 
    [category_id] => 
    [cost_price] => 99.95
    [sales_price] => 
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 0.00
    [quantity] => 
    [barcode] => 0000
    [description] => 
    [discounted_price] => 
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 3
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 3
            [1] => 3
        )

    [variant_additional_prices] => Array
        (
            [0] => 1
            [1] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 15
            [1] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-06-09 12:14:07 - Basic product data - Data: Array
(
    [category_id] => 
    [name] => 
    [cost_price] => 99.95
    [sales_price] => 
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 0.00
    [quantity] => 
    [barcode] => 0000
    [status] => 0
)

2025-06-09 12:14:07 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '', '', '', '0000', '99.95', '',
            '0', '0.00', '0.00', NULL, '', 'default.jpg', '0', '2025-06-09 12:14:07',
            '0', '0', '1', '0'
        )
2025-06-09 12:14:07 - Exception - Data: Array
(
    [message] => Incorrect decimal value: '' for column `webapp`.`products`.`sales_price` at row 1
    [trace] => #0 C:\xampp\htdocs\webapp\admin\modules\products\direct_sql_form.php(107): mysqli_query(Object(mysqli), 'INSERT INTO pro...')
#1 {main}
)

2025-06-09 12:14:58 - Script started
2025-06-09 12:14:58 - Form submitted - Data: Array
(
    [name] => Kurthi
    [category_id] => 
    [cost_price] => 10
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [description] => 
    [product_labels] => Array
        (
            [0] => 1
        )

    [discounted_price] => 
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 3
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 3
            [1] => 3
        )

    [variant_additional_prices] => Array
        (
            [0] => 1
            [1] => 2
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-06-09 12:14:58 - Basic product data - Data: Array
(
    [category_id] => 
    [name] => Kurthi
    [cost_price] => 10
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [status] => 0
)

2025-06-09 12:14:58 - Main image uploaded successfully - Data: 1749464098_SKU Ku 1718.jpg
2025-06-09 12:14:58 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '', 'Kurthi', '', '0000', '10', '199.95',
            '0', '0.00', '199.95', NULL, '1', '1749464098_SKU Ku 1718.jpg', '0', '2025-06-09 12:14:58',
            '0', '0', '1', '0'
        )
2025-06-09 12:14:58 - Query result - Data: Array
(
    [success] => true
    [product_id] => 36
    [error] => 
)

2025-06-09 12:14:58 - Product created successfully with ID: 36
2025-06-09 12:14:58 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-06-09 12:14:58 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 3
        )

    [variant_pricing_type] => additional
)

2025-06-09 12:14:58 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 3
            [1] => 3
        )

    [additional_prices] => Array
        (
            [0] => 1
            [1] => 2
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

)

2025-06-09 12:14:58 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 3
    [additional_price] => 1
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-06-09 12:14:58 - Variant with additional price created - Data: Array
(
    [color_id] => 1
    [size_id] => 3
    [additional_price] => 2
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-06-09 12:14:58 - Processing product label - Data: Array
(
    [product_id] => 36
    [label_id] => 1
)

2025-06-09 12:14:58 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-06-09 12:14:58 - Redirecting to products.php
2025-06-09 12:16:51 - Script started
2025-06-09 12:16:51 - Form submitted - Data: Array
(
    [name] => 007
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [description] => 
    [discounted_price] => 
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
        )

    [variant_sizes] => Array
        (
            [0] => 3
        )

    [variant_color_ids] => Array
        (
            [0] => 3
        )

    [variant_size_ids] => Array
        (
            [0] => 3
        )

    [variant_additional_prices] => Array
        (
            [0] => 200
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-06-09 12:16:51 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => 007
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [status] => 0
)

2025-06-09 12:16:51 - Main image uploaded successfully - Data: 1749464211_SKU Pvs 30 A.jpg
2025-06-09 12:16:51 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', '007', '', '0000', '99.95', '199.95',
            '0', '0.00', '199.95', NULL, '1', '1749464211_SKU Pvs 30 A.jpg', '0', '2025-06-09 12:16:51',
            '0', '0', '1', '0'
        )
2025-06-09 12:16:51 - Query result - Data: Array
(
    [success] => true
    [product_id] => 37
    [error] => 
)

2025-06-09 12:16:51 - Product created successfully with ID: 37
2025-06-09 12:16:51 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-06-09 12:16:51 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
        )

    [size_ids] => Array
        (
            [0] => 3
        )

    [variant_pricing_type] => additional
)

2025-06-09 12:16:51 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
        )

    [size_ids] => Array
        (
            [0] => 3
        )

    [additional_prices] => Array
        (
            [0] => 200
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

)

2025-06-09 12:16:51 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 3
    [additional_price] => 200
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-06-09 12:16:51 - Redirecting to products.php
2025-06-09 12:33:02 - Script started
2025-06-09 12:33:02 - Form submitted - Data: Array
(
    [name] => Dress colour 2
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [description] => 
    [discounted_price] => 
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 
            [1] => 
        )

    [variant_additional_prices] => Array
        (
            [0] => 1
            [1] => 1
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-06-09 12:33:02 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => Dress colour 2
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [status] => 0
)

2025-06-09 12:33:02 - Main image uploaded successfully - Data: 1749465182_SKU Ku 104 (front).jpg
2025-06-09 12:33:02 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'Dress colour 2', '', '0000', '99.95', '199.95',
            '0', '0.00', '199.95', NULL, '1', '1749465182_SKU Ku 104 (front).jpg', '0', '2025-06-09 12:33:02',
            '0', '0', '1', '0'
        )
2025-06-09 12:33:02 - Query result - Data: Array
(
    [success] => true
    [product_id] => 38
    [error] => 
)

2025-06-09 12:33:02 - Product created successfully with ID: 38
2025-06-09 12:33:02 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-06-09 12:33:02 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
        )

    [variant_pricing_type] => additional
)

2025-06-09 12:33:02 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 
            [1] => 
        )

    [additional_prices] => Array
        (
            [0] => 1
            [1] => 1
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

)

2025-06-09 12:33:02 - Exception - Data: Array
(
    [message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' '1', '10', 0)' at line 2
    [trace] => #0 C:\xampp\htdocs\webapp\admin\modules\products\direct_sql_form.php(240): mysqli_query(Object(mysqli), 'INSERT INTO pro...')
#1 {main}
)

2025-06-09 12:35:28 - Script started
2025-06-09 12:35:28 - Form submitted - Data: Array
(
    [name] => Kurthi
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [description] => 
    [discounted_price] => 
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 4
        )

    [variant_sizes] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 3
            [2] => 4
            [3] => 4
        )

    [variant_size_ids] => Array
        (
            [0] => 3
            [1] => 1
            [2] => 3
            [3] => 1
        )

    [variant_additional_prices] => Array
        (
            [0] => 1
            [1] => 1
            [2] => 1
            [3] => 1
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
            [2] => 10
            [3] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-06-09 12:35:28 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => Kurthi
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [status] => 0
)

2025-06-09 12:35:28 - Main image uploaded successfully - Data: 1749465328_SKU Jmp 1093.jpg
2025-06-09 12:35:28 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'Kurthi', '', '0000', '99.95', '199.95',
            '0', '0.00', '199.95', NULL, '1', '1749465328_SKU Jmp 1093.jpg', '0', '2025-06-09 12:35:28',
            '0', '0', '1', '0'
        )
2025-06-09 12:35:28 - Query result - Data: Array
(
    [success] => true
    [product_id] => 39
    [error] => 
)

2025-06-09 12:35:28 - Product created successfully with ID: 39
2025-06-09 12:35:28 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-06-09 12:35:28 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 4
        )

    [size_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_pricing_type] => additional
)

2025-06-09 12:35:28 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 3
            [2] => 4
            [3] => 4
        )

    [size_ids] => Array
        (
            [0] => 3
            [1] => 1
            [2] => 3
            [3] => 1
        )

    [additional_prices] => Array
        (
            [0] => 1
            [1] => 1
            [2] => 1
            [3] => 1
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
            [2] => 10
            [3] => 10
        )

)

2025-06-09 12:35:28 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 3
    [additional_price] => 1
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-06-09 12:35:28 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 1
    [additional_price] => 1
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-06-09 12:35:28 - Variant with additional price created - Data: Array
(
    [color_id] => 4
    [size_id] => 3
    [additional_price] => 1
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-06-09 12:35:28 - Variant with additional price created - Data: Array
(
    [color_id] => 4
    [size_id] => 1
    [additional_price] => 1
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-06-09 12:35:28 - Redirecting to products.php
2025-06-09 15:27:01 - Script started
2025-06-09 15:27:01 - Form submitted - Data: Array
(
    [name] => test
    [category_id] => 81
    [cost_price] => 0
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [description] => 
    [product_labels] => Array
        (
            [0] => 1
        )

    [discounted_price] => 
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 2
        )

    [variant_sizes] => Array
        (
            [0] => 3
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 2
        )

    [variant_size_ids] => Array
        (
            [0] => 3
            [1] => 3
        )

    [variant_additional_prices] => Array
        (
            [0] => 25
            [1] => 30
        )

    [variant_quantities] => Array
        (
            [0] => 1
            [1] => 1
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-06-09 15:27:01 - Basic product data - Data: Array
(
    [category_id] => 81
    [name] => test
    [cost_price] => 0
    [sales_price] => 199.95
    [vat_percentage] => 0
    [vatT] => 0.00
    [price] => 199.95
    [quantity] => 1
    [barcode] => 0000
    [status] => 0
)

2025-06-09 15:27:01 - Main image uploaded successfully - Data: 1749475621_SKU Ku 1718.jpg
2025-06-09 15:27:01 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '81', 'test', '', '0000', '0', '199.95',
            '0', '0.00', '199.95', NULL, '1', '1749475621_SKU Ku 1718.jpg', '0', '2025-06-09 15:27:01',
            '0', '0', '1', '0'
        )
2025-06-09 15:27:01 - Query result - Data: Array
(
    [success] => true
    [product_id] => 38
    [error] => 
)

2025-06-09 15:27:01 - Product created successfully with ID: 38
2025-06-09 15:27:01 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-06-09 15:27:01 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 2
        )

    [size_ids] => Array
        (
            [0] => 3
        )

    [variant_pricing_type] => additional
)

2025-06-09 15:27:01 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 2
        )

    [size_ids] => Array
        (
            [0] => 3
            [1] => 3
        )

    [additional_prices] => Array
        (
            [0] => 25
            [1] => 30
        )

    [variant_quantities] => Array
        (
            [0] => 1
            [1] => 1
        )

)

2025-06-09 15:27:01 - Variant with additional price created - Data: Array
(
    [color_id] => 3
    [size_id] => 3
    [additional_price] => 25
    [quantity] => 1
    [success] => true
    [error] => 
)

2025-06-09 15:27:01 - Variant with additional price created - Data: Array
(
    [color_id] => 2
    [size_id] => 3
    [additional_price] => 30
    [quantity] => 1
    [success] => true
    [error] => 
)

2025-06-09 15:27:01 - Processing product label - Data: Array
(
    [product_id] => 38
    [label_id] => 1
)

2025-06-09 15:27:01 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-06-09 15:27:01 - Redirecting to products.php
2025-07-01 13:13:05 - Script started
2025-07-01 13:13:05 - Form submitted - Data: Array
(
    [name] => taariq
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 29.99
    [price] => 229.94
    [quantity] => 25
    [barcode] => 007
    [description] => 
    [product_labels] => Array
        (
            [0] => 1
        )

    [discounted_price] => 
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
            [1] => 1
        )

    [variant_additional_prices] => Array
        (
            [0] => 0
            [1] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-07-01 13:13:05 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => taariq
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 29.99
    [price] => 229.94
    [quantity] => 25
    [barcode] => 007
    [status] => 0
)

2025-07-01 13:13:05 - Main image uploaded successfully - Data: 1751368385_black-dress.webp
2025-07-01 13:13:05 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'taariq', '', '007', '99.95', '199.95',
            '15', '29.99', '229.94', NULL, '25', '1751368385_black-dress.webp', '0', '2025-07-01 13:13:05',
            '0', '0', '1', '0'
        )
2025-07-01 13:13:05 - Query result - Data: Array
(
    [success] => true
    [product_id] => 83
    [error] => 
)

2025-07-01 13:13:05 - Product created successfully with ID: 83
2025-07-01 13:13:05 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 13:13:05 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 13:13:05 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
            [1] => 1
        )

    [additional_prices] => Array
        (
            [0] => 0
            [1] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

)

2025-07-01 13:13:05 - Exception - Data: Array
(
    [message] => Field 'sku' doesn't have a default value
    [trace] => #0 C:\xampp\htdocs\apponline\admin\modules\products\direct_sql_form.php(240): mysqli_query(Object(mysqli), 'INSERT INTO pro...')
#1 {main}
)

2025-07-01 13:36:55 - Script started
2025-07-01 13:36:55 - Form submitted - Data: Array
(
    [name] => test1
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 22.50
    [price] => 172.50
    [quantity] => 25
    [barcode] => 007
    [description] => 
    [product_labels] => Array
        (
            [0] => 1
        )

    [enable_discounted_price] => 1
    [discounted_price] => 150
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
        )

    [variant_additional_prices] => Array
        (
            [0] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-07-01 13:36:55 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => test1
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 22.50
    [price] => 172.50
    [quantity] => 25
    [barcode] => 007
    [status] => 0
)

2025-07-01 13:36:55 - Main image uploaded successfully - Data: 1751369815_reddress1.jpg
2025-07-01 13:36:55 - Discounted price set - Data: '150'
2025-07-01 13:36:55 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'test1', '', '007', '99.95', '199.95',
            '15', '22.50', '172.50', '150', '25', '1751369815_reddress1.jpg', '0', '2025-07-01 13:36:55',
            '0', '0', '1', '0'
        )
2025-07-01 13:36:55 - Query result - Data: Array
(
    [success] => true
    [product_id] => 84
    [error] => 
)

2025-07-01 13:36:55 - Product created successfully with ID: 84
2025-07-01 13:36:55 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 13:36:55 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 13:36:55 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [additional_prices] => Array
        (
            [0] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

)

2025-07-01 13:36:55 - Variant with additional price created - Data: Array
(
    [sku] => TEST1-84-RED-SMA-1751369815
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 0
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 13:36:55 - Processing product label - Data: Array
(
    [product_id] => 84
    [label_id] => 1
)

2025-07-01 13:36:55 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 13:36:55 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => producthere.jpg
        )

    [full_path] => Array
        (
            [0] => producthere.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\phpC17.tmp
        )

    [error] => Array
        (
            [0] => 0
        )

    [size] => Array
        (
            [0] => 3473
        )

)

2025-07-01 13:36:55 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => producthere.jpg
    [final_name] => 1751369815_additional_0_producthere.jpg
    [upload_to] => ../../../uploads/products/1751369815_additional_0_producthere.jpg
)

2025-07-01 13:36:55 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 13:36:55 - Redirecting to products.php
2025-07-01 13:58:00 - Script started
2025-07-01 13:58:00 - Form submitted - Data: Array
(
    [name] => test2
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 22.49
    [price] => 172.44
    [quantity] => 25
    [barcode] => 0007
    [description] => 
    [product_labels] => Array
        (
            [0] => 4
        )

    [enable_discounted_price] => 1
    [discounted_price] => 149.95
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
            [1] => 1
        )

    [variant_skus] => Array
        (
            [0] => skuM1
            [1] => skuM1
        )

    [variant_additional_prices] => Array
        (
            [0] => 25
            [1] => 25
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

    [enable_bulk_pricing] => 1
    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 100
        )

    [saveProduct] => 1
)

2025-07-01 13:58:00 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => test2
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 22.49
    [price] => 172.44
    [quantity] => 25
    [barcode] => 0007
    [status] => 0
)

2025-07-01 13:58:00 - Main image uploaded successfully - Data: 1751371080_black-dress.webp
2025-07-01 13:58:00 - Discounted price set - Data: '149.95'
2025-07-01 13:58:00 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'test2', '', '0007', '99.95', '199.95',
            '15', '22.49', '172.44', '149.95', '25', '1751371080_black-dress.webp', '0', '2025-07-01 13:58:00',
            '0', '0', '1', '0'
        )
2025-07-01 13:58:00 - Query result - Data: Array
(
    [success] => true
    [product_id] => 85
    [error] => 
)

2025-07-01 13:58:00 - Product created successfully with ID: 85
2025-07-01 13:58:00 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 13:58:00 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 13:58:00 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
            [1] => 1
        )

    [additional_prices] => Array
        (
            [0] => 25
            [1] => 25
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

)

2025-07-01 13:58:00 - Variant with additional price created - Data: Array
(
    [sku] => skuM1
    [color_id] => 3
    [size_id] => 1
    [additional_price] => 25
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 13:58:00 - Variant with additional price created - Data: Array
(
    [sku] => skuM1
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 25
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 13:58:00 - Processing bulk pricing - Data: Array
(
    [min_quantities] => Array
        (
            [0] => 2
        )

    [max_quantities] => Array
        (
            [0] => 5
        )

    [bulk_prices] => Array
        (
            [0] => 100
        )

)

2025-07-01 13:58:00 - Bulk pricing rule inserted - Data: Array
(
    [min_qty] => 2
    [max_qty] => 5
    [price] => 100
    [success] => true
    [error] => 
)

2025-07-01 13:58:00 - Processing product label - Data: Array
(
    [product_id] => 85
    [label_id] => 4
)

2025-07-01 13:58:00 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 13:58:00 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress1.jpg
        )

    [full_path] => Array
        (
            [0] => reddress1.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\php57BC.tmp
        )

    [error] => Array
        (
            [0] => 0
        )

    [size] => Array
        (
            [0] => 106140
        )

)

2025-07-01 13:58:00 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress1.jpg
    [final_name] => 1751371080_additional_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1751371080_additional_0_reddress1.jpg
)

2025-07-01 13:58:00 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 13:58:00 - Redirecting to products.php
2025-07-01 14:18:16 - Script started
2025-07-01 14:18:16 - Form submitted - Data: Array
(
    [name] => test3
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 27.00
    [price] => 207.00
    [quantity] => 2
    [barcode] => 0002
    [description] => 
    [product_labels] => Array
        (
            [0] => 4
        )

    [enable_discounted_price] => 1
    [discounted_price] => 180.00
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
            [1] => 1
        )

    [variant_skus] => Array
        (
            [0] => skuM1
            [1] => skuM1
        )

    [variant_additional_prices] => Array
        (
            [0] => 25
            [1] => 25
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

    [enable_bulk_pricing] => 1
    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 180.00
        )

    [saveProduct] => 1
)

2025-07-01 14:18:16 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => test3
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 27.00
    [price] => 207.00
    [quantity] => 2
    [barcode] => 0002
    [status] => 0
)

2025-07-01 14:18:16 - Main image uploaded successfully - Data: 1751372296_black-dress.webp
2025-07-01 14:18:16 - Discounted price set - Data: '180.00'
2025-07-01 14:18:16 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'test3', '', '0002', '99.95', '199.95',
            '15', '27.00', '207.00', '180.00', '2', '1751372296_black-dress.webp', '0', '2025-07-01 14:18:16',
            '0', '0', '1', '0'
        )
2025-07-01 14:18:16 - Query result - Data: Array
(
    [success] => true
    [product_id] => 86
    [error] => 
)

2025-07-01 14:18:16 - Product created successfully with ID: 86
2025-07-01 14:18:16 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 14:18:16 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 14:18:16 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 3
            [1] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
            [1] => 1
        )

    [additional_prices] => Array
        (
            [0] => 25
            [1] => 25
        )

    [variant_quantities] => Array
        (
            [0] => 10
            [1] => 10
        )

)

2025-07-01 14:18:16 - Variant with additional price created - Data: Array
(
    [sku] => skuM1
    [color_id] => 3
    [size_id] => 1
    [additional_price] => 25
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 14:18:16 - Variant with additional price created - Data: Array
(
    [sku] => skuM1
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 25
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 14:18:16 - Processing bulk pricing - Data: Array
(
    [min_quantities] => Array
        (
            [0] => 2
        )

    [max_quantities] => Array
        (
            [0] => 5
        )

    [bulk_prices] => Array
        (
            [0] => 180.00
        )

)

2025-07-01 14:18:16 - Bulk pricing rule inserted - Data: Array
(
    [min_qty] => 2
    [max_qty] => 5
    [price] => 180.00
    [success] => true
    [error] => 
)

2025-07-01 14:18:16 - Processing product label - Data: Array
(
    [product_id] => 86
    [label_id] => 4
)

2025-07-01 14:18:16 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 14:18:16 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => producthere.jpg
        )

    [full_path] => Array
        (
            [0] => producthere.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\phpE65D.tmp
        )

    [error] => Array
        (
            [0] => 0
        )

    [size] => Array
        (
            [0] => 3473
        )

)

2025-07-01 14:18:16 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => producthere.jpg
    [final_name] => 1751372296_additional_0_producthere.jpg
    [upload_to] => ../../../uploads/products/1751372296_additional_0_producthere.jpg
)

2025-07-01 14:18:16 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 14:18:16 - Redirecting to products.php
2025-07-01 15:04:20 - Script started
2025-07-01 15:04:20 - Form submitted - Data: Array
(
    [name] => testALL
    [category_id] => 82
    [cost_price] => 25.00
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 27.00
    [price] => 207.00
    [quantity] => 25
    [barcode] => 0007
    [description] => 
    [product_labels] => Array
        (
            [0] => 2
        )

    [enable_discounted_price] => 1
    [discounted_price] => 180.00
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
        )

    [variant_skus] => Array
        (
            [0] => skuM
        )

    [variant_additional_prices] => Array
        (
            [0] => 20
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-07-01 15:04:20 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => testALL
    [cost_price] => 25.00
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 27.00
    [price] => 207.00
    [quantity] => 25
    [barcode] => 0007
    [status] => 0
)

2025-07-01 15:04:20 - Main image uploaded successfully - Data: 1751375060_black-dress.webp
2025-07-01 15:04:20 - Discounted price set - Data: '180.00'
2025-07-01 15:04:20 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'testALL', '', '0007', '25.00', '199.95',
            '15', '27.00', '207.00', '180.00', '25', '1751375060_black-dress.webp', '0', '2025-07-01 15:04:20',
            '0', '0', '1', '0'
        )
2025-07-01 15:04:20 - Query result - Data: Array
(
    [success] => true
    [product_id] => 87
    [error] => 
)

2025-07-01 15:04:20 - Product created successfully with ID: 87
2025-07-01 15:04:20 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 15:04:20 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 15:04:20 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [additional_prices] => Array
        (
            [0] => 20
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

)

2025-07-01 15:04:20 - Variant with additional price created - Data: Array
(
    [sku] => skuM
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 20
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 15:04:20 - Processing product label - Data: Array
(
    [product_id] => 87
    [label_id] => 2
)

2025-07-01 15:04:20 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:04:20 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress2.jpg
            [1] => reddress3.jpg
            [2] => reddress4.jpg
        )

    [full_path] => Array
        (
            [0] => reddress2.jpg
            [1] => reddress3.jpg
            [2] => reddress4.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
            [1] => image/jpeg
            [2] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\php146A.tmp
            [1] => C:\xampp\tmp\php146B.tmp
            [2] => C:\xampp\tmp\php146C.tmp
        )

    [error] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
        )

    [size] => Array
        (
            [0] => 98139
            [1] => 46596
            [2] => 55671
        )

)

2025-07-01 15:04:20 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress2.jpg
    [final_name] => 1751375060_additional_0_reddress2.jpg
    [upload_to] => ../../../uploads/products/1751375060_additional_0_reddress2.jpg
)

2025-07-01 15:04:20 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:04:20 - Uploading additional image - Data: Array
(
    [index] => 1
    [name] => reddress3.jpg
    [final_name] => 1751375060_additional_1_reddress3.jpg
    [upload_to] => ../../../uploads/products/1751375060_additional_1_reddress3.jpg
)

2025-07-01 15:04:20 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:04:20 - Uploading additional image - Data: Array
(
    [index] => 2
    [name] => reddress4.jpg
    [final_name] => 1751375060_additional_2_reddress4.jpg
    [upload_to] => ../../../uploads/products/1751375060_additional_2_reddress4.jpg
)

2025-07-01 15:04:20 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:04:20 - Redirecting to products.php
2025-07-01 15:18:58 - Script started
2025-07-01 15:18:58 - Form submitted - Data: Array
(
    [name] => all2
    [category_id] => 82
    [cost_price] => 25.00
    [sales_price] => 50
    [vat_percentage] => 15
    [vatT] => 6.00
    [price] => 46.00
    [quantity] => 25
    [barcode] => 0007
    [description] => <p>test</p>
    [product_labels] => Array
        (
            [0] => 3
        )

    [enable_discounted_price] => 1
    [discounted_price] => 40
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
        )

    [variant_skus] => Array
        (
            [0] => skuM
        )

    [variant_additional_prices] => Array
        (
            [0] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

    [enable_bulk_pricing] => 1
    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 35
        )

    [saveProduct] => 1
)

2025-07-01 15:18:58 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => all2
    [cost_price] => 25.00
    [sales_price] => 50
    [vat_percentage] => 15
    [vatT] => 6.00
    [price] => 46.00
    [quantity] => 25
    [barcode] => 0007
    [status] => 0
)

2025-07-01 15:18:58 - Main image uploaded successfully - Data: 1751375938_black-dress.webp
2025-07-01 15:18:58 - Discounted price set - Data: '40'
2025-07-01 15:18:58 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'all2', '<p>test</p>', '0007', '25.00', '50',
            '15', '6.00', '46.00', '40', '25', '1751375938_black-dress.webp', '0', '2025-07-01 15:18:58',
            '0', '0', '1', '0'
        )
2025-07-01 15:18:58 - Query result - Data: Array
(
    [success] => true
    [product_id] => 88
    [error] => 
)

2025-07-01 15:18:58 - Product created successfully with ID: 88
2025-07-01 15:18:58 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 15:18:58 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 15:18:58 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [additional_prices] => Array
        (
            [0] => 0
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

)

2025-07-01 15:18:58 - Variant with additional price created - Data: Array
(
    [sku] => skuM
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 0
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 15:18:58 - Processing bulk pricing - Data: Array
(
    [min_quantities] => Array
        (
            [0] => 2
        )

    [max_quantities] => Array
        (
            [0] => 5
        )

    [bulk_prices] => Array
        (
            [0] => 35
        )

)

2025-07-01 15:18:58 - Bulk pricing rule inserted - Data: Array
(
    [min_qty] => 2
    [max_qty] => 5
    [price] => 35
    [success] => true
    [error] => 
)

2025-07-01 15:18:58 - Processing product label - Data: Array
(
    [product_id] => 88
    [label_id] => 3
)

2025-07-01 15:18:58 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:18:58 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [full_path] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
            [1] => image/jpeg
            [2] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\php79A2.tmp
            [1] => C:\xampp\tmp\php79A3.tmp
            [2] => C:\xampp\tmp\php79A4.tmp
        )

    [error] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
        )

    [size] => Array
        (
            [0] => 106140
            [1] => 98139
            [2] => 46596
        )

)

2025-07-01 15:18:58 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress1.jpg
    [final_name] => 1751375938_additional_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1751375938_additional_0_reddress1.jpg
)

2025-07-01 15:18:58 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:18:58 - Uploading additional image - Data: Array
(
    [index] => 1
    [name] => reddress2.jpg
    [final_name] => 1751375938_additional_1_reddress2.jpg
    [upload_to] => ../../../uploads/products/1751375938_additional_1_reddress2.jpg
)

2025-07-01 15:18:58 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:18:58 - Uploading additional image - Data: Array
(
    [index] => 2
    [name] => reddress3.jpg
    [final_name] => 1751375938_additional_2_reddress3.jpg
    [upload_to] => ../../../uploads/products/1751375938_additional_2_reddress3.jpg
)

2025-07-01 15:18:58 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:18:58 - Processing variant images - Data: Array
(
    [name] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => reddress1.jpg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [full_path] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => reddress1.jpg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [type] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => image/jpeg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [tmp_name] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => C:\xampp\tmp\php79A5.tmp
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [error] => Array
        (
            [3] => Array
                (
                    [0] => 4
                )

            [2] => Array
                (
                    [0] => 4
                )

            [1] => Array
                (
                    [0] => 0
                )

            [4] => Array
                (
                    [0] => 4
                )

        )

    [size] => Array
        (
            [3] => Array
                (
                    [0] => 0
                )

            [2] => Array
                (
                    [0] => 0
                )

            [1] => Array
                (
                    [0] => 106140
                )

            [4] => Array
                (
                    [0] => 0
                )

        )

)

2025-07-01 15:18:58 - Processing variant images for color - Data: 3
2025-07-01 15:18:58 - Processing variant images for color - Data: 2
2025-07-01 15:18:58 - Processing variant images for color - Data: 1
2025-07-01 15:18:58 - Uploading variant image - Data: Array
(
    [color_id] => 1
    [filename] => reddress1.jpg
    [final_name] => 1751375938_variant_1_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1751375938_variant_1_0_reddress1.jpg
)

2025-07-01 15:18:58 - Variant image inserted - Data: Array
(
    [success] => true
    [error] => 
    [sql] => INSERT INTO product_variant_images (product_id, color_id, image, display_order)
                                   VALUES ('88', '1', '1751375938_variant_1_0_reddress1.jpg', 1)
)

2025-07-01 15:18:58 - Processing variant images for color - Data: 4
2025-07-01 15:18:58 - Redirecting to products.php
2025-07-01 15:26:48 - Script started
2025-07-01 15:26:48 - Form submitted - Data: Array
(
    [name] => variantsTest
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 22.50
    [price] => 172.50
    [quantity] => 25
    [barcode] => 007
    [description] => 
    [product_labels] => Array
        (
            [0] => 1
        )

    [enable_discounted_price] => 1
    [discounted_price] => 150
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
        )

    [variant_skus] => Array
        (
            [0] => skuM
        )

    [variant_additional_prices] => Array
        (
            [0] => 25
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-07-01 15:26:48 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => variantsTest
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 22.50
    [price] => 172.50
    [quantity] => 25
    [barcode] => 007
    [status] => 0
)

2025-07-01 15:26:48 - Main image uploaded successfully - Data: 1751376408_producthere.jpg
2025-07-01 15:26:48 - Discounted price set - Data: '150'
2025-07-01 15:26:48 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'variantsTest', '', '007', '99.95', '199.95',
            '15', '22.50', '172.50', '150', '25', '1751376408_producthere.jpg', '0', '2025-07-01 15:26:48',
            '0', '0', '1', '0'
        )
2025-07-01 15:26:48 - Query result - Data: Array
(
    [success] => true
    [product_id] => 89
    [error] => 
)

2025-07-01 15:26:48 - Product created successfully with ID: 89
2025-07-01 15:26:48 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 15:26:48 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 15:26:48 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [additional_prices] => Array
        (
            [0] => 25
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

)

2025-07-01 15:26:48 - Variant with additional price created - Data: Array
(
    [sku] => skuM
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 25
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 15:26:48 - Processing product label - Data: Array
(
    [product_id] => 89
    [label_id] => 1
)

2025-07-01 15:26:48 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:26:48 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => black-dress.webp
            [1] => reddress1.jpg
        )

    [full_path] => Array
        (
            [0] => black-dress.webp
            [1] => reddress1.jpg
        )

    [type] => Array
        (
            [0] => image/webp
            [1] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\phpA44E.tmp
            [1] => C:\xampp\tmp\phpA44F.tmp
        )

    [error] => Array
        (
            [0] => 0
            [1] => 0
        )

    [size] => Array
        (
            [0] => 7088
            [1] => 106140
        )

)

2025-07-01 15:26:48 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => black-dress.webp
    [final_name] => 1751376408_additional_0_black-dress.webp
    [upload_to] => ../../../uploads/products/1751376408_additional_0_black-dress.webp
)

2025-07-01 15:26:48 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:26:48 - Uploading additional image - Data: Array
(
    [index] => 1
    [name] => reddress1.jpg
    [final_name] => 1751376408_additional_1_reddress1.jpg
    [upload_to] => ../../../uploads/products/1751376408_additional_1_reddress1.jpg
)

2025-07-01 15:26:48 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 15:26:48 - Processing variant images - Data: Array
(
    [name] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => reddress4.jpg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [full_path] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => reddress4.jpg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [type] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => image/jpeg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [tmp_name] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => C:\xampp\tmp\phpA450.tmp
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [error] => Array
        (
            [3] => Array
                (
                    [0] => 4
                )

            [2] => Array
                (
                    [0] => 4
                )

            [1] => Array
                (
                    [0] => 0
                )

            [4] => Array
                (
                    [0] => 4
                )

        )

    [size] => Array
        (
            [3] => Array
                (
                    [0] => 0
                )

            [2] => Array
                (
                    [0] => 0
                )

            [1] => Array
                (
                    [0] => 55671
                )

            [4] => Array
                (
                    [0] => 0
                )

        )

)

2025-07-01 15:26:48 - Processing variant images for color - Data: 3
2025-07-01 15:26:48 - Processing variant images for color - Data: 2
2025-07-01 15:26:48 - Processing variant images for color - Data: 1
2025-07-01 15:26:48 - Uploading variant image - Data: Array
(
    [color_id] => 1
    [filename] => reddress4.jpg
    [final_name] => 1751376408_variant_1_0_reddress4.jpg
    [upload_to] => ../../../uploads/products/1751376408_variant_1_0_reddress4.jpg
)

2025-07-01 15:26:48 - Variant image inserted - Data: Array
(
    [success] => true
    [error] => 
    [sql] => INSERT INTO product_variant_images (product_id, color_id, image, display_order)
                                   VALUES ('89', '1', '1751376408_variant_1_0_reddress4.jpg', 1)
)

2025-07-01 15:26:48 - Processing variant images for color - Data: 4
2025-07-01 15:26:48 - Redirecting to products.php
2025-07-01 16:26:44 - Script started
2025-07-01 16:26:44 - Form submitted - Data: Array
(
    [name] => test90
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 15.00
    [price] => 115.00
    [quantity] => 25
    [barcode] => 007
    [description] => <p>test</p>
    [product_labels] => Array
        (
            [0] => 1
        )

    [enable_discounted_price] => 1
    [discounted_price] => 100
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
        )

    [variant_skus] => Array
        (
            [0] => skuM
        )

    [variant_additional_prices] => Array
        (
            [0] => 20
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-07-01 16:26:44 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => test90
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 15.00
    [price] => 115.00
    [quantity] => 25
    [barcode] => 007
    [status] => 0
)

2025-07-01 16:26:44 - Main image uploaded successfully - Data: 1751380004_black-dress.webp
2025-07-01 16:26:44 - Discounted price set - Data: '100'
2025-07-01 16:26:44 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'test90', '<p>test</p>', '007', '99.95', '199.95',
            '15', '15.00', '115.00', '100', '25', '1751380004_black-dress.webp', '0', '2025-07-01 16:26:44',
            '0', '0', '1', '0'
        )
2025-07-01 16:26:44 - Query result - Data: Array
(
    [success] => true
    [product_id] => 90
    [error] => 
)

2025-07-01 16:26:44 - Product created successfully with ID: 90
2025-07-01 16:26:44 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 16:26:44 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 16:26:44 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [additional_prices] => Array
        (
            [0] => 20
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

)

2025-07-01 16:26:44 - Variant with additional price created - Data: Array
(
    [sku] => skuM
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 20
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 16:26:44 - Processing product label - Data: Array
(
    [product_id] => 90
    [label_id] => 1
)

2025-07-01 16:26:44 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 16:26:44 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress1.jpg
        )

    [full_path] => Array
        (
            [0] => reddress1.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\php8314.tmp
        )

    [error] => Array
        (
            [0] => 0
        )

    [size] => Array
        (
            [0] => 106140
        )

)

2025-07-01 16:26:44 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress1.jpg
    [final_name] => 1751380004_additional_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1751380004_additional_0_reddress1.jpg
)

2025-07-01 16:26:44 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 16:26:44 - Processing variant images - Data: Array
(
    [name] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => reddress1.jpg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [full_path] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => reddress1.jpg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [type] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => image/jpeg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [tmp_name] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => C:\xampp\tmp\php8315.tmp
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [error] => Array
        (
            [3] => Array
                (
                    [0] => 4
                )

            [2] => Array
                (
                    [0] => 4
                )

            [1] => Array
                (
                    [0] => 0
                )

            [4] => Array
                (
                    [0] => 4
                )

        )

    [size] => Array
        (
            [3] => Array
                (
                    [0] => 0
                )

            [2] => Array
                (
                    [0] => 0
                )

            [1] => Array
                (
                    [0] => 106140
                )

            [4] => Array
                (
                    [0] => 0
                )

        )

)

2025-07-01 16:26:44 - Processing variant images for color - Data: 3
2025-07-01 16:26:44 - Processing variant images for color - Data: 2
2025-07-01 16:26:44 - Processing variant images for color - Data: 1
2025-07-01 16:26:44 - Uploading variant image - Data: Array
(
    [color_id] => 1
    [filename] => reddress1.jpg
    [final_name] => 1751380004_variant_1_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1751380004_variant_1_0_reddress1.jpg
)

2025-07-01 16:26:44 - Variant image inserted - Data: Array
(
    [success] => true
    [error] => 
    [sql] => INSERT INTO product_variant_images (product_id, color_id, image, display_order)
                                   VALUES ('90', '1', '1751380004_variant_1_0_reddress1.jpg', 1)
)

2025-07-01 16:26:44 - Processing variant images for color - Data: 4
2025-07-01 16:26:44 - Redirecting to products.php
2025-07-01 16:39:36 - Script started
2025-07-01 16:39:36 - Form submitted - Data: Array
(
    [name] => test91
    [category_id] => 82
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 22.50
    [price] => 172.50
    [quantity] => 20
    [barcode] => 007
    [description] => 
    [product_labels] => Array
        (
            [0] => 1
        )

    [enable_discounted_price] => 1
    [discounted_price] => 150
    [product_type_id] => 1
    [enable_variants] => 1
    [variant_pricing_type] => additional
    [variant_colors] => Array
        (
            [0] => 1
        )

    [variant_sizes] => Array
        (
            [0] => 1
        )

    [variant_color_ids] => Array
        (
            [0] => 1
        )

    [variant_size_ids] => Array
        (
            [0] => 1
        )

    [variant_skus] => Array
        (
            [0] => 
        )

    [variant_additional_prices] => Array
        (
            [0] => 25
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

    [bulk_min_qty] => Array
        (
            [0] => 2
        )

    [bulk_max_qty] => Array
        (
            [0] => 5
        )

    [bulk_price] => Array
        (
            [0] => 
        )

    [saveProduct] => 1
)

2025-07-01 16:39:36 - Basic product data - Data: Array
(
    [category_id] => 82
    [name] => test91
    [cost_price] => 99.95
    [sales_price] => 199.95
    [vat_percentage] => 15
    [vatT] => 22.50
    [price] => 172.50
    [quantity] => 20
    [barcode] => 007
    [status] => 0
)

2025-07-01 16:39:36 - Main image uploaded successfully - Data: 1751380776_producthere.jpg
2025-07-01 16:39:36 - Discounted price set - Data: '150'
2025-07-01 16:39:36 - SQL Query - Data: INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price,
            vat_percentage, vatT, price, discounted_price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '82', 'test91', '', '007', '99.95', '199.95',
            '15', '22.50', '172.50', '150', '20', '1751380776_producthere.jpg', '0', '2025-07-01 16:39:36',
            '0', '0', '1', '0'
        )
2025-07-01 16:39:36 - Query result - Data: Array
(
    [success] => true
    [product_id] => 91
    [error] => 
)

2025-07-01 16:39:36 - Product created successfully with ID: 91
2025-07-01 16:39:36 - Product type information - Data: Array
(
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-07-01 16:39:36 - Processing product variants - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [variant_pricing_type] => additional
)

2025-07-01 16:39:36 - Processing variants with additional pricing - Data: Array
(
    [color_ids] => Array
        (
            [0] => 1
        )

    [size_ids] => Array
        (
            [0] => 1
        )

    [additional_prices] => Array
        (
            [0] => 25
        )

    [variant_quantities] => Array
        (
            [0] => 10
        )

)

2025-07-01 16:39:36 - Variant with additional price created - Data: Array
(
    [sku] => TEST91-91-RED-SMA-1751380776
    [color_id] => 1
    [size_id] => 1
    [additional_price] => 25
    [quantity] => 10
    [success] => true
    [error] => 
)

2025-07-01 16:39:36 - Processing product label - Data: Array
(
    [product_id] => 91
    [label_id] => 1
)

2025-07-01 16:39:36 - Product label inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 16:39:36 - Processing additional images - Data: Array
(
    [name] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [full_path] => Array
        (
            [0] => reddress1.jpg
            [1] => reddress2.jpg
            [2] => reddress3.jpg
        )

    [type] => Array
        (
            [0] => image/jpeg
            [1] => image/jpeg
            [2] => image/jpeg
        )

    [tmp_name] => Array
        (
            [0] => C:\xampp\tmp\php49FC.tmp
            [1] => C:\xampp\tmp\php49FD.tmp
            [2] => C:\xampp\tmp\php49FE.tmp
        )

    [error] => Array
        (
            [0] => 0
            [1] => 0
            [2] => 0
        )

    [size] => Array
        (
            [0] => 106140
            [1] => 98139
            [2] => 46596
        )

)

2025-07-01 16:39:36 - Uploading additional image - Data: Array
(
    [index] => 0
    [name] => reddress1.jpg
    [final_name] => 1751380776_additional_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1751380776_additional_0_reddress1.jpg
)

2025-07-01 16:39:36 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 16:39:36 - Uploading additional image - Data: Array
(
    [index] => 1
    [name] => reddress2.jpg
    [final_name] => 1751380776_additional_1_reddress2.jpg
    [upload_to] => ../../../uploads/products/1751380776_additional_1_reddress2.jpg
)

2025-07-01 16:39:36 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 16:39:36 - Uploading additional image - Data: Array
(
    [index] => 2
    [name] => reddress3.jpg
    [final_name] => 1751380776_additional_2_reddress3.jpg
    [upload_to] => ../../../uploads/products/1751380776_additional_2_reddress3.jpg
)

2025-07-01 16:39:36 - Additional image inserted - Data: Array
(
    [success] => true
    [error] => 
)

2025-07-01 16:39:36 - Processing variant images - Data: Array
(
    [name] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => reddress1.jpg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [full_path] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => reddress1.jpg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [type] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => image/jpeg
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [tmp_name] => Array
        (
            [3] => Array
                (
                    [0] => 
                )

            [2] => Array
                (
                    [0] => 
                )

            [1] => Array
                (
                    [0] => C:\xampp\tmp\php49FF.tmp
                )

            [4] => Array
                (
                    [0] => 
                )

        )

    [error] => Array
        (
            [3] => Array
                (
                    [0] => 4
                )

            [2] => Array
                (
                    [0] => 4
                )

            [1] => Array
                (
                    [0] => 0
                )

            [4] => Array
                (
                    [0] => 4
                )

        )

    [size] => Array
        (
            [3] => Array
                (
                    [0] => 0
                )

            [2] => Array
                (
                    [0] => 0
                )

            [1] => Array
                (
                    [0] => 106140
                )

            [4] => Array
                (
                    [0] => 0
                )

        )

)

2025-07-01 16:39:36 - Processing variant images for color - Data: 3
2025-07-01 16:39:36 - Processing variant images for color - Data: 2
2025-07-01 16:39:36 - Processing variant images for color - Data: 1
2025-07-01 16:39:36 - Uploading variant image - Data: Array
(
    [color_id] => 1
    [filename] => reddress1.jpg
    [final_name] => 1751380776_variant_1_0_reddress1.jpg
    [upload_to] => ../../../uploads/products/1751380776_variant_1_0_reddress1.jpg
)

2025-07-01 16:39:36 - Variant image inserted - Data: Array
(
    [success] => true
    [error] => 
    [sql] => INSERT INTO product_variant_images (product_id, color_id, image, display_order)
                                   VALUES ('91', '1', '1751380776_variant_1_0_reddress1.jpg', 1)
)

2025-07-01 16:39:36 - Processing variant images for color - Data: 4
2025-07-01 16:39:36 - Redirecting to products.php
