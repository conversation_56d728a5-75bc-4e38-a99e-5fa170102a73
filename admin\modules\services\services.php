<?php
include('../../includes/header.php');

// Add the export function
function getAllServicesForExport() {
    global $conn;
    $sql = "SELECT * FROM services ORDER BY name ASC";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_execute($stmt);
    return mysqli_stmt_get_result($stmt);
}
?>

<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Services
                <a href="services-create.php" class="btn btn-primary float-end">Add Service</a>
                <a href="../../index.php" class="btn btn-dark float-end me-2">Dashboard <i class="fas fa-tachometer-alt"></i></a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <?php
            $services = getAllServicesForExport();
            if(!$services){
                echo '<h4>Something Went Wrong!</h4>';
                return false;
            }

            if(mysqli_num_rows($services) > 0)
            {
            ?>
            <form action="code.php" method="POST">
                <table id="exportTable" class="display">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Cost Price</th>
                            <th>Sales Price</th>
                            <th>VAT Percentage</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th>Featured</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($services as $item) : ?>
                        <tr>
                            <td><?= $item['name'] ?></td>
                            <td><?= $item['description'] ?></td>
                            <td><?= $item['cost_price'] ?></td>
                            <td><?= $item['sales_price'] ?></td>
                            <td><?= $item['vat_percentage'] ?></td>
                            <td><?= $item['price'] ?></td>
                            <td><?= $item['quantity'] ?></td>
                            <td>
                                <input type="checkbox" name="featured[]" value="<?= $item['id']; ?>" 
                                    <?= ($item['featured'] == 1) ? 'checked' : ''; ?> />
                            </td>
                            <td><?= $item['status'] == 1 ? 'Hidden' : 'Visible' ?></td>
                            <td>
                                <a href="services-edit.php?id=<?= $item['id']; ?>" class="btn btn-success btn-sm">Edit</a>
                                <button type="button" class="btn btn-danger btn-sm delete-service" value="<?= $item['id']; ?>">Delete</button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="mt-3">
                    <button type="submit" name="saveServicesUpdateFeatured" class="btn btn-primary">Save Update Featured</button>
                </div>
            </form>
            <?php
            }
            else
            {
                echo '<h4 class="mb-0">No Record found</h4>';
            }
            ?>
        </div>
    </div>
</div>

<?php include('../../includes/footer.php'); ?>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

<script>
$(document).ready(function() {
    $('#exportTable').DataTable({
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'copy',
                className: 'btn btn-secondary btn-sm',
                text: '<i class="fas fa-copy"></i> Copy'
            },
            {
                extend: 'csv',
                className: 'btn btn-primary btn-sm',
                text: '<i class="fas fa-file-csv"></i> CSV'
            },
            {
                extend: 'excel',
                className: 'btn btn-success btn-sm',
                text: '<i class="fas fa-file-excel"></i> Excel'
            },
            {
                extend: 'pdf',
                className: 'btn btn-danger btn-sm',
                text: '<i class="fas fa-file-pdf"></i> PDF'
            },
            {
                extend: 'print',
                className: 'btn btn-info btn-sm',
                text: '<i class="fas fa-print"></i> Print'
            }
        ]
    });
});
</script>

</body>
<script
