<?php
// Include session manager at the very beginning
require_once 'session_manager.php';

// Include database connection
include('admin/config/dbcon.php');

// Include quotation functions
require_once 'quotation_functions.php';

// Get pagination parameters from URL
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$items_per_page = isset($_GET['items']) ? (int)$_GET['items'] : 6;
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'name_asc';
// '0' means all categories
$category_filter = isset($_GET['category']) ? (string)$_GET['category'] : '0';
// Handle empty string case
if ($category_filter === '') {
    $category_filter = '0';
}

// Get all active categories for the filter dropdown
function getActiveCategories() {
    global $conn;
    $sql = "SELECT c.*, p.name as parent_name
            FROM categories c
            LEFT JOIN categories p ON c.parent_id = p.id
            WHERE c.status = 0
            ORDER BY
                CASE WHEN c.parent_id IS NULL THEN 0 ELSE 1 END,
                p.name ASC,
                c.name ASC";
    $result = mysqli_query($conn, $sql);
    $categories = [];

    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            $categories[] = $row;
        }
    }

    return $categories;
}

$categories = getActiveCategories();

// Validate parameters
if ($page < 1) $page = 1;
if (!in_array($items_per_page, [6, 12, 24])) $items_per_page = 6;

// Determine sort order
$order_by = 'name ASC'; // Default
switch ($sort_by) {
    case 'price_asc':
        $order_by = 'CASE WHEN vat_percentage = 0 THEN sales_price ELSE price END ASC';
        break;
    case 'price_desc':
        $order_by = 'CASE WHEN vat_percentage = 0 THEN sales_price ELSE price END DESC';
        break;
    case 'name_desc':
        $order_by = 'name DESC';
        break;
    case 'newest':
        $order_by = 'id DESC';
        break;
    case 'category_asc':
        $order_by = 'category_name ASC, name ASC';
        break;
    case 'category_desc':
        $order_by = 'category_name DESC, name ASC';
        break;
    default:
        $order_by = 'name ASC';
}

// Count total products
function getTotalProducts() {
    global $conn, $category_filter;
    $sql = "SELECT COUNT(*) as total FROM products WHERE status = 0";

    // Add category filter if selected
    if ($category_filter !== '0' && !empty($category_filter)) {
        // Include products from both primary and secondary categories
        $sql .= " AND (category_id = '" . mysqli_real_escape_string($conn, $category_filter) . "'
                OR id IN (SELECT product_id FROM product_secondary_categories WHERE category_id = '" . mysqli_real_escape_string($conn, $category_filter) . "'))";
    }

    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return $row['total'];
}

// Fetch products with pagination and sorting
function getActiveProducts($page, $items_per_page, $order_by) {
    global $conn, $category_filter;

    // Calculate offset
    $offset = ($page - 1) * $items_per_page;

    // Build the base query
    $sql = "SELECT p.id, p.name, p.description, p.price, p.sales_price, p.vat_percentage, p.image, p.status,
            c.name as category_name,
            (SELECT COUNT(*) FROM product_variants WHERE product_id = p.id) as has_variants,
            (SELECT COUNT(*) FROM product_bulk_pricing WHERE product_id = p.id) as has_bulk_pricing
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 0 AND (p.featured = 1 OR p.is_featured = 1)";

    // Add category filter if selected
    if ($category_filter !== '0' && !empty($category_filter)) {
        // Include products from both primary and secondary categories
        $sql .= " AND (p.category_id = '" . mysqli_real_escape_string($conn, $category_filter) . "'
                OR p.id IN (SELECT product_id FROM product_secondary_categories WHERE category_id = '" . mysqli_real_escape_string($conn, $category_filter) . "'))";
    }

    // Add sorting and pagination
    $sql .= " ORDER BY $order_by LIMIT ?, ?";

    // For debugging
    global $debug_enabled;
    if ($debug_enabled) {
        $debug_sql = $sql;
        $debug_sql = str_replace('?, ?', $offset . ', ' . $items_per_page, $debug_sql);
        $GLOBALS['actual_sql'] = $debug_sql;
    }

    $stmt = mysqli_prepare($conn, $sql);
    if (!$stmt) {
        if ($debug_enabled) {
            echo "<div class='alert alert-danger'>Prepare failed: " . mysqli_error($conn) . "</div>";
        }
        return [];
    }

    mysqli_stmt_bind_param($stmt, 'ii', $offset, $items_per_page);
    $execute_result = mysqli_stmt_execute($stmt);

    if (!$execute_result) {
        if ($debug_enabled) {
            echo "<div class='alert alert-danger'>Execute failed: " . mysqli_stmt_error($stmt) . "</div>";
        }
        return [];
    }

    $result = mysqli_stmt_get_result($stmt);
    if (!$result) {
        if ($debug_enabled) {
            echo "<div class='alert alert-danger'>Get result failed: " . mysqli_stmt_error($stmt) . "</div>";
        }
        return [];
    }

    $products = mysqli_fetch_all($result, MYSQLI_ASSOC);

    // Add variant and bulk pricing details to each product
    foreach ($products as &$product) {
        // Get variants if any
        if ($product['has_variants'] > 0) {
            $variantsQuery = "SELECT pv.*, pv.additional_price, pc.name as color_name, pc.color_code, ps.name as size_name
                              FROM product_variants pv
                              LEFT JOIN product_colors pc ON pv.color_id = pc.id
                              LEFT JOIN product_sizes ps ON pv.size_id = ps.id
                              WHERE pv.product_id = ? AND pv.status = 0";
            $variantsStmt = mysqli_prepare($conn, $variantsQuery);
            mysqli_stmt_bind_param($variantsStmt, 'i', $product['id']);
            mysqli_stmt_execute($variantsStmt);
            $variantsResult = mysqli_stmt_get_result($variantsStmt);
            $product['variants'] = mysqli_fetch_all($variantsResult, MYSQLI_ASSOC);

            // Get unique colors and sizes with their additional prices
            $product['colors'] = [];
            $product['sizes'] = [];
            $color_ids = []; // To track unique color IDs
            $size_ids = []; // To track unique size IDs

            // First, collect all unique colors and sizes
            foreach ($product['variants'] as $variant) {
                if (!empty($variant['color_id']) && !in_array($variant['color_id'], $color_ids)) {
                    $color_ids[] = $variant['color_id'];
                    $product['colors'][] = [
                        'id' => $variant['color_id'],
                        'name' => $variant['color_name'],
                        'color_code' => $variant['color_code'],
                        'additional_price' => 0 // Will be updated later
                    ];
                }

                if (!empty($variant['size_id']) && !in_array($variant['size_id'], $size_ids)) {
                    $size_ids[] = $variant['size_id'];
                    $product['sizes'][] = [
                        'id' => $variant['size_id'],
                        'name' => $variant['size_name'],
                        'additional_price' => 0 // Will be updated later
                    ];
                }
            }

            // Now, update the additional price for each color and size
            foreach ($product['variants'] as $variant) {
                // Find the color in our array
                foreach ($product['colors'] as &$color) {
                    if ($color['id'] == $variant['color_id'] && $variant['additional_price'] > $color['additional_price']) {
                        $color['additional_price'] = $variant['additional_price'];
                    }
                }

                // Find the size in our array
                foreach ($product['sizes'] as &$size) {
                    if ($size['id'] == $variant['size_id'] && $variant['additional_price'] > $size['additional_price']) {
                        $size['additional_price'] = $variant['additional_price'];
                    }
                }
            }
        }

        // Get bulk pricing if any
        if ($product['has_bulk_pricing'] > 0) {
            $bulkQuery = "SELECT * FROM product_bulk_pricing WHERE product_id = ? ORDER BY min_quantity ASC";
            $bulkStmt = mysqli_prepare($conn, $bulkQuery);
            mysqli_stmt_bind_param($bulkStmt, 'i', $product['id']);
            mysqli_stmt_execute($bulkStmt);
            $bulkResult = mysqli_stmt_get_result($bulkStmt);
            $product['bulk_pricing'] = mysqli_fetch_all($bulkResult, MYSQLI_ASSOC);
        }
    }

    return $products;
}

// Get total products and calculate pagination
$total_products = getTotalProducts();
$total_pages = ceil($total_products / $items_per_page);

// Get products for current page
$products = getActiveProducts($page, $items_per_page, $order_by);

// Debug information
$debug_enabled = isset($_GET['debug']) && $_GET['debug'] === '1';
if ($debug_enabled) {
    // Build the query to show in debug
    $debug_sql = "SELECT p.id, p.name, p.category_id, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 0";

    if ($category_filter !== '0' && !empty($category_filter)) {
        $debug_sql .= " AND (p.category_id = '" . mysqli_real_escape_string($conn, $category_filter) . "')";
    }

    $debug_sql .= " ORDER BY $order_by LIMIT " . (($page - 1) * $items_per_page) . ", $items_per_page";

    echo "<div style='background: #f8f9fa; padding: 15px; margin-bottom: 20px; border: 1px solid #ddd;'>";
    echo "<h4>Debug Information</h4>";
    echo "<p>Category Filter: '" . htmlspecialchars($category_filter) . "'</p>";
    echo "<p>Total Products: " . $total_products . "</p>";
    echo "<p>Products Found: " . count($products) . "</p>";
    echo "<p>Current Page: " . $page . " of " . $total_pages . "</p>";
    echo "<p>SQL Query (Debug): " . htmlspecialchars($debug_sql) . "</p>";
    if (isset($GLOBALS['actual_sql'])) {
        echo "<p>SQL Query (Actual): " . htmlspecialchars($GLOBALS['actual_sql']) . "</p>";
    }

    // Show the products array
    echo "<h5>Products Array:</h5>";
    echo "<table border='1' style='font-size: 12px;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Category ID</th><th>Category Name</th></tr>";
    foreach ($products as $product) {
        echo "<tr>";
        echo "<td>" . ($product['id'] ?? 'N/A') . "</td>";
        echo "<td>" . ($product['name'] ?? 'N/A') . "</td>";
        echo "<td>" . ($product['category_id'] ?? 'N/A') . "</td>";
        echo "<td>" . ($product['category_name'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Products | Custom Applications</title>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <meta name="description" content="Browse our product catalog. Custom Applications offers a range of solutions for your business needs.">

    <link href="assets/img/favicon.png" rel="icon">
    <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Jost:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <link href="assets_site/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets_site/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="assets_site/vendor/aos/aos.css" rel="stylesheet">
    <link href="assets_site/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="assets_site/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <link href="assets_site/css/main.css" rel="stylesheet">

    <style>
        /* Add margin to account for fixed header */
        #products {
            margin-top: 120px;
            padding-bottom: 60px;
        }

        /* Product Grid Styling */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 30px;
            padding: 15px;
            background-color: #f0f2f5;
            border-radius: 12px;
        }

        .product-card {
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: visible; /* Changed from hidden to visible */
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            border: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            background-color: #fff;
        }

        .product-image-container {
            height: 200px;
            overflow: hidden;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            border-radius: 10px 10px 0 0;
        }

        .product-image {
            max-width: 100%;
            max-height: 100%;
            width: 100%;
            height: 170px; /* Fixed height */
            object-fit: contain;
            transition: transform 0.5s ease;
            padding: 5px;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .product-body {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            background-color: #f8f9fa;
            border-radius: 0 0 10px 10px;
        }

        .product-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            height: 50px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .product-price {
            font-size: 20px;
            font-weight: 700;
            color: #28a745;
            margin-top: auto;
            margin-bottom: 15px;
            text-align: center;
        }

        .product-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            background-color: #f8f9fa;
            padding-top: 5px;
            border-top: 1px solid #e9ecef;
            position: relative;
            z-index: 10;
        }

        .btn-quick-view, .btn-whatsapp {
            flex: 1;
            color: white;
            border: none;
            padding: 10px 0;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-size: 14px;
        }

        .btn-quick-view {
            background-color: #007bff;
        }

        .btn-quick-view:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            color: white;
        }

        .btn-whatsapp {
            background-color: #25D366;
            position: relative;
            z-index: 5;
            cursor: pointer;
        }

        .btn-whatsapp:hover {
            background-color: #128C7E;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            color: white;
        }

        /* Section Title Styling */
        .products-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .products-title {
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
        }

        .products-title:after {
            content: '';
            position: absolute;
            width: 50px;
            height: 3px;
            background-color: #007bff;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }

        .products-subtitle {
            color: #666;
            font-size: 16px;
            max-width: 700px;
            margin: 0 auto;
        }

        /* Empty State Styling */
        .no-products {
            text-align: center;
            padding: 50px 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-top: 30px;
        }

        .no-products i {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 20px;
            display: block;
        }

        .no-products h4 {
            color: #666;
            margin-bottom: 10px;
        }

        .no-products p {
            color: #999;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .product-grid {
                grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
                gap: 15px;
            }

            .product-image-container {
                height: 180px;
            }

            .product-title {
                font-size: 16px;
            }

            .product-price {
                font-size: 18px;
            }
        }

        @media (max-width: 576px) {
            .product-grid {
                grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
            }

            .products-title {
                font-size: 28px;
            }
        }

        /* Modal Styling */
        .modal-image-container {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            border-radius: 8px;
            height: 300px;
            overflow: hidden;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .modal-image-container img {
            max-width: 100%;
            max-height: 280px;
            width: auto;
            height: auto;
            object-fit: contain;
            padding: 10px;
        }

        .no-image-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            color: #aaa;
            text-align: center;
        }

        .no-image-placeholder i {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .product-modal-price {
            font-size: 24px;
            font-weight: 700;
            color: #28a745;
            margin-bottom: 20px;
            padding: 10px 15px;
            background-color: #f0f9f0;
            border-radius: 50px;
            display: inline-block;
        }

        .product-modal-description {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #555;
        }

        .product-modal-description h1,
        .product-modal-description h2,
        .product-modal-description h3,
        .product-modal-description h4 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .product-modal-description p {
            margin-bottom: 1rem;
        }

        .modal-footer {
            justify-content: space-between;
        }

        .modal-footer .btn {
            padding: 8px 16px;
            border-radius: 50px;
        }

        /* Sorting and Filtering Controls */
        .product-controls {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .form-select {
            border-radius: 50px;
            padding: 8px 15px;
            border: 1px solid #ced4da;
            cursor: pointer;
            background-color: white;
            transition: all 0.3s ease;
            min-width: 180px; /* Ensure minimum width for dropdowns */
        }

        /* Fix for label display */
        .control-label {
            white-space: nowrap;
            margin-right: 10px;
            font-weight: 500;
        }

        .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* Pagination Styling */
        .pagination-container {
            margin-top: 40px;
            margin-bottom: 20px;
        }

        .pagination .page-link {
            color: #007bff;
            border-radius: 50%;
            margin: 0 5px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .pagination .page-item.active .page-link {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }

        .pagination .page-link:hover {
            background-color: #e9ecef;
            border-color: #dee2e6;
            color: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,0.1);
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #0069d9;
            border-color: #0062cc;
            color: white;
        }

        /* Fix for modal positioning */
        .modal {
            z-index: 1050;
        }

        .modal-backdrop {
            z-index: 1040;
        }

        .modal-dialog {
            max-width: 800px;
            margin: 1.75rem auto;
        }

        /* Fix for inquiry form */
        div[id^="inquiryModal"] {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            outline: 0;
        }
    </style>
</head>
<body>

<!-- Include Header -->
<?php include('includes_site/header.php'); ?>

<section id="products">
    <div class="container">
        <div class="products-header">
            <h1 class="products-title">Our Featured Products</h1>
            <p class="products-subtitle">Discover our selection of high-quality products designed to meet your needs</p>
        </div>

        <!-- Sorting and Filtering Controls with Help Button -->
        <div class="product-controls bg-light p-3 rounded mb-4 shadow-sm">
            <div class="row g-3 align-items-center">
                <!-- Help Button -->
                <div class="col-md-2 col-lg-1">
                    <button class="btn btn-sm btn-success w-100" type="button" id="productAudioButton" title="Listen to Product List Help">
                        Help
                    </button>
                </div>

                <!-- Category Filter -->
                <div class="col-md-5 col-lg-4">
                    <div class="d-flex align-items-center">
                        <label for="category-filter" class="control-label me-2 fw-bold">Category:</label>
                        <select id="category-filter" class="form-select" onchange="window.location.href=this.value">
                            <option value="?category=0&sort=<?= $sort_by ?>&items=<?= $items_per_page ?>&page=1" <?= $category_filter === '0' ? 'selected' : '' ?>>All Categories</option>
                            <?php
                            // Organize categories by parent
                            $main_categories = [];
                            $sub_categories = [];

                            foreach($categories as $cat) {
                                if(empty($cat['parent_id'])) {
                                    $main_categories[] = $cat;
                                } else {
                                    $sub_categories[] = $cat;
                                }
                            }

                            // Display main categories first
                            foreach($main_categories as $main_cat): ?>
                                <option value="?category=<?= $main_cat['id'] ?>&sort=<?= $sort_by ?>&items=<?= $items_per_page ?>&page=1"
                                        <?= $category_filter === (string)$main_cat['id'] ? 'selected' : '' ?>
                                        style="font-weight: bold;">
                                    <?= htmlspecialchars($main_cat['name']) ?>
                                </option>

                                <?php
                                // Display subcategories under this main category
                                foreach($sub_categories as $sub_cat):
                                    if($sub_cat['parent_id'] == $main_cat['id']): ?>
                                        <option value="?category=<?= $sub_cat['id'] ?>&sort=<?= $sort_by ?>&items=<?= $items_per_page ?>&page=1"
                                                <?= $category_filter === (string)$sub_cat['id'] ? 'selected' : '' ?>>
                                            &nbsp;&nbsp;&nbsp;└─ <?= htmlspecialchars($sub_cat['name']) ?>
                                        </option>
                                    <?php endif;
                                endforeach;
                            endforeach; ?>
                        </select>
                    </div>
                </div>

                <!-- Sort Options -->
                <div class="col-md-5 col-lg-4">
                    <div class="d-flex align-items-center">
                        <label for="sort-options" class="control-label me-2 fw-bold">Sort by:</label>
                        <select id="sort-options" class="form-select" onchange="window.location.href=this.value">
                            <option value="?category=<?= $category_filter ?>&sort=name_asc&items=<?= $items_per_page ?>&page=1" <?= $sort_by == 'name_asc' ? 'selected' : '' ?>>Name (A-Z)</option>
                            <option value="?category=<?= $category_filter ?>&sort=name_desc&items=<?= $items_per_page ?>&page=1" <?= $sort_by == 'name_desc' ? 'selected' : '' ?>>Name (Z-A)</option>
                            <option value="?category=<?= $category_filter ?>&sort=price_asc&items=<?= $items_per_page ?>&page=1" <?= $sort_by == 'price_asc' ? 'selected' : '' ?>>Price (Low to High)</option>
                            <option value="?category=<?= $category_filter ?>&sort=price_desc&items=<?= $items_per_page ?>&page=1" <?= $sort_by == 'price_desc' ? 'selected' : '' ?>>Price (High to Low)</option>
                            <option value="?category=<?= $category_filter ?>&sort=newest&items=<?= $items_per_page ?>&page=1" <?= $sort_by == 'newest' ? 'selected' : '' ?>>Newest First</option>
                        </select>
                    </div>
                </div>

                <!-- Items Per Page -->
                <div class="col-md-12 col-lg-3">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <label for="items-per-page" class="control-label me-2 fw-bold">Show:</label>
                        <select id="items-per-page" class="form-select" style="width: auto;" onchange="window.location.href=this.value">
                            <option value="?category=<?= $category_filter ?>&sort=<?= $sort_by ?>&items=6&page=1" <?= $items_per_page == 6 ? 'selected' : '' ?>>6 items</option>
                            <option value="?category=<?= $category_filter ?>&sort=<?= $sort_by ?>&items=12&page=1" <?= $items_per_page == 12 ? 'selected' : '' ?>>12 items</option>
                            <option value="?category=<?= $category_filter ?>&sort=<?= $sort_by ?>&items=24&page=1" <?= $items_per_page == 24 ? 'selected' : '' ?>>24 items</option>
                        </select>
                    </div>
                </div>
            </div>


        <?php if(!empty($products)): ?>
            <div class="product-grid">
                <?php foreach($products as $item): ?>
                    <div class="product-card">
                        <div class="product-image-container">
                            <?php
                            $image_path = "uploads/products/" . $item['image'];
                            if(!empty($item['image']) && file_exists($image_path)): ?>
                                <img src="<?= $image_path ?>"
                                     class="product-image"
                                     alt="<?= htmlspecialchars($item['name']) ?>"
                                     data-product-id="<?= $item['id'] ?>"
                                     onerror="this.onerror=null; this.src='assets_site/img/placeholder.jpg'; this.alt='No Image Available';" />
                            <?php else: ?>
                                <div style="color:#aaa;font-size:14px;text-align:center;width:100%;height:200px;display:flex;align-items:center;justify-content:center;flex-direction:column;background:#f8f9fa;">
                                    <i class="bi bi-image" style="font-size:32px;display:block;margin-bottom:10px;"></i>
                                    No Image Available
                                </div>
                            <?php endif; ?>

                            <?php
                            // Get product labels
                            $labels_query = "SELECT l.* FROM product_labels l
                                            JOIN product_label_assignments a ON l.id = a.label_id
                                            WHERE a.product_id = ? AND l.status = 0";
                            $stmt = mysqli_prepare($conn, $labels_query);
                            mysqli_stmt_bind_param($stmt, "i", $item['id']);
                            mysqli_stmt_execute($stmt);
                            $labels_result = mysqli_stmt_get_result($stmt);

                            if(mysqli_num_rows($labels_result) > 0):
                            ?>
                            <div class="product-labels" style="position: absolute; top: 10px; left: 10px; display: flex; flex-direction: column; gap: 5px;">
                                <?php while($label = mysqli_fetch_assoc($labels_result)): ?>
                                    <span class="badge" style="background-color: <?= $label['background_color'] ?>; color: <?= $label['text_color'] ?>; padding: 5px 10px; font-size: 0.8rem;">
                                        <?= htmlspecialchars($label['name']) ?>
                                    </span>
                                <?php endwhile; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="product-body">
                            <h3 class="product-title"><?= htmlspecialchars($item['name']) ?></h3>
                            <?php if(!empty($item['category_name'])): ?>
                            <div class="product-category" style="font-size: 0.85rem; color: #6c757d; margin-bottom: 8px;">
                                <i class="bi bi-tag"></i> <?= htmlspecialchars($item['category_name']) ?>
                            </div>
                            <?php endif; ?>

                            <?php if(isset($item['has_variants']) && $item['has_variants'] > 0): ?>
                            <div class="product-variants-indicator" style="font-size: 0.85rem; color: #007bff; margin-bottom: 8px;">
                                <i class="bi bi-palette"></i>
                                <?php if(!empty($item['colors']) && !empty($item['sizes'])): ?>
                                    <?= count($item['colors']) ?> colors, <?= count($item['sizes']) ?> sizes available
                                <?php elseif(!empty($item['colors'])): ?>
                                    <?= count($item['colors']) ?> colors available
                                <?php elseif(!empty($item['sizes'])): ?>
                                    <?= count($item['sizes']) ?> sizes available
                                <?php else: ?>
                                    Options available
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>

                            <?php if(isset($item['has_bulk_pricing']) && $item['has_bulk_pricing'] > 0): ?>
                            <div class="product-bulk-indicator" style="font-size: 0.85rem; color: #28a745; margin-bottom: 8px;">
                                <i class="bi bi-box-seam"></i> Bulk pricing available
                            </div>
                            <?php endif; ?>
                            <div class="product-price">
                                <?php if($item['vat_percentage'] == 0): ?>
                                    R <?= number_format(htmlspecialchars($item['sales_price']), 2) ?>
                                <?php else: ?>
                                    R <?= number_format(htmlspecialchars($item['price']), 2) ?>
                                <?php endif; ?>
                            </div>
                            <div class="product-actions">
                                <a href="javascript:void(0)" class="btn-quick-view" data-bs-toggle="modal" data-bs-target="#productModal<?= $item['id'] ?>">
                                    <i class="bi bi-eye"></i> Quick View
                                </a>
                                <?php if(isset($item['has_variants']) && $item['has_variants'] > 0): ?>
                                <a href="javascript:void(0)" class="btn-select-options" data-bs-toggle="modal" data-bs-target="#productModal<?= $item['id'] ?>" style="background-color: #dc3545; color: white; border: none; padding: 10px 0; border-radius: 50px; font-weight: 600; transition: all 0.3s ease; text-decoration: none; display: inline-block; text-align: center; font-size: 14px; flex: 1;">
                                    <i class="bi bi-list-check"></i> Select Options
                                </a>
                                <?php else: ?>
                                <button type="button" class="btn-add-to-quote" onclick="addToQuotation(<?= $item['id'] ?>)" style="background-color: #fd7e14; color: white; border: none; padding: 10px 0; border-radius: 50px; font-weight: 600; transition: all 0.3s ease; text-decoration: none; display: inline-block; text-align: center; font-size: 14px; flex: 1;">
                                    <i class="bi bi-cart-plus"></i> Add to Quote
                                </button>
                                <?php endif; ?>
                            </div>
                            <div class="product-actions mt-2">
                                <button type="button" class="btn-whatsapp" data-bs-toggle="modal" data-bs-target="#inquiryModal<?= $item['id'] ?>" style="width: 100%;">
                                    <i class="bi bi-envelope"></i> Inquire
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Product Inquiry Modals -->
            <?php foreach($products as $item): ?>
            <div class="modal fade" id="inquiryModal<?= $item['id'] ?>" tabindex="-1" aria-labelledby="inquiryModalLabel<?= $item['id'] ?>" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="inquiryModalLabel<?= $item['id'] ?>">Inquire About <?= htmlspecialchars($item['name']) ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="inquiryForm<?= $item['id'] ?>" action="process_product_inquiry.php" method="post">
                                <input type="hidden" name="product_name" value="<?= htmlspecialchars($item['name']) ?> (ID:<?= $item['id'] ?>)">
                                <div class="mb-3">
                                    <label for="name<?= $item['id'] ?>" class="form-label">Your Name</label>
                                    <input type="text" class="form-control" id="name<?= $item['id'] ?>" name="name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="number<?= $item['id'] ?>" class="form-label">Phone Number</label>
                                    <input type="text" class="form-control" id="number<?= $item['id'] ?>" name="number" required>
                                </div>
                                <div class="mb-3">
                                    <label for="email<?= $item['id'] ?>" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email<?= $item['id'] ?>" name="email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="message<?= $item['id'] ?>" class="form-label">Message</label>
                                    <textarea class="form-control" id="message<?= $item['id'] ?>" name="message" rows="3" required></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" form="inquiryForm<?= $item['id'] ?>" class="btn btn-primary">Submit & Continue to WhatsApp</button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>

            <!-- Pagination Controls -->
            <?php if($total_pages > 1): ?>
            <div class="pagination-container mt-5">
                <nav aria-label="Product pagination">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page Link -->
                        <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                            <a class="page-link" href="?category=<?= $category_filter ?>&sort=<?= $sort_by ?>&items=<?= $items_per_page ?>&page=<?= $page-1 ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>

                        <!-- Page Number Links -->
                        <?php for($i = 1; $i <= $total_pages; $i++): ?>
                            <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                <a class="page-link" href="?category=<?= $category_filter ?>&sort=<?= $sort_by ?>&items=<?= $items_per_page ?>&page=<?= $i ?>"><?= $i ?></a>
                            </li>
                        <?php endfor; ?>

                        <!-- Next Page Link -->
                        <li class="page-item <?= ($page >= $total_pages) ? 'disabled' : '' ?>">
                            <a class="page-link" href="?category=<?= $category_filter ?>&sort=<?= $sort_by ?>&items=<?= $items_per_page ?>&page=<?= $page+1 ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                <div class="text-center mt-3 text-muted">
                    <small>Showing <?= count($products) ?> of <?= $total_products ?> products</small>
                </div>
            </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="no-products">
                <i class="bi bi-bag-x"></i>
                <h4>No Products Available</h4>
                <p>Check back soon for our latest offerings</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Product Modals -->
<?php if(!empty($products)): ?>
    <?php foreach($products as $item): ?>
        <div class="modal fade" id="productModal<?= $item['id'] ?>" tabindex="-1" aria-labelledby="productModalLabel<?= $item['id'] ?>" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="productModalLabel<?= $item['id'] ?>"><?= htmlspecialchars($item['name']) ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="modal-image-container">
                                    <?php if(!empty($item['image'])): ?>
                                        <img src="uploads/products/<?= htmlspecialchars($item['image']) ?>"
                                             class="img-fluid rounded"
                                             alt="<?= htmlspecialchars($item['name']) ?>"
                                             onerror="this.onerror=null; this.src='assets_site/img/placeholder.jpg'; this.alt='No Image Available';" />
                                    <?php else: ?>
                                        <div class="no-image-placeholder">
                                            <i class="bi bi-image"></i>
                                            <p>No Image Available</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <?php if(!empty($item['category_name'])): ?>
                                <div class="product-modal-category mb-2" style="font-size: 0.9rem; color: #6c757d;">
                                    <i class="bi bi-tag"></i> Category: <strong><?= htmlspecialchars($item['category_name']) ?></strong>
                                </div>
                                <?php endif; ?>
                                <h4 class="product-modal-price" id="price-<?= $item['id'] ?>">
                                    <?php if($item['vat_percentage'] == 0): ?>
                                        R <?= number_format(htmlspecialchars($item['sales_price']), 2) ?>
                                    <?php else: ?>
                                        R <?= number_format(htmlspecialchars($item['price']), 2) ?>
                                    <?php endif; ?>
                                </h4>

                                <!-- Variant details will be displayed here -->
                                <div id="variant-details-<?= $item['id'] ?>" class="mt-2"></div>
                                <div class="product-modal-description">
                                    <?php echo html_entity_decode($item['description']); ?>
                                </div>

                                <?php if(isset($item['has_variants']) && $item['has_variants'] > 0): ?>
                                <div class="product-variants-section mt-4">
                                    <h5 class="mb-3">Available Options</h5>

                                    <?php if(!empty($item['colors'])): ?>
                                    <div class="mb-3">
                                        <label class="form-label">Colors:</label>
                                        <div class="d-flex flex-wrap gap-2">
                                            <?php foreach($item['colors'] as $color): ?>
                                                <div class="color-option" data-color-id="<?= $color['id'] ?>" data-product-id="<?= $item['id'] ?>" data-additional-price="<?= $color['additional_price'] ?? 0 ?>" onclick="selectColor(<?= $item['id'] ?>, <?= $color['id'] ?>, '<?= htmlspecialchars($color['name']) ?>', <?= $color['additional_price'] ?? 0 ?>)">
                                                    <div class="color-swatch" style="width: 40px; height: 40px; background-color: <?= $color['color_code'] ?>; border: 2px solid #ddd; border-radius: 50%; cursor: pointer; transition: all 0.2s ease;"
                                                         title="<?= htmlspecialchars($color['name']) ?><?= isset($color['additional_price']) && $color['additional_price'] > 0 ? ' (+R '.number_format($color['additional_price'], 2).')' : '' ?>"></div>
                                                    <small class="d-block mt-1 text-center">
                                                        <?= htmlspecialchars($color['name']) ?>
                                                        <?php if(isset($color['additional_price']) && $color['additional_price'] > 0): ?>
                                                            <span class="text-danger">(+R <?= number_format($color['additional_price'], 2) ?>)</span>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if(!empty($item['sizes'])): ?>
                                    <div class="mb-3">
                                        <label class="form-label">Sizes:</label>
                                        <div class="d-flex flex-wrap gap-2">
                                            <?php foreach($item['sizes'] as $size): ?>
                                                <div class="size-option" data-size-id="<?= $size['id'] ?>" data-product-id="<?= $item['id'] ?>" data-additional-price="<?= $size['additional_price'] ?? 0 ?>">
                                                    <div class="size-box" style="min-width: 50px; height: 50px; border: 1px solid #ddd; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer; font-weight: bold; transition: all 0.2s ease; background-color: #f8f9fa;">
                                                        <span><?= htmlspecialchars($size['name']) ?></span>
                                                        <?php if(isset($size['additional_price']) && $size['additional_price'] > 0): ?>
                                                            <span class="text-danger" style="font-size: 10px; font-weight: normal;">+R <?= number_format($size['additional_price'], 2) ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>

                                <?php if(isset($item['has_bulk_pricing']) && $item['has_bulk_pricing'] > 0): ?>
                                <div class="bulk-pricing-section mt-4">
                                    <h5 class="mb-3">Bulk Pricing</h5>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Quantity</th>
                                                    <th>Price per Unit</th>
                                                    <th>Discount</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $basePrice = $item['vat_percentage'] > 0 ? $item['price'] : $item['sales_price'];
                                                foreach($item['bulk_pricing'] as $pricing):
                                                    $discount = $basePrice - $pricing['price'];
                                                    $discountPercentage = ($discount / $basePrice) * 100;
                                                ?>
                                                <tr>
                                                    <td>
                                                        <?php if($pricing['min_quantity'] == $pricing['max_quantity']): ?>
                                                            <?= $pricing['min_quantity'] ?>
                                                        <?php else: ?>
                                                            <?= $pricing['min_quantity'] ?> - <?= $pricing['max_quantity'] ?>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>R <?= number_format($pricing['price'], 2) ?></td>
                                                    <td>
                                                        <?php if($discount > 0): ?>
                                                            <span class="text-success">
                                                                <?= number_format($discountPercentage, 1) ?>% off
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div class="d-flex justify-content-between w-100">
                            <div>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#inquiryModal<?= $item['id'] ?>" data-bs-dismiss="modal">
                                    <i class="bi bi-envelope"></i> Inquire
                                </button>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                            <div>
                                <?php if(isset($item['has_variants']) && $item['has_variants'] > 0): ?>
                                <div class="text-end mb-2">
                                    <small class="text-muted">Please select color and/or size options above</small>
                                </div>
                                <button type="button" class="btn btn-danger" onclick="addToQuotationFromModal(<?= $item['id'] ?>)">
                                    <i class="bi bi-cart-plus"></i> Add with Selected Options
                                </button>
                                <?php else: ?>
                                <button type="button" class="btn btn-warning" onclick="addToQuotationFromModal(<?= $item['id'] ?>)">
                                    <i class="bi bi-cart-plus"></i> Add to Quote
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
<?php endif; ?>

<script src="assets_site/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="assets_site/vendor/aos/aos.js"></script>
<script src="assets_site/vendor/glightbox/js/glightbox.min.js"></script>
<script src="assets_site/vendor/swiper/swiper-bundle.min.js"></script>
<script src="assets_site/js/main.js"></script>

<!-- Audio Button Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const audioButton = document.getElementById('productAudioButton');
    if (audioButton) {
        audioButton.addEventListener('click', function() {
            // Product list help content
            const productListHelpContent = `Welcome to our Products page. Here's how to use the features on this page:

            You can browse through our product catalog using various options:

            1. Sorting: Use the Sort By dropdown menu to arrange products by name, price (low to high or high to low), or newest arrivals.

            2. Items per page: Choose how many products you want to see on each page - 6, 12, or 24 items.

            3. Pagination: Navigate between pages using the page numbers at the bottom of the product list.

            4. Product Details: Click on any product to view its details, including description, specifications, and pricing.

            5. Add to Quotation: If you're interested in a product, click the "Add to Quotation" button. This adds the product to your quotation cart.

            6. Request Quotation: After adding products, click the quotation icon in the navigation bar to review your selections and submit a quotation request.

            7. Delivery Options: When submitting a quotation, you can choose between pickup or delivery and specify your preferences.

            Our quotation system allows you to request pricing for multiple products at once without any obligation to purchase. We'll respond to your quotation request promptly with pricing and availability information.

            Thank you for shopping with us!`;

            // Check if speech synthesis is supported
            if ('speechSynthesis' in window) {
                // Cancel any ongoing speech
                window.speechSynthesis.cancel();

                // Create a new utterance
                const utterance = new SpeechSynthesisUtterance(productListHelpContent);
                utterance.rate = 0.9;

                // Add visual feedback
                audioButton.innerHTML = 'Speaking...';
                audioButton.disabled = true;

                // When speech ends
                utterance.onend = function() {
                    audioButton.innerHTML = 'Help';
                    audioButton.disabled = false;
                };

                // If there's an error
                utterance.onerror = function() {
                    audioButton.innerHTML = 'Help';
                    audioButton.disabled = false;
                };

                // Speak the utterance
                window.speechSynthesis.speak(utterance);
            } else {
                alert('Text-to-speech functionality is not available in your browser.');
            }
        });
    }
});
</script>

<!-- jQuery is already included in the head section -->
<script>
$(document).ready(function() {
    // Add animation to product cards
    $('.product-card').each(function(index) {
        $(this).css({
            'animation-delay': (index * 0.1) + 's'
        }).addClass('animate__animated animate__fadeInUp');
    });

    // Initialize dropdown menu
    $('.dropdown-toggle').on('click', function(e) {
        e.preventDefault();
        $(this).parent().toggleClass('active');
        $(this).next('.dropdown-menu').toggleClass('show');
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Function to add product to quotation
function addToQuotation(productId) {
    // Debug info
    console.log('Adding to quotation (original function):', productId);

    // Prepare data for AJAX request
    const data = {
        product_id: productId,
        quantity: 1
    };

    // Send AJAX request using jQuery
    $.ajax({
        url: 'add_to_quotation.php',
        type: 'POST',
        data: data,
        dataType: 'json',
        success: function(response) {
            console.log('Response:', response);
            if (response.success) {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
                toast.style.top = '80px';
                toast.style.right = '20px';
                toast.style.zIndex = '9999';
                toast.setAttribute('role', 'alert');
                toast.setAttribute('aria-live', 'assertive');
                toast.setAttribute('aria-atomic', 'true');
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi bi-check-circle me-2"></i> Product added to quotation
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                `;
                document.body.appendChild(toast);

                // Initialize and show the toast
                const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
                bsToast.show();

                // Update quotation count in the navigation
                const quotationBadge = document.querySelector('.bi-cart').nextElementSibling;
                if (quotationBadge) {
                    quotationBadge.textContent = response.item_count;
                } else {
                    // Create badge if it doesn't exist
                    const badge = document.createElement('span');
                    badge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                    badge.textContent = response.item_count;
                    document.querySelector('.bi-cart').parentNode.appendChild(badge);
                }
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('An error occurred. Please try again.');
        }
    });
}
</script>

<!-- Variant Selection and Add to Quote Script -->
<script>
    // Global variables to store selected variants
    let selectedColorId = null;
    let selectedSizeId = null;
    let selectedProductId = null;
    let selectedQuantity = 1;
    let selectedColorPrice = 0;
    let selectedSizePrice = 0;
    let productPrices = {};

    // Initialize variant selection
    document.addEventListener('DOMContentLoaded', function() {
        // Store base prices for all products
        <?php foreach($products as $item): ?>
        productPrices[<?= $item['id'] ?>] = {
            basePrice: <?= ($item['vat_percentage'] == 0) ? $item['sales_price'] : $item['price'] ?>,
            currentPrice: <?= ($item['vat_percentage'] == 0) ? $item['sales_price'] : $item['price'] ?>
        };
        <?php endforeach; ?>

        initVariantSelection();
    });

    function initVariantSelection() {
        // Color selection
        const colorOptions = document.querySelectorAll('.color-option');
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from all color options
                colorOptions.forEach(opt => {
                    if (opt.getAttribute('data-product-id') === this.getAttribute('data-product-id')) {
                        opt.querySelector('div').style.boxShadow = 'none';
                        opt.querySelector('div').style.transform = 'scale(1)';
                    }
                });

                // Add active class to selected color option
                this.querySelector('div').style.boxShadow = '0 0 0 3px rgba(220,53,69,.5)';
                this.querySelector('div').style.transform = 'scale(1.1)';

                // Store selected color ID and price
                selectedColorId = this.getAttribute('data-color-id');
                selectedProductId = this.getAttribute('data-product-id');
                selectedColorPrice = parseFloat(this.getAttribute('data-additional-price') || 0);

                // Update displayed price
                updateDisplayPrice(selectedProductId);
            });
        });

        // Size selection
        const sizeOptions = document.querySelectorAll('.size-option');
        sizeOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from all size options
                sizeOptions.forEach(opt => {
                    if (opt.getAttribute('data-product-id') === this.getAttribute('data-product-id')) {
                        opt.querySelector('.size-box').style.boxShadow = 'none';
                        opt.querySelector('.size-box').style.backgroundColor = '#f8f9fa';
                        opt.querySelector('.size-box').style.transform = 'scale(1)';
                    }
                });

                // Add active class to selected size option
                this.querySelector('.size-box').style.boxShadow = '0 0 0 3px rgba(220,53,69,.5)';
                this.querySelector('.size-box').style.backgroundColor = '#f8d7da';
                this.querySelector('.size-box').style.transform = 'scale(1.05)';

                // Store selected size ID and price
                selectedSizeId = this.getAttribute('data-size-id');
                selectedProductId = this.getAttribute('data-product-id');
                selectedSizePrice = parseFloat(this.getAttribute('data-additional-price') || 0);

                // Update displayed price
                updateDisplayPrice(selectedProductId);
            });
        });
    }

    // Function to update the displayed price based on selected variants
    function updateDisplayPrice(productId) {
        if (!productPrices[productId]) return;

        // Calculate total price
        const basePrice = productPrices[productId].basePrice;
        const totalPrice = basePrice + selectedColorPrice + selectedSizePrice;

        // Update the stored current price
        productPrices[productId].currentPrice = totalPrice;

        // Format the price
        const formattedPrice = 'R ' + totalPrice.toFixed(2);

        // Update the price display
        const priceElement = document.getElementById(`price-${productId}`);
        if (priceElement) {
            priceElement.textContent = formattedPrice;
        }

        // Show variant details
        let variantDetails = '';

        if (selectedColorPrice > 0 || selectedSizePrice > 0) {
            variantDetails += '<div class="alert alert-info mb-3">';
            variantDetails += '<p class="mb-1"><strong>Selected Options:</strong></p>';

            if (selectedColorPrice > 0) {
                variantDetails += '<p class="mb-1">Color: +R ' + selectedColorPrice.toFixed(2) + '</p>';
            }

            if (selectedSizePrice > 0) {
                variantDetails += '<p class="mb-1">Size: +R ' + selectedSizePrice.toFixed(2) + '</p>';
            }

            variantDetails += '<p class="mb-0"><strong>Total: R ' + totalPrice.toFixed(2) + '</strong></p>';
            variantDetails += '</div>';
        }

        // Update variant details display
        const variantDetailsElement = document.getElementById(`variant-details-${productId}`);
        if (variantDetailsElement) {
            variantDetailsElement.innerHTML = variantDetails;
        }
    }

    // Add to quotation from modal
    function addToQuotationFromModal(productId) {
        // Set the product ID
        selectedProductId = productId;

        // Get quantity
        selectedQuantity = 1; // Default quantity

        // Prepare data for AJAX request
        const data = {
            product_id: selectedProductId,
            quantity: selectedQuantity
        };

        // Add variant information if selected
        if (selectedColorId) {
            data.color_id = selectedColorId;
        }

        if (selectedSizeId) {
            data.size_id = selectedSizeId;
        }

        // Add additional price information separately for color and size
        if (selectedColorPrice > 0) {
            data.color_additional_price = selectedColorPrice;
        }

        if (selectedSizePrice > 0) {
            data.size_additional_price = selectedSizePrice;
        }

        // Also send the total additional price for backward compatibility
        const totalAdditionalPrice = selectedColorPrice + selectedSizePrice;
        if (totalAdditionalPrice > 0) {
            data.additional_price = totalAdditionalPrice;
        }

        // Debug info
        console.log('Adding to quotation from modal:', productId);
        console.log('Selected color:', selectedColorId);
        console.log('Selected size:', selectedSizeId);
        console.log('Data being sent:', data);

        // Send AJAX request using jQuery
        $.ajax({
            url: 'add_to_quotation.php',
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                console.log('Response:', response);
                if (response.success) {
                    // Close the modal
                    $('.modal.show').modal('hide');

                    // Show success message
                    const toast = document.createElement('div');
                    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
                    toast.style.top = '80px';
                    toast.style.right = '20px';
                    toast.style.zIndex = '9999';
                    toast.setAttribute('role', 'alert');
                    toast.setAttribute('aria-live', 'assertive');
                    toast.setAttribute('aria-atomic', 'true');
                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="bi bi-check-circle me-2"></i> Product added to quotation
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    `;
                    document.body.appendChild(toast);

                    // Initialize and show the toast
                    const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
                    bsToast.show();

                    // Update quotation count in the navigation
                    const quotationBadge = document.querySelector('.bi-cart').nextElementSibling;
                    if (quotationBadge) {
                        quotationBadge.textContent = response.item_count;
                    } else {
                        // Create badge if it doesn't exist
                        const badge = document.createElement('span');
                        badge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                        badge.textContent = response.item_count;
                        document.querySelector('.bi-cart').parentNode.appendChild(badge);
                    }

                    // Reset selection
                    selectedColorId = null;
                    selectedSizeId = null;
                    selectedProductId = null;
                    selectedQuantity = 1;

                    // Reset visual selection
                    document.querySelectorAll('.color-option div').forEach(el => {
                        el.style.boxShadow = 'none';
                        el.style.transform = 'scale(1)';
                    });
                    document.querySelectorAll('.size-option .size-box').forEach(el => {
                        el.style.boxShadow = 'none';
                        el.style.backgroundColor = '#f8f9fa';
                        el.style.transform = 'scale(1)';
                    });
                } else {
                    alert(response.message || 'Error adding product to quotation');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response Text:', xhr.responseText);
                try {
                    const errorData = JSON.parse(xhr.responseText);
                    alert('Error: ' + (errorData.message || 'Unknown error'));
                } catch (e) {
                    alert('An error occurred. Please check the console for details.');
                }
            }
        });
    }

    // Color selection and image switching functionality
    function selectColor(productId, colorId, colorName, additionalPrice) {
        // Remove active class from all color options for this product
        const productModal = document.querySelector(`#productModal${productId}`);
        if (productModal) {
            const colorOptions = productModal.querySelectorAll('.color-option');
            colorOptions.forEach(option => {
                const swatch = option.querySelector('.color-swatch');
                swatch.style.borderColor = '#ddd';
                swatch.style.borderWidth = '2px';
                option.classList.remove('selected');
            });

            // Add active class to selected color
            const selectedOption = productModal.querySelector(`[data-color-id="${colorId}"]`);
            if (selectedOption) {
                const swatch = selectedOption.querySelector('.color-swatch');
                swatch.style.borderColor = '#007bff';
                swatch.style.borderWidth = '3px';
                selectedOption.classList.add('selected');
            }

            // Update variant details
            updateVariantDetails(productId, colorId, null, colorName, null, additionalPrice);

            // Try to load variant image
            loadVariantImage(productId, colorId);
        }
    }

    function selectSize(productId, sizeId, sizeName, additionalPrice) {
        // Remove active class from all size options for this product
        const productModal = document.querySelector(`#productModal${productId}`);
        if (productModal) {
            const sizeOptions = productModal.querySelectorAll('.size-option');
            sizeOptions.forEach(option => {
                const sizeBox = option.querySelector('.size-box');
                sizeBox.style.borderColor = '#ddd';
                sizeBox.style.backgroundColor = '#f8f9fa';
                option.classList.remove('selected');
            });

            // Add active class to selected size
            const selectedOption = productModal.querySelector(`[data-size-id="${sizeId}"]`);
            if (selectedOption) {
                const sizeBox = selectedOption.querySelector('.size-box');
                sizeBox.style.borderColor = '#007bff';
                sizeBox.style.backgroundColor = '#e3f2fd';
                selectedOption.classList.add('selected');
            }

            // Update variant details
            const selectedColor = productModal.querySelector('.color-option.selected');
            const colorId = selectedColor ? selectedColor.dataset.colorId : null;
            const colorName = selectedColor ? selectedColor.querySelector('small').textContent.split('(')[0].trim() : null;
            const colorPrice = selectedColor ? parseFloat(selectedColor.dataset.additionalPrice) || 0 : 0;

            updateVariantDetails(productId, colorId, sizeId, colorName, sizeName, colorPrice + additionalPrice);
        }
    }

    function updateVariantDetails(productId, colorId, sizeId, colorName, sizeName, totalAdditionalPrice) {
        const detailsContainer = document.querySelector(`#variant-details-${productId}`);
        if (detailsContainer) {
            let details = '<div class="variant-selection-summary">';
            details += '<strong>Selected Options:</strong><br>';

            if (colorName) {
                details += `<span class="badge bg-primary me-2">Color: ${colorName}</span>`;
            }

            if (sizeName) {
                details += `<span class="badge bg-success me-2">Size: ${sizeName}</span>`;
            }

            if (totalAdditionalPrice > 0) {
                details += `<span class="badge bg-warning">Additional: +R ${totalAdditionalPrice.toFixed(2)}</span>`;
            }

            details += '</div>';
            detailsContainer.innerHTML = details;

            // Store selected variant data for quotation
            const modal = document.querySelector(`#productModal${productId}`);
            if (modal) {
                modal.dataset.selectedColorId = colorId || '';
                modal.dataset.selectedSizeId = sizeId || '';
                modal.dataset.additionalPrice = totalAdditionalPrice || 0;
            }
        }
    }

    function loadVariantImage(productId, colorId) {
        // Try to load variant-specific image
        fetch(`get_variant_image.php?product_id=${productId}&color_id=${colorId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.image) {
                    const modalImage = document.querySelector(`#productModal${productId} .modal-image-container img`);
                    if (modalImage) {
                        modalImage.src = `uploads/products/${data.image}`;
                    }
                }
            })
            .catch(error => {
                console.log('No variant image found, keeping original image');
            });
    }

    // Add click handlers for size options
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.size-option').forEach(option => {
            option.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const sizeId = this.dataset.sizeId;
                const sizeName = this.querySelector('.size-box span').textContent;
                const additionalPrice = parseFloat(this.dataset.additionalPrice) || 0;

                selectSize(productId, sizeId, sizeName, additionalPrice);
            });
        });
    });
</script>

</body>
</html>
