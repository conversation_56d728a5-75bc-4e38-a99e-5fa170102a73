<?php include('includes/header.php'); ?>

<?php
function insertServiceCategory($data) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "INSERT INTO services_categories (name, description, status) VALUES (?, ?, ?)");
    mysqli_stmt_bind_param($stmt, "ssi", 
        $data['name'],
        $data['description'],
        $data['status']
    );
    
    return mysqli_stmt_execute($stmt);
}

// Form submission handling
if(isset($_POST['saveServiceCategory'])) {
    $data = [
        'name' => validate($_POST['name']),
        'description' => validate($_POST['description']),
        'status' => isset($_POST['status']) ? 1 : 0
    ];
    
    if(insertServiceCategory($data)) {
        redirect('services-categories.php', 'Category Created Successfully!');
    } else {
        redirect('services-categories-create.php', 'Something Went Wrong!');
    }
}
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Add Service Category
                <a href="services_categories.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">

            <?php alertMessage(); ?>

            <form action="code.php" method="POST">

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="">Name *</label>
                        <input type="text" name="name" required class="form-control" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <label for="">Description</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="col-md-6">
                        <label>Status (UnChecked=Visible, Checked=Hidden)</label>
                        <br/>
                        <input type="checkbox" name="status" style="width:30px;height:30px";>
                    </div>
                    <div class="col-md-6 mb-3 text-end">
                        <br/>
                        <button type="submit" name="saveServiceCategory" class="btn btn-primary">Save</button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>
