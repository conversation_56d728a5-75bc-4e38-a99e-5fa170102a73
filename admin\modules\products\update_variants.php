<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';
require '../../config/dbcon.php';

// Create a log file
$log_file = __DIR__ . '/update_variants_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - <PERSON>ript started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Function to generate SKU for variants
function generateVariantSKU($product_id, $color_id = null, $size_id = null) {
    global $conn;

    // Get product name for SKU base
    $product_query = mysqli_query($conn, "SELECT name FROM products WHERE id = $product_id");
    $product = mysqli_fetch_assoc($product_query);
    $product_name = $product ? $product['name'] : 'PROD';

    // Clean product name for SKU (remove spaces, special chars, limit length)
    $sku_base = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $product_name));
    $sku_base = substr($sku_base, 0, 8); // Limit to 8 characters

    // Add color code if available
    $color_code = '';
    if ($color_id && $color_id !== 'NULL') {
        $color_query = mysqli_query($conn, "SELECT name FROM product_colors WHERE id = $color_id");
        $color = mysqli_fetch_assoc($color_query);
        if ($color) {
            $color_code = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $color['name']), 0, 3));
        }
    }

    // Add size code if available
    $size_code = '';
    if ($size_id && $size_id !== 'NULL') {
        $size_query = mysqli_query($conn, "SELECT name FROM product_sizes WHERE id = $size_id");
        $size = mysqli_fetch_assoc($size_query);
        if ($size) {
            $size_code = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $size['name']), 0, 3));
        }
    }

    // Combine to create SKU
    $sku = $sku_base . '-' . $product_id;
    if ($color_code) $sku .= '-' . $color_code;
    if ($size_code) $sku .= '-' . $size_code;

    // Add timestamp to ensure uniqueness
    $sku .= '-' . time();

    return $sku;
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    log_message("Form submitted", $_POST);

    try {
        // Get product ID
        $product_id = validate($_POST['product_id']);

        if(!$product_id) {
            log_message("No product ID provided");
            redirect('products.php', 'Invalid product ID');
            exit;
        }

        // Update product variants if provided
        if(isset($_POST['variant_ids'])) {
            $variant_ids = $_POST['variant_ids'];
            $variant_color_ids = isset($_POST['variant_color_ids']) ? $_POST['variant_color_ids'] : [];
            $variant_size_ids = isset($_POST['variant_size_ids']) ? $_POST['variant_size_ids'] : [];
            $variant_additional_prices = isset($_POST['variant_additional_prices']) ? $_POST['variant_additional_prices'] : [];
            $variant_quantities = isset($_POST['variant_quantities']) ? $_POST['variant_quantities'] : [];
            $variant_status = isset($_POST['variant_status']) ? $_POST['variant_status'] : [];

            log_message("Updating variants", [
                'variant_ids' => $variant_ids,
                'variant_color_ids' => $variant_color_ids,
                'variant_size_ids' => $variant_size_ids,
                'variant_additional_prices' => $variant_additional_prices,
                'variant_quantities' => $variant_quantities,
                'variant_status' => $variant_status
            ]);

            // Update each variant
            for($i = 0; $i < count($variant_ids); $i++) {
                $variant_id = $variant_ids[$i];
                $color_id = isset($variant_color_ids[$i]) ? validate($variant_color_ids[$i]) : 'NULL';
                $size_id = isset($variant_size_ids[$i]) ? validate($variant_size_ids[$i]) : 'NULL';
                $additional_price = isset($variant_additional_prices[$i]) ? validate($variant_additional_prices[$i]) : 0;
                $quantity = isset($variant_quantities[$i]) ? validate($variant_quantities[$i]) : 0;
                $status = in_array($variant_id, $variant_status) ? 0 : 1; // 0 = visible, 1 = hidden

                // Check if this is a new variant (ID starts with 'new-')
                if(strpos($variant_id, 'new-') === 0) {
                    // Generate SKU for new variant
                    $color_id_for_sku = ($color_id === 'NULL') ? null : $color_id;
                    $size_id_for_sku = ($size_id === 'NULL') ? null : $size_id;
                    $sku = generateVariantSKU($product_id, $color_id_for_sku, $size_id_for_sku);

                    // Insert new variant
                    $sql = "INSERT INTO product_variants (product_id, sku, color_id, size_id, additional_price, quantity, status)
                           VALUES ('$product_id', '$sku', $color_id, $size_id, '$additional_price', '$quantity', '$status')";

                    $result = mysqli_query($conn, $sql);

                    log_message("Inserted new variant", [
                        'sku' => $sku,
                        'color_id' => $color_id,
                        'size_id' => $size_id,
                        'additional_price' => $additional_price,
                        'quantity' => $quantity,
                        'status' => $status,
                        'success' => $result ? 'true' : 'false',
                        'error' => mysqli_error($conn)
                    ]);
                } else {
                    // Update existing variant
                    $variant_id = validate($variant_id); // Ensure it's a valid ID

                    // Check if the variant has a SKU, if not generate one
                    $check_sku_sql = "SELECT sku FROM product_variants WHERE id = '$variant_id'";
                    $check_result = mysqli_query($conn, $check_sku_sql);
                    $existing_variant = mysqli_fetch_assoc($check_result);

                    $sku_update = '';
                    if(empty($existing_variant['sku'])) {
                        // Generate SKU for existing variant that doesn't have one
                        $color_id_for_sku = ($color_id === 'NULL') ? null : $color_id;
                        $size_id_for_sku = ($size_id === 'NULL') ? null : $size_id;
                        $sku = generateVariantSKU($product_id, $color_id_for_sku, $size_id_for_sku);
                        $sku_update = ", sku = '$sku'";
                    }

                    $sql = "UPDATE product_variants
                            SET color_id = $color_id, size_id = $size_id, additional_price = '$additional_price',
                                quantity = '$quantity', status = '$status'$sku_update
                            WHERE id = '$variant_id' AND product_id = '$product_id'";

                    $result = mysqli_query($conn, $sql);

                    log_message("Updated variant", [
                        'variant_id' => $variant_id,
                        'sku_updated' => !empty($sku_update),
                        'color_id' => $color_id,
                        'size_id' => $size_id,
                        'additional_price' => $additional_price,
                        'quantity' => $quantity,
                        'status' => $status,
                        'success' => $result ? 'true' : 'false',
                        'error' => mysqli_error($conn)
                    ]);
                }
            }

            // Update the product's total quantity to match the sum of all variant quantities
            $sql = "SELECT SUM(quantity) as total_quantity FROM product_variants WHERE product_id = '$product_id'";
            $result = mysqli_query($conn, $sql);

            if($result && mysqli_num_rows($result) > 0) {
                $row = mysqli_fetch_assoc($result);
                $total_quantity = $row['total_quantity'];

                $sql = "UPDATE products SET quantity = '$total_quantity' WHERE id = '$product_id'";
                $result = mysqli_query($conn, $sql);

                log_message("Updated product quantity", [
                    'product_id' => $product_id,
                    'total_quantity' => $total_quantity,
                    'success' => $result ? 'true' : 'false',
                    'error' => mysqli_error($conn)
                ]);
            }

            // Success - check where to redirect
            log_message("Variants updated successfully");

            // Check if we should close the window after update
            if(isset($_POST['close_window']) && $_POST['close_window'] == '1') {
                // Output JavaScript to close the window
                echo "<script>window.opener.location.reload(); window.close();</script>";
                exit;
            }

            // Check if we came from variants-only.php, popup, or integrated page
            if(isset($_POST['source_page'])) {
                if($_POST['source_page'] === 'variants_only') {
                    $redirect_page = "variants-only.php?id=$product_id";
                } elseif($_POST['source_page'] === 'popup') {
                    $redirect_page = "variants-popup.php?id=$product_id";
                } elseif($_POST['source_page'] === 'integrated') {
                    $redirect_page = "products-edit.php?id=$product_id#variantsAccordion";
                } else {
                    $redirect_page = "products-edit.php?id=$product_id";
                }
            } else {
                $redirect_page = "products-edit.php?id=$product_id";
            }

            redirect($redirect_page, 'Product variants updated successfully!');
        } else {
            log_message("No variant data provided");
            redirect("products-edit.php?id=$product_id", 'No variant data provided.');
        }
    } catch (Exception $e) {
        log_message("Exception", [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        redirect('products.php', 'Error: ' . $e->getMessage());
    }
} else {
    log_message("Invalid request method");
    redirect('products.php', 'Invalid request method');
}
?>
