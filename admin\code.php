<?php
require __DIR__ . '/config/function.php';

// Admin Management
if(isset($_POST['saveAdmin']))
{
    $name = validate($_POST['name']);
    $email = validate($_POST['email']);
    $password = validate($_POST['password']);
    $phone = validate($_POST['phone']);
    $user_type = validate($_POST['user_type']); // Fixed: was duplicating phone
    $is_ban = isset($_POST['is_ban']) == true ? 1:0;

    if($name != '' && $email != '' && $password != ''){
        // Use prepared statement to prevent SQL injection
        $stmt = mysqli_prepare($conn, "SELECT * FROM admins WHERE email=?");
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        $emailCheck = mysqli_stmt_get_result($stmt);

        if(mysqli_num_rows($emailCheck) > 0){
            redirect('admins-create.php','Email Already used by another user.');
        }

        $bcrypt_password = password_hash($password, PASSWORD_BCRYPT);
        $created_at = date('Y-m-d H:i:s');

        $data = [
            'name' => $name,
            'email' => $email,
            'password' => $bcrypt_password, // Fixed: was storing plain password
            'phone' => $phone,
            'is_ban' => $is_ban,
            'user_type' => $user_type,
            'created_at' => $created_at
        ];
        $result = insert('admins',$data);
        
        if($result){
            redirect('admins.php','Admin Created Successfully!');
        }else{
            redirect('admins-create.php','Something Went Wrong!');
        }
    }else{
        redirect('admins-create.php','Please fill required fields.');
    }
}

if(isset($_POST['updateAdmin']))
{
    $adminId = validate($_POST['adminId']);
    $adminData = getById('admins',$adminId);
    
    if($adminData['status'] != 200){
        redirect('admins-edit.php?id='.$adminId,'Please fill required fields.');
    }

    $name = validate($_POST['name']);
    $email = validate($_POST['email']);
    $password = validate($_POST['password']);
    $phone = validate($_POST['phone']);
    $is_ban = isset($_POST['is_ban']) == true ? 1:0;

    // Use prepared statement
    $stmt = mysqli_prepare($conn, "SELECT * FROM admins WHERE email=? AND id!=?");
    mysqli_stmt_bind_param($stmt, "si", $email, $adminId);
    mysqli_stmt_execute($stmt);
    $checkResult = mysqli_stmt_get_result($stmt);

    if(mysqli_num_rows($checkResult) > 0){
        redirect('admins-edit.php?id='.$adminId,'Email Already used by another user');
    }

    if($password != ''){
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT);
    }else{
        $hashedPassword = $adminData['data']['password'];
    }

    if($name != '' && $email != '')
    {
        $data = [
            'name' => $name,
            'email' => $email,
            'password' => $hashedPassword,
            'phone' => $phone,
            'is_ban' => $is_ban
        ];
        $result = update('admins', $adminId, $data);
        
        if($result){
            redirect('admins-edit.php?id='.$adminId,'Admin Updated Successfully!');
        }else{
            redirect('admins-edit.php?id='.$adminId,'Something Went Wrong!');
        }
    }
    else
    {
        redirect('admins-create.php','Please fill required fields.');
    }
}

// Category Management
if(isset($_POST['saveCategory'])) {
    $name = validate($_POST['name']);
    $description = validate($_POST['description']);
    $status = isset($_POST['status']) ? 1 : 0;

    $stmt = mysqli_prepare($conn, "INSERT INTO categories (name, description, status) VALUES (?, ?, ?)");
    mysqli_stmt_bind_param($stmt, "ssi", $name, $description, $status);
    
    if(mysqli_stmt_execute($stmt)) {
        redirect('categories.php', 'Category Created Successfully!');
    } else {
        redirect('categories-create.php', 'Something Went Wrong!');
    }
}

if(isset($_POST['updateCategory'])) {
    $categoryId = validate($_POST['categoryId']);
    $name = validate($_POST['name']);
    $description = validate($_POST['description']);
    $status = isset($_POST['status']) ? 1 : 0;

    $stmt = mysqli_prepare($conn, "UPDATE categories SET name = ?, description = ?, status = ? WHERE id = ?");
    mysqli_stmt_bind_param($stmt, "ssii", $name, $description, $status, $categoryId);
    
    if(mysqli_stmt_execute($stmt)) {
        redirect('categories.php', 'Category Updated Successfully!');
    } else {
        redirect('categories-edit.php?id='.$categoryId, 'Something Went Wrong!');
    }
}

// Product Management
if(isset($_POST['saveProduct']))
{
    $category_id = validate($_POST['category_id']);
    $name = validate($_POST['name']);
    $description = validate($_POST['description']);
    $cost_price = validate($_POST['cost_price']);
    $sales_price = validate($_POST['sales_price']);
    $vat_percentage = validate($_POST['vat_percentage']);
    $price = validate($_POST['price']) ?: 0;
    $quantity = validate($_POST['quantity']);
    $barcode = validate($_POST['barcode']);
    $status = isset($_POST['status']) == true ? 1 : 0;
    $featured = isset($_POST['featured']) == true ? 1 : 0;
    
    // Handle image upload
    $finalImage = "";
    if(isset($_FILES['image']['name'])) {
        $upload_path = "uploads" . DIRECTORY_SEPARATOR . "products";  // For file operations
        $image = $_FILES['image']['name'];
        
        if($image != "") {
            $finalImage = time() . '_' . $image;  // Store only filename in database
            $upload_to = ".." . DIRECTORY_SEPARATOR . $upload_path . DIRECTORY_SEPARATOR . $finalImage;
            
            // Create directory if it doesn't exist
            if (!file_exists(".." . DIRECTORY_SEPARATOR . $upload_path)) {
                mkdir(".." . DIRECTORY_SEPARATOR . $upload_path, 0777, true);
            }
            
            move_uploaded_file($_FILES['image']['tmp_name'], $upload_to);
        }
    }

    $data = [
        'category_id' => $category_id,
        'name' => $name,
        'description' => $description,
        'cost_price' => $cost_price,
        'sales_price' => $sales_price,
        'vat_percentage' => $vat_percentage,
        'price' => $price,
        'quantity' => $quantity,
        'barcode' => $barcode,
        'image' => $finalImage,
        'status' => $status,
        'featured' => $featured
    ];

    $result = insert('products', $data);

    if($result) {
        redirect('modules/products/products.php', 'Product Created Successfully!');
    } else {
        redirect('modules/products/products-create.php', 'Something Went Wrong!');
    }
}

// Product Management - Update
if(isset($_POST['updateProduct']))
{
    $product_id = validate($_POST['product_id']);
    $category_id = validate($_POST['category_id']);
    $name = validate($_POST['name']);
    $description = validate($_POST['description']);
    $cost_price = validate($_POST['cost_price']);
    $sales_price = validate($_POST['sales_price']);
    $vat_percentage = validate($_POST['vat_percentage']);
    $price = validate($_POST['price']) ?: 0;
    $quantity = validate($_POST['quantity']);
    $barcode = validate($_POST['barcode']);
    $status = isset($_POST['status']) == true ? 1 : 0;
    
    // Handle image upload
    $oldImage = getById('products', $product_id);
    $updateImage = $oldImage['data']['image'];
    
    if(isset($_FILES['image']['name']) && $_FILES['image']['name'] != '') {
        $upload_path = "uploads/products/";
        $image = $_FILES['image']['name'];
        
        $updateImage = time() . '_' . $image;
        $upload_to = "../" . $upload_path . $updateImage;
        
        // Create directory if it doesn't exist
        if (!file_exists("../" . $upload_path)) {
            mkdir("../" . $upload_path, 0777, true);
        }
        
        // Delete old image if exists
        if($oldImage['data']['image'] != '' && file_exists("../" . $upload_path . $oldImage['data']['image'])) {
            unlink("../" . $upload_path . $oldImage['data']['image']);
        }
        
        // Upload new image
        move_uploaded_file($_FILES['image']['tmp_name'], $upload_to);
    }

    $data = [
        'category_id' => $category_id,
        'name' => $name,
        'description' => $description,
        'cost_price' => $cost_price,
        'sales_price' => $sales_price,
        'vat_percentage' => $vat_percentage,
        'price' => $price,
        'quantity' => $quantity,
        'barcode' => $barcode,
        'image' => $updateImage,
        'status' => $status
    ];

    $result = update('products', $product_id, $data);

    if($result) {
        redirect('modules/products/products.php', 'Product Updated Successfully!');
    } else {
        redirect('modules/products/products-edit.php?id='.$product_id, 'Something Went Wrong!');
    }
}

// Customer Management
if(isset($_POST['saveCustomer'])) {
    $name = validate($_POST['name']);
    $email = validate($_POST['email']);
    $phone = validate($_POST['phone']);
    $status = isset($_POST['status']) ? 1 : 0;

    if($name != '') {
        // Check if email exists
        $stmt = mysqli_prepare($conn, "SELECT id FROM customers WHERE email = ?");
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if(mysqli_num_rows($result) > 0) {
            redirect('customers.php', 'Email Already used by another user');
            exit();
        }

        // Insert new customer
        $stmt = mysqli_prepare($conn, "INSERT INTO customers (name, email, phone, status) VALUES (?, ?, ?, ?)");
        mysqli_stmt_bind_param($stmt, "sssi", $name, $email, $phone, $status);
        
        if(mysqli_stmt_execute($stmt)) {
            redirect('customers.php', 'Customer Created Successfully');
        } else {
            redirect('customers.php', 'Something Went Wrong');
        }
    } else {
        redirect('customers.php', 'Please fill required fields');
    }
}

// Update Customer
if(isset($_POST['updateCustomer'])) {
    $customerId = validate($_POST['customerId']);
    $name = validate($_POST['name']);
    $email = validate($_POST['email']);
    $phone = validate($_POST['phone']);
    $status = isset($_POST['status']) ? 1 : 0;

    if($name != '') {
        // Check if email exists for other customers
        $stmt = mysqli_prepare($conn, "SELECT id FROM customers WHERE email = ? AND id != ?");
        mysqli_stmt_bind_param($stmt, "si", $email, $customerId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if(mysqli_num_rows($result) > 0) {
            redirect('customers-edit.php?id='.$customerId, 'Email Already used by another user');
            exit();
        }

        // Update customer
        $stmt = mysqli_prepare($conn, "UPDATE customers SET name = ?, email = ?, phone = ?, status = ? WHERE id = ?");
        mysqli_stmt_bind_param($stmt, "sssii", $name, $email, $phone, $status, $customerId);
        
        if(mysqli_stmt_execute($stmt)) {
            redirect('customers-edit.php?id='.$customerId, 'Customer Updated Successfully');
        } else {
            redirect('customers-edit.php?id='.$customerId, 'Something Went Wrong');
        }
    } else {
        redirect('customers-edit.php?id='.$customerId, 'Please fill required fields');
    }
}

// Featured Products Management
if(isset($_POST['saveUpdateFeatured']))
{
    $featured = isset($_POST['featured']) ? $_POST['featured'] : array();
    
    // Begin transaction
    mysqli_begin_transaction($conn);
    
    try {
        // First, set all products as non-featured
        $stmt1 = mysqli_prepare($conn, "UPDATE products SET featured = 0");
        mysqli_stmt_execute($stmt1);
        
        if (!empty($featured)) {
            // Create placeholders for IN clause
            $placeholders = str_repeat('?,', count($featured) - 1) . '?';
            
            // Update selected products as featured
            $stmt2 = mysqli_prepare($conn, "UPDATE products SET featured = 1 WHERE id IN ($placeholders)");
            
            // Create array of types
            $types = str_repeat('i', count($featured));
            
            // Bind and execute statement
            mysqli_stmt_bind_param($stmt2, $types, ...$featured);
            mysqli_stmt_execute($stmt2);
        }
        
        mysqli_commit($conn);
        redirect('modules/products/products.php','Featured Products Updated Successfully!');
    } catch (Exception $e) {
        mysqli_rollback($conn);
        redirect('modules/products/products.php','Something Went Wrong!');
    }
}

// Featured Services Management
if(isset($_POST['saveServicesUpdateFeatured']))
{
    if(isset($_POST['featured']) && is_array($_POST['featured']))
    {
        $featured = $_POST['featured'];
    }
    else
    {
        $featured = array($_POST['featured']);
    }

    // Use prepared statements for both updates
    $stmt1 = mysqli_prepare($conn, "UPDATE services SET featured = 1 WHERE id IN (" . str_repeat('?,', count($featured)-1) . '?)');
    $stmt2 = mysqli_prepare($conn, "UPDATE services SET featured = 0 WHERE id NOT IN (" . str_repeat('?,', count($featured)-1) . '?)');
    
    $types = str_repeat('i', count($featured));
    
    mysqli_stmt_bind_param($stmt1, $types, ...$featured);
    mysqli_stmt_bind_param($stmt2, $types, ...$featured);
    
    $result = mysqli_stmt_execute($stmt1);
    $result2 = mysqli_stmt_execute($stmt2);

    if($result && $result2){
        redirect('services.php','Featured Status Updated Successfully!');
    }else{
        redirect('services.php','Something Went Wrong!');
    }
}

// Request Call Back Customer Save
if (isset($_POST['saveRequestCallBackSaveCustomer'])) {
    $name = validate($_POST['name']);
    $email = validate($_POST['email']);
    $phone = validate($_POST['phone']);
  
    if($name != '')
    {
        // Use prepared statement
        $stmt = mysqli_prepare($conn, "SELECT * FROM customers WHERE email=?");
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        $emailCheck = mysqli_stmt_get_result($stmt);

        if(mysqli_num_rows($emailCheck) > 0){
            redirect('customers.php','Email Already used by another user');
        }

        $data = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone
        ];

        $result = insert('customers',$data);

        if($result){
            redirect('customers.php','Customer Created Successfully');
        }else{
            redirect('customers.php','Something Went Wrong');
        }
    }
    else
    {
        redirect('customers.php','Please fill required fields');
    }
}

// Services Update
if(isset($_POST['updateServices']))
{
    $name = validate($_POST['name']);    
    $description = validate($_POST['description']);
    $contact_number = validate($_POST['contact_number']);
    $contact_email = validate($_POST['contact_email']);
    $contact_description = validate($_POST['contact_description']);

    $data = [
        'contact_name' => $contact_name,
        'contact_number' => $contact_number,
        'contact_email' => $contact_email,
        'contact_description' => $contact_description
    ];
    
    $result = update('services', $contactsId, $data);
    
    if($result){
        redirect('services.php?id='.$name,'Contacts Updated Successfully!');
    }else{
        redirect('services.php?id='.$name,'Something Went Wrong!');
    }
}

// Save Contact
if(isset($_POST['saveContact'])) {
    $contact_name = validate($_POST['contact_name']);
    $contact_number = validate($_POST['contact_number']);
    $contact_email = validate($_POST['contact_email']);
    $contact_description = validate($_POST['contact_description']);

    $stmt = mysqli_prepare($conn, "INSERT INTO contacts (contact_name, contact_number, contact_email, contact_description) 
            VALUES (?, ?, ?, ?)");
    mysqli_stmt_bind_param($stmt, "ssss", $contact_name, $contact_number, $contact_email, $contact_description);
    
    if(mysqli_stmt_execute($stmt)) {
        redirect('contacts.php', 'Contact Created Successfully');
    } else {
        redirect('contacts-create.php', 'Something Went Wrong');
    }
}

// Update Contact
if(isset($_POST['updateContact'])) {
    $contact_id = validate($_POST['contactId']);
    $contact_name = validate($_POST['contact_name']);
    $contact_number = validate($_POST['contact_number']);
    $contact_email = validate($_POST['contact_email']);
    $contact_description = validate($_POST['contact_description']);

    $stmt = mysqli_prepare($conn, "UPDATE contacts 
            SET contact_name = ?, contact_number = ?, contact_email = ?, contact_description = ? 
            WHERE id = ?");
    mysqli_stmt_bind_param($stmt, "ssssi", $contact_name, $contact_number, $contact_email, $contact_description, $contact_id);
    
    if(mysqli_stmt_execute($stmt)) {
        redirect('contacts.php', 'Contact Updated Successfully');
    } else {
        redirect('contacts-edit.php?id='.$contact_id, 'Something Went Wrong');
    }
}
?>
        $contact_email = validate($_POST['contact_email']);
        $contact_description = validate($_POST['contact_description']);
    
        $data = [
            'contact_name' => $contact_name,
            'contact_number' => $contact_number,
            'contact_email' => $contact_email,
            'contact_description' => $contact_description
       
        ];
        $result = update('services', $contactsId, $data);
        
        if($result){
            redirect('services.php?id='.$name,'Contacts Updated Successfully!');
        }else{
            redirect('services.php?id='.$name,'Something Went Wrong!');
        }
    }


