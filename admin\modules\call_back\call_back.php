<?php
// Start the session at the very beginning
session_start();

// Process delete action before any output
if(isset($_POST['delete_btn'])) {
    require_once('../../config/dbcon.php');

    $id = mysqli_real_escape_string($conn, $_POST['delete_id']);
    $query = "DELETE FROM request_call_back WHERE id='$id'";
    $query_run = mysqli_query($conn, $query);

    if($query_run) {
        $_SESSION['success'] = "Call Back Request deleted successfully";
    } else {
        $_SESSION['error'] = "Failed to delete Call Back Request";
    }

    // Redirect before any output
    header('Location: call_back.php');
    exit();
}

// Process toggle read status action
if(isset($_POST['toggle_read_btn'])) {
    require_once('../../config/dbcon.php');

    $id = mysqli_real_escape_string($conn, $_POST['item_id']);
    $current_status = mysqli_real_escape_string($conn, $_POST['current_status']);
    $new_status = $current_status == 1 ? 0 : 1;

    $query = "UPDATE request_call_back SET read_status='$new_status' WHERE id='$id'";
    $query_run = mysqli_query($conn, $query);

    if($query_run) {
        $status_text = $new_status == 1 ? "marked as read" : "marked as unread";
        $_SESSION['success'] = "Call Back Request $status_text successfully";
    } else {
        $_SESSION['error'] = "Failed to update read status";
    }

    // Redirect before any output
    header('Location: call_back.php');
    exit();
}

// Include header after processing actions
include('../../includes/header.php');
checkUserRoles(['admin', 'user']);

// Fetch all call back requests
$query = "SELECT * FROM request_call_back ORDER BY created_at DESC";
$query_run = mysqli_query($conn, $query);
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Call Back Requests
                <a href="../../index.php" class="btn btn-dark float-end me-2">Dashboard <i class="fas fa-tachometer-alt"></i></a>
            </h4>
        </div>
        <div class="card-body">
            <?php include('../../includes/message.php'); ?>
            <div class="table-responsive">
                <table id="datatablesSimple" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Phone Number</th>
                            <th>Email</th>
                            <th>Message</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if(mysqli_num_rows($query_run) > 0) {
                            while($row = mysqli_fetch_assoc($query_run)) {
                        ?>
                        <tr class="<?= isset($row['read_status']) && $row['read_status'] == 0 ? 'table-warning' : ''; ?>">
                            <td><?= $row['id']; ?></td>
                            <td><?= htmlspecialchars($row['name']); ?></td>
                            <td><?= htmlspecialchars($row['number']); ?></td>
                            <td><?= htmlspecialchars($row['email']); ?></td>
                            <td><?= htmlspecialchars($row['message']); ?></td>
                            <td><?= date('d M Y H:i', strtotime($row['created_at'])); ?></td>
                            <td>
                                <?php if(!isset($row['read_status']) || $row['read_status'] == 0): ?>
                                    <span class="badge bg-warning text-dark">Unread</span>
                                <?php else: ?>
                                    <span class="badge bg-success">Read</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="d-flex gap-2">
                                    <form action="call_back.php" method="POST" class="me-1">
                                        <input type="hidden" name="item_id" value="<?= $row['id']; ?>">
                                        <input type="hidden" name="current_status" value="<?= isset($row['read_status']) ? $row['read_status'] : 0; ?>">
                                        <button type="submit" name="toggle_read_btn" class="btn <?= !isset($row['read_status']) || $row['read_status'] == 0 ? 'btn-success' : 'btn-warning'; ?> btn-sm">
                                            <i class="fas <?= !isset($row['read_status']) || $row['read_status'] == 0 ? 'fa-check' : 'fa-undo'; ?>"></i>
                                            <?= !isset($row['read_status']) || $row['read_status'] == 0 ? 'Mark as Read' : 'Mark as Unread'; ?>
                                        </button>
                                    </form>
                                    <form action="call_back.php" method="POST">
                                        <input type="hidden" name="delete_id" value="<?= $row['id']; ?>">
                                        <button type="submit" name="delete_btn" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this request?')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php
                            }
                        } else {
                        ?>
                        <tr>
                            <td colspan="8" class="text-center">No Call Back Requests Found</td>
                        </tr>
                        <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
include('../../includes/footer.php');
?>
