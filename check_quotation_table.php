<?php
require 'admin/config/function.php';

echo "<h2>Checking quotation_items Table Structure</h2>";

// Check if SKU field exists in quotation_items table
$check_sku = mysqli_query($conn, "SHOW COLUMNS FROM quotation_items LIKE 'sku'");

if(mysqli_num_rows($check_sku) == 0) {
    echo "<p style='color: orange;'>SKU field does not exist in quotation_items table. Adding it now...</p>";
    
    // Add SKU field to quotation_items table
    $add_sku_sql = "ALTER TABLE quotation_items ADD COLUMN sku VARCHAR(100) DEFAULT '' AFTER size_name";
    
    if(mysqli_query($conn, $add_sku_sql)) {
        echo "<p style='color: green;'>✓ SKU field added successfully to quotation_items table.</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to add SKU field: " . mysqli_error($conn) . "</p>";
    }
} else {
    echo "<p style='color: green;'>✓ SKU field already exists in quotation_items table.</p>";
}

// Show current table structure
echo "<h3>Current quotation_items table structure:</h3>";
$structure = mysqli_query($conn, "DESCRIBE quotation_items");

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

while($row = mysqli_fetch_assoc($structure)) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<p><a href='quotation.php'>← Back to Quotation</a></p>";
?>
