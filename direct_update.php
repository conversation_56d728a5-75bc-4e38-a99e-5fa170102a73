<?php
session_start();
header('Content-Type: application/json');

// Get the key and quantity from the URL
$key = $_GET['key'] ?? '';
$quantity = intval($_GET['quantity'] ?? 1);

// Debug information before update
$before = [
    'session_id' => session_id(),
    'quotation_items' => $_SESSION['quotation_items'] ?? [],
    'quotation_keys' => array_keys($_SESSION['quotation_items'] ?? [])
];

// Update the item if it exists
$success = false;
$message = 'Item not found';

if (!empty($key) && isset($_SESSION['quotation_items'][$key])) {
    $_SESSION['quotation_items'][$key]['quantity'] = $quantity;
    $success = true;
    $message = 'Quantity updated successfully';
}

// Debug information after update
$after = [
    'quotation_items' => $_SESSION['quotation_items'] ?? [],
    'quotation_keys' => array_keys($_SESSION['quotation_items'] ?? [])
];

// Response
$response = [
    'success' => $success,
    'message' => $message,
    'key' => $key,
    'quantity' => $quantity,
    'before' => $before,
    'after' => $after
];

// If this is a browser request, redirect back to the quotation page
if (!isset($_GET['json'])) {
    // Set a session message
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $success ? 'success' : 'danger';

    // Redirect back to the quotation page
    header('Location: quotation.php');
    exit;
}

// Otherwise, return JSON
echo json_encode($response, JSON_PRETTY_PRINT);
?>
