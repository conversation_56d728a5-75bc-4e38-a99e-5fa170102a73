<?php
session_start();
require_once 'admin/config/dbcon.php';

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'item_count' => 0,
    'debug' => []
];

// Log request data for debugging
$response['debug']['request_method'] = $_SERVER['REQUEST_METHOD'];
$response['debug']['post_data'] = $_POST;
$response['debug']['session_status'] = session_status();
$response['debug']['session_id'] = session_id();
$response['debug']['session_data'] = isset($_SESSION) ? array_keys($_SESSION) : 'No session data';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
    // Get product ID and quantity
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    $color_id = isset($_POST['color_id']) ? intval($_POST['color_id']) : null;
    $size_id = isset($_POST['size_id']) ? intval($_POST['size_id']) : null;
    $color_additional_price = isset($_POST['color_additional_price']) ? floatval($_POST['color_additional_price']) : 0;
    $size_additional_price = isset($_POST['size_additional_price']) ? floatval($_POST['size_additional_price']) : 0;
    $additional_price = isset($_POST['additional_price']) ? floatval($_POST['additional_price']) : ($color_additional_price + $size_additional_price);

    // Validate product ID
    if ($product_id <= 0) {
        $response['message'] = 'Invalid product ID';
        echo json_encode($response);
        exit;
    }

    // Validate quantity
    if ($quantity <= 0) {
        $quantity = 1;
    }

    // Get product details
    $sql = "SELECT * FROM products WHERE id = ? AND status = 0";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $product_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) === 0) {
        $response['message'] = 'Product not found';
        echo json_encode($response);
        exit;
    }

    $product = mysqli_fetch_assoc($result);

    // Get variant details if color_id or size_id is provided
    $variant = null;
    $variant_price = $additional_price; // Use the additional_price from the request
    $sku = '';

    if ($color_id || $size_id) {
        $variant_sql = "SELECT * FROM product_variants WHERE product_id = ?";
        $params = [$product_id];
        $types = "i";

        if ($color_id) {
            $variant_sql .= " AND color_id = ?";
            $params[] = $color_id;
            $types .= "i";
        }

        if ($size_id) {
            $variant_sql .= " AND size_id = ?";
            $params[] = $size_id;
            $types .= "i";
        }

        $variant_stmt = mysqli_prepare($conn, $variant_sql);
        mysqli_stmt_bind_param($variant_stmt, $types, ...$params);
        mysqli_stmt_execute($variant_stmt);
        $variant_result = mysqli_stmt_get_result($variant_stmt);

        if (mysqli_num_rows($variant_result) > 0) {
            $variant = mysqli_fetch_assoc($variant_result);
            $variant_price = $variant['additional_price'];
            $sku = $variant['sku'] ?? '';
        }
    }

    // Get color and size names if applicable
    $color_name = '';
    $size_name = '';

    if ($color_id) {
        $color_sql = "SELECT name FROM product_colors WHERE id = ?";
        $color_stmt = mysqli_prepare($conn, $color_sql);
        mysqli_stmt_bind_param($color_stmt, "i", $color_id);
        mysqli_stmt_execute($color_stmt);
        $color_result = mysqli_stmt_get_result($color_stmt);

        if (mysqli_num_rows($color_result) > 0) {
            $color = mysqli_fetch_assoc($color_result);
            $color_name = $color['name'];
        }
    }

    if ($size_id) {
        $size_sql = "SELECT name FROM product_sizes WHERE id = ?";
        $size_stmt = mysqli_prepare($conn, $size_sql);
        mysqli_stmt_bind_param($size_stmt, "i", $size_id);
        mysqli_stmt_execute($size_stmt);
        $size_result = mysqli_stmt_get_result($size_stmt);

        if (mysqli_num_rows($size_result) > 0) {
            $size = mysqli_fetch_assoc($size_result);
            $size_name = $size['name'];
        }
    }

    // Calculate price
    $base_price = $product['vat_percentage'] > 0 ? $product['price'] : $product['sales_price'];
    $item_price = $base_price;

    // Get bulk pricing if available
    $bulk_pricing = [];
    $bulk_sql = "SELECT * FROM product_bulk_pricing WHERE product_id = ? ORDER BY min_quantity ASC";
    $bulk_stmt = mysqli_prepare($conn, $bulk_sql);
    mysqli_stmt_bind_param($bulk_stmt, "i", $product_id);
    mysqli_stmt_execute($bulk_stmt);
    $bulk_result = mysqli_stmt_get_result($bulk_stmt);

    if (mysqli_num_rows($bulk_result) > 0) {
        while ($bulk = mysqli_fetch_assoc($bulk_result)) {
            $bulk_pricing[] = $bulk;
        }
    }

    // Create item array
    $item = [
        'id' => $product_id,
        'name' => $product['name'],
        'price' => $item_price,
        'quantity' => $quantity,
        'color_id' => $color_id,
        'color_name' => $color_name,
        'size_id' => $size_id,
        'size_name' => $size_name,
        'variant_id' => $variant ? $variant['id'] : null,
        'sku' => $sku,
        'image' => $product['image'] ?? '',
        'color_additional_price' => $color_additional_price,
        'size_additional_price' => $size_additional_price,
        'additional_price' => $color_additional_price + $size_additional_price,
        'bulk_pricing' => $bulk_pricing
    ];

    // Initialize quotation session if not exists
    if (!isset($_SESSION['quotation_items'])) {
        $_SESSION['quotation_items'] = [];
    }

    // Check if item already exists in quotation
    $item_exists = false;
    foreach ($_SESSION['quotation_items'] as $key => $quotation_item) {
        if ($quotation_item['id'] == $product_id &&
            $quotation_item['color_id'] == $color_id &&
            $quotation_item['size_id'] == $size_id) {
            // Update quantity
            $_SESSION['quotation_items'][$key]['quantity'] += $quantity;
            $item_exists = true;
            break;
        }
    }

    // Add item to quotation if not exists
    if (!$item_exists) {
        $_SESSION['quotation_items'][] = $item;
    }

    // Set response
    $response['success'] = true;
    $response['message'] = 'Product added to quotation';
    $response['item_count'] = count($_SESSION['quotation_items']);
    } catch (Exception $e) {
        $response['success'] = false;
        $response['message'] = 'Error: ' . $e->getMessage();
        $response['debug']['error'] = $e->getTraceAsString();
    }
}

// Return response
echo json_encode($response);
?>
