<?php
require '../../config/function.php';
require '../../config/dbcon.php';
require '../../includes/header.php';
include('product_audio.php'); // Include the product audio functionality

// Get product ID
$paramValue = checkParamId('id');
if(!is_numeric($paramValue)){
    redirect('products.php', 'Please provide a valid ID');
    exit;
}

$product = getById('products', $paramValue);
if($product['status'] != 200){
    redirect('products.php', 'No such product found');
    exit;
}

// Set default product type
$product_type_id = $product['data']['product_type_id'] ?? 1; // Default to Standard (1) if not set
$product_type_name = 'Standard';

// Initialize product categories array
$product_categories = [];

// Initialize bulk pricing array
$bulk_pricing = [];
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Product: <?= $product['data']['name']; ?>
                <a href="products.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <form action="code.php" method="POST" enctype="multipart/form-data" id="mainProductForm">
                <input type="hidden" name="product_id" value="<?= $product['data']['id']; ?>">

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label>Select Category *</label>
                        <select name="category_id" class="form-select">
                            <option value="">Select Category *</option>
                            <?php
                            $categories = getAll('categories');
                            if($categories['status'] == 200){
                                foreach($categories['data'] as $category){
                                    $selected = $category['id'] == $product['data']['category_id'] ? 'selected' : '';
                                    echo "<option value='{$category['id']}' $selected>{$category['name']}</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>

                    <div class="col-md-12 mb-3">
                        <label>Product Name *</label>
                        <input type="text" name="name" value="<?= $product['data']['name']; ?>" required class="form-control" />
                    </div>

                    <div class="col-md-12 mb-3">
                        <label>Description</label>
                        <textarea name="description" class="form-control" rows="3"><?= $product['data']['description']; ?></textarea>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label>Price *</label>
                        <input type="text" name="price" value="<?= $product['data']['price']; ?>" required class="form-control" />
                    </div>

                    <div class="col-md-4 mb-3">
                        <label>Quantity *</label>
                        <input type="text" name="quantity" value="<?= $product['data']['quantity']; ?>" required class="form-control" />
                    </div>

                    <div class="col-md-4 mb-3">
                        <label>Image</label>
                        <input type="file" name="image" class="form-control" />
                        <input type="hidden" name="old_image" value="<?= $product['data']['image']; ?>" />
                    </div>

                    <div class="col-md-6 mb-3">
                        <label>Status</label>
                        <br/>
                        <input type="checkbox" name="status" <?= $product['data']['status'] == 0 ? 'checked':''; ?> style="width:30px;height:30px;" />
                    </div>

                    <div class="col-md-6 mb-3">
                        <label>Trending</label>
                        <br/>
                        <input type="checkbox" name="trending" <?= $product['data']['trending'] == 0 ? 'checked':''; ?> style="width:30px;height:30px;" />
                    </div>

                    <!-- Regular buttons (will be hidden on larger screens) -->
                    <div class="col-md-6 d-md-none">
                        <div class="d-flex gap-2">
                            <button type="submit" name="updateProduct" class="btn btn-primary">Update</button>
                            <button type="button" class="btn btn-success" onclick="openVariantsPopup(<?= $product['data']['id']; ?>);">
                                <i class="fas fa-tags me-1"></i> Manage Variants
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <hr>

            <!-- Product Variants -->
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 d-flex align-items-center">
                                <i class="fas fa-tags me-2"></i>Product Variants
                                <?php createAudioButton('variantsAudioBtn', 'Listen to Product Variants explanation'); ?>
                            </h5>
                            <button type="button" class="btn btn-success" onclick="openVariantsPopup(<?= $product['data']['id']; ?>);">
                                <i class="fas fa-tags me-1"></i> Manage Variants
                            </button>
                        </div>

                        <!-- JavaScript for Variants Popup -->
                        <script>
                        function openVariantsPopup(productId) {
                            // Open variants-popup.php in a popup window
                            const width = Math.min(1200, window.innerWidth * 0.9);
                            const height = Math.min(800, window.innerHeight * 0.9);
                            const left = (window.innerWidth - width) / 2;
                            const top = (window.innerHeight - height) / 2;

                            const popup = window.open(
                                'variants-popup.php?id=' + productId,
                                'ManageVariants',
                                `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes,location=no,menubar=no,toolbar=no,status=no`
                            );

                            // Focus the popup
                            if (popup) {
                                popup.focus();
                            } else {
                                alert('Popup blocked! Please allow popups for this site to manage variants.');
                            }
                        }
                        </script>
                    </div>
                    <div class="card-body">
                        <?php
                        // Check if product has variants
                        $has_variants = false;
                        $variant_count = 0;
                        $variant_query = mysqli_query($conn, "SELECT COUNT(*) as variant_count FROM product_variants WHERE product_id = {$product['data']['id']}");
                        if($variant_query) {
                            $variant_count = mysqli_fetch_assoc($variant_query)['variant_count'];
                            $has_variants = $variant_count > 0;
                        }

                        // Get variant summary
                        $colors_summary = [];
                        $sizes_summary = [];

                        if($has_variants) {
                            // Get colors
                            $colors_query = mysqli_query($conn, "SELECT DISTINCT pc.name
                                                        FROM product_variants pv
                                                        JOIN product_colors pc ON pv.color_id = pc.id
                                                        WHERE pv.product_id = {$product['data']['id']}
                                                        AND pv.color_id IS NOT NULL");
                            if($colors_query) {
                                while($row = mysqli_fetch_assoc($colors_query)) {
                                    $colors_summary[] = $row['name'];
                                }
                            }

                            // Get sizes
                            $sizes_query = mysqli_query($conn, "SELECT DISTINCT ps.name
                                                        FROM product_variants pv
                                                        JOIN product_sizes ps ON pv.size_id = ps.id
                                                        WHERE pv.product_id = {$product['data']['id']}
                                                        AND pv.size_id IS NOT NULL");
                            if($sizes_query) {
                                while($row = mysqli_fetch_assoc($sizes_query)) {
                                    $sizes_summary[] = $row['name'];
                                }
                            }
                        }
                        ?>

                        <div class="alert <?= $has_variants ? 'alert-success' : 'alert-info' ?>">
                            <i class="fas <?= $has_variants ? 'fa-check-circle' : 'fa-info-circle' ?> me-2"></i>
                            <?php if($has_variants): ?>
                                This product has <strong><?= $variant_count ?> variant(s)</strong>.
                            <?php else: ?>
                                This product does not have any variants yet.
                            <?php endif; ?>
                        </div>

                        <?php if($has_variants): ?>
                        <div class="row mb-3">
                            <?php if(!empty($colors_summary)): ?>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-light py-2">
                                        <h6 class="mb-0">Colors</h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <?= implode(', ', $colors_summary) ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if(!empty($sizes_summary)): ?>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-light py-2">
                                        <h6 class="mb-0">Sizes</h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <?= implode(', ', $sizes_summary) ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <p class="text-muted">Click the "Manage Variants" button to add, edit, or remove variants in a popup window.</p>
                    </div>
                </div>
            </div>

            <!-- Bulk Pricing -->
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-money-bill-wave me-2"></i>Bulk Pricing
                            <?php createAudioButton('bulkPricingAudioBtn', 'Listen to Bulk Pricing explanation'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="enableBulkPricing" name="enable_bulk_pricing" value="1" <?= isset($product['data']['enable_bulk_pricing']) && $product['data']['enable_bulk_pricing'] == 1 ? 'checked' : '' ?>>
                            <label class="form-check-label" for="enableBulkPricing">
                                Enable bulk pricing
                            </label>
                        </div>
                        <small class="text-muted">Set different prices for quantity ranges</small>

                        <div id="bulkPricingContainer" style="display: <?= isset($product['data']['enable_bulk_pricing']) && $product['data']['enable_bulk_pricing'] == 1 ? 'block' : 'none'; ?>;">
                            <div class="table-responsive mt-3">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Quantity Range</th>
                                            <th>Price per Unit</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bulkPricingTableBody">
                                        <!-- Bulk pricing rows will be added here -->
                                    </tbody>
                                </table>
                            </div>
                            <button type="button" id="addBulkPricingRow" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-plus me-1"></i> Add Pricing Row
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Type -->
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-cube me-2"></i>Product Type
                            <?php createAudioButton('productTypeAudioBtn', 'Listen to Product Type explanation'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label>Product Type</label>
                            <select name="product_type_id" class="form-select">
                                <option value="1" <?= $product_type_id == 1 ? 'selected' : '' ?>>Standard</option>
                                <option value="2" <?= $product_type_id == 2 ? 'selected' : '' ?>>Buildable</option>
                                <option value="3" <?= $product_type_id == 3 ? 'selected' : '' ?>>Component</option>
                            </select>
                            <small class="text-muted">Select the type of product</small>
                        </div>

                        <div id="componentOptions" style="display: <?= $product_type_id != 2 ? 'block' : 'none'; ?>;">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="canBeComponent" name="can_be_component" value="1" <?= isset($product['data']['can_be_component']) && $product['data']['can_be_component'] == 1 ? 'checked' : '' ?>>
                                <label class="form-check-label" for="canBeComponent">
                                    This product can be used as a component in buildable products
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bulk Pricing Toggle
    const enableBulkPricingCheckbox = document.getElementById('enableBulkPricing');
    const bulkPricingContainer = document.getElementById('bulkPricingContainer');

    if (enableBulkPricingCheckbox && bulkPricingContainer) {
        enableBulkPricingCheckbox.addEventListener('change', function() {
            bulkPricingContainer.style.display = this.checked ? 'block' : 'none';
        });
    }

    // Product Type Toggle
    const productTypeSelect = document.querySelector('select[name="product_type_id"]');
    const componentOptions = document.getElementById('componentOptions');

    if (productTypeSelect && componentOptions) {
        productTypeSelect.addEventListener('change', function() {
            // If Buildable (2), hide component options
            componentOptions.style.display = this.value != 2 ? 'block' : 'none';
        });
    }

    // Add Bulk Pricing Row
    const addBulkPricingRowBtn = document.getElementById('addBulkPricingRow');
    const bulkPricingTableBody = document.getElementById('bulkPricingTableBody');

    if (addBulkPricingRowBtn && bulkPricingTableBody) {
        addBulkPricingRowBtn.addEventListener('click', function() {
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>
                    <div class="input-group">
                        <input type="number" name="bulk_min_qty[]" class="form-control" placeholder="Min" min="2">
                        <span class="input-group-text">-</span>
                        <input type="number" name="bulk_max_qty[]" class="form-control" placeholder="Max" min="5">
                    </div>
                </td>
                <td>
                    <input type="number" step="0.01" name="bulk_price[]" class="form-control" placeholder="Price per unit">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-bulk-row">Remove</button>
                </td>
            `;

            bulkPricingTableBody.appendChild(newRow);

            // Add event listener to the new remove button
            newRow.querySelector('.remove-bulk-row').addEventListener('click', function() {
                this.closest('tr').remove();
            });
        });

        // Remove Bulk Pricing Row
        document.querySelectorAll('.remove-bulk-row').forEach(function(button) {
            button.addEventListener('click', function() {
                this.closest('tr').remove();
            });
        });
    }
});
</script>

<?php include('../../includes/footer.php'); ?>
