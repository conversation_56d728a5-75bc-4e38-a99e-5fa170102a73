<?php include('includes/header.php'); ?>
<script src="https://cdn.ckeditor.com/ckeditor5/23.0.0/classic/ckeditor.js"></script>
<?php
// Get VAT settings
function getVatSettings() {
    global $conn;
    $stmt = mysqli_prepare($conn, "SELECT vat_percentage FROM settings LIMIT 1");
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($row = mysqli_fetch_assoc($result)) {
        return $row['vat_percentage'];
    }
    return 0;
}

function insertService($data) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, 
        "INSERT INTO services (name, description, cost_price, sales_price, 
         vat_percentage, price, quantity, status) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
    );
    
    mysqli_stmt_bind_param($stmt, "ssdddddi",
        $data['name'],
        $data['description'],
        $data['cost_price'],
        $data['sales_price'],
        $data['vat_percentage'],
        $data['price'],
        $data['quantity'],
        $data['status']
    );
    
    return mysqli_stmt_execute($stmt);
}

$vat_percentage = getVatSettings();
?>

<script>
function calculatePrice() {
    const salePrice = parseFloat(document.getElementById("sale_price").value) || 0;
    const vat = parseFloat(document.getElementById("vat").value) || 0;
    const price = salePrice * (1 + (vat/100));
    document.getElementById("price").value = price.toFixed(2);
}
</script>
<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Add Service
                <a href="services.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">

            <?php alertMessage(); ?>

            <form action="code.php" method="POST" enctype="multipart/form-data">

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label>Service Name *</label>
                        <input type="text" name="name" required class="form-control" />
                    </div>
<div class="col-md-12 mb-3">
    <label>Description</label>
    <textarea name="description" class="form-control" rows="3" id="description"></textarea>
</div>

<script>
ClassicEditor
    .create( document.querySelector( '#description' ), {
        toolbar: {
            items: [
                'heading',
                '|',
                'bold',
                'italic',
                'underline',
                'fontSize',
                'fontFamily',
                'fontColor',
                'alignment',
                'bullets',
                'numberedList',
                'mediaEmbed',
                '|',
                'undo',
                'redo'
            ]
        }
    } )
    .then( editor => {
        console.log( editor );
    } )
    .catch( error => {
        console.error( error );
    } );
</script>
                    <div class="col-md-2 mb-3">
                        <label for="">Cost Price *</label>
                        <input type="text" name="cost_price" required class="form-control" />
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="">Sales Price *</label>
                        <input type="text" name="sales_price" id="sale_price"  onfocusout="calculatePrice()" class="form-control"  />
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="">Vat % *</label>
                        <input type="text" id="vat" value="<?php echo  $vat_percentage ?>" name="vat_percentage" required class="form-control" />
                        <!-- <input type="text" value="<?php echo  $row['vat_percentage'];?>" name="vat_percentage" required class="form-control" /> -->
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="">Price *</label>
                        <input type="text" name="price" id="price" readonly class="form-control" />
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="">Quantity *</label>
                        <input type="text" name="quantity" required class="form-control" />
                    </div>

                    <div class="col-md-6">
                        <label>Status (UnChecked=Visible, Checked=Hidden)</label>
                        <br/>
                        <input type="checkbox" name="status" style="width:30px;height:30px";>
                    </div>
                    <div class="col-md-6 mb-3 text-end">
                        <br/>
                        <button type="submit" name="saveService" class="btn btn-primary">Save</button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>
