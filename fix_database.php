<?php
// Include database connection
require_once('admin/config/dbcon.php');

// Direct SQL update for product ID 10
$sql = "UPDATE products SET description = '<p>colour <i>product</i> <strong>test</strong></p>' WHERE id = 10";

if(mysqli_query($conn, $sql)) {
    echo "<div style='padding: 20px; background-color: #d4edda; color: #155724; margin-bottom: 15px;'>
            <h3>Success!</h3>
            <p>The product description has been fixed directly in the database.</p>
          </div>";
} else {
    echo "<div style='padding: 20px; background-color: #f8d7da; color: #721c24; margin-bottom: 15px;'>
            <h3>Error!</h3>
            <p>Failed to update the database: " . mysqli_error($conn) . "</p>
          </div>";
}

echo "<div style='padding: 20px;'>
        <a href='admin/modules/products/products-edit.php?id=10' class='btn btn-primary' style='display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;'>Go to Product Edit Page</a>
      </div>";
?>
