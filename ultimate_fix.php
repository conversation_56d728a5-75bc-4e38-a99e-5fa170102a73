<?php
// Include database connection
include('admin/config/dbcon.php');

// Function to update the product_list.php file
function updateProductListFile() {
    // Find the section in product_list.php that processes variants
    $file_path = 'product_list.php';
    $file_content = file_get_contents($file_path);
    
    // Find the section that processes variants
    $start_marker = "            // Get unique colors and sizes";
    $end_marker = "        // Get bulk pricing if any";
    
    $start_pos = strpos($file_content, $start_marker);
    $end_pos = strpos($file_content, $end_marker, $start_pos);
    
    if ($start_pos !== false && $end_pos !== false) {
        // Extract the section
        $section_length = $end_pos - $start_pos;
        $old_section = substr($file_content, $start_pos, $section_length);
        
        // Create the new section
        $new_section = "            // Get unique colors and sizes with their additional prices
            \$product['colors'] = [];
            \$product['sizes'] = [];
            
            // First, collect all unique colors and sizes
            \$color_map = []; // To track unique colors
            \$size_map = []; // To track unique sizes
            
            foreach (\$product['variants'] as \$variant) {
                if (!empty(\$variant['color_id'])) {
                    \$color_id = \$variant['color_id'];
                    if (!isset(\$color_map[\$color_id])) {
                        \$color_map[\$color_id] = [
                            'id' => \$color_id,
                            'name' => \$variant['color_name'],
                            'color_code' => \$variant['color_code'],
                            'additional_price' => 0
                        ];
                    }
                    
                    // Update additional price if this variant has a higher price
                    if (\$variant['additional_price'] > \$color_map[\$color_id]['additional_price']) {
                        \$color_map[\$color_id]['additional_price'] = \$variant['additional_price'];
                    }
                }
                
                if (!empty(\$variant['size_id'])) {
                    \$size_id = \$variant['size_id'];
                    if (!isset(\$size_map[\$size_id])) {
                        \$size_map[\$size_id] = [
                            'id' => \$size_id,
                            'name' => \$variant['size_name'],
                            'additional_price' => 0
                        ];
                    }
                    
                    // Update additional price if this variant has a higher price
                    if (\$variant['additional_price'] > \$size_map[\$size_id]['additional_price']) {
                        \$size_map[\$size_id]['additional_price'] = \$variant['additional_price'];
                    }
                }
            }
            
            // Convert maps to arrays
            \$product['colors'] = array_values(\$color_map);
            \$product['sizes'] = array_values(\$size_map);";
        
        // Replace the old section with the new section
        $new_content = str_replace($old_section, $new_section, $file_content);
        
        // Write the updated content back to the file
        file_put_contents($file_path, $new_content);
        
        return true;
    }
    
    return false;
}

// Update the product_list.php file
$result = updateProductListFile();

// Display the result
echo "<h1>Ultimate Fix for product_list.php</h1>";
if ($result) {
    echo "<p>Successfully updated the product_list.php file!</p>";
} else {
    echo "<p>Failed to update the product_list.php file.</p>";
}

// Now, let's test the variant display
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.id = 10";

$result = mysqli_query($conn, $sql);
$product = mysqli_fetch_assoc($result);

// Get variants
$variantsQuery = "SELECT pv.*, pc.name as color_name, pc.color_code, ps.name as size_name
                  FROM product_variants pv
                  LEFT JOIN product_colors pc ON pv.color_id = pc.id
                  LEFT JOIN product_sizes ps ON pv.size_id = ps.id
                  WHERE pv.product_id = 10 AND pv.status = 0";
$variantsResult = mysqli_query($conn, $variantsQuery);
$product['variants'] = mysqli_fetch_all($variantsResult, MYSQLI_ASSOC);

// Process variants using the new approach
$product['colors'] = [];
$product['sizes'] = [];

// First, collect all unique colors and sizes
$color_map = []; // To track unique colors
$size_map = []; // To track unique sizes

foreach ($product['variants'] as $variant) {
    if (!empty($variant['color_id'])) {
        $color_id = $variant['color_id'];
        if (!isset($color_map[$color_id])) {
            $color_map[$color_id] = [
                'id' => $color_id,
                'name' => $variant['color_name'],
                'color_code' => $variant['color_code'],
                'additional_price' => 0
            ];
        }
        
        // Update additional price if this variant has a higher price
        if ($variant['additional_price'] > $color_map[$color_id]['additional_price']) {
            $color_map[$color_id]['additional_price'] = $variant['additional_price'];
        }
    }
    
    if (!empty($variant['size_id'])) {
        $size_id = $variant['size_id'];
        if (!isset($size_map[$size_id])) {
            $size_map[$size_id] = [
                'id' => $size_id,
                'name' => $variant['size_name'],
                'additional_price' => 0
            ];
        }
        
        // Update additional price if this variant has a higher price
        if ($variant['additional_price'] > $size_map[$size_id]['additional_price']) {
            $size_map[$size_id]['additional_price'] = $variant['additional_price'];
        }
    }
}

// Convert maps to arrays
$product['colors'] = array_values($color_map);
$product['sizes'] = array_values($size_map);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .color-option, .size-option {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .color-swatch {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 1px solid #ddd;
            transition: all 0.2s ease;
        }
        .size-box {
            min-width: 50px;
            height: 50px;
            border: 1px solid #ddd;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.2s ease;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2>Test Results</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title"><?= htmlspecialchars($product['name']) ?></h3>
                        <p><strong>Category:</strong> <?= htmlspecialchars($product['category_name']) ?></p>
                        <h4 id="product-price">R <?= number_format($product['sales_price'], 2) ?></h4>
                        
                        <!-- Variant details will be displayed here -->
                        <div id="variant-details" class="mt-2"></div>
                        
                        <div class="product-variants-section mt-4">
                            <h5 class="mb-3">Available Options</h5>
                            
                            <?php if(!empty($product['colors'])): ?>
                            <div class="mb-3">
                                <label class="form-label">Colors:</label>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php foreach($product['colors'] as $color): ?>
                                        <div class="color-option" data-color-id="<?= $color['id'] ?>" data-additional-price="<?= $color['additional_price'] ?>">
                                            <div class="color-swatch" style="background-color: <?= $color['color_code'] ?>;"
                                                 title="<?= htmlspecialchars($color['name']) ?><?= $color['additional_price'] > 0 ? ' (+R '.number_format($color['additional_price'], 2).')' : '' ?>"></div>
                                            <small class="d-block mt-1 text-center">
                                                <?= htmlspecialchars($color['name']) ?>
                                                <?php if($color['additional_price'] > 0): ?>
                                                    <span class="text-danger">(+R <?= number_format($color['additional_price'], 2) ?>)</span>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if(!empty($product['sizes'])): ?>
                            <div class="mb-3">
                                <label class="form-label">Sizes:</label>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php foreach($product['sizes'] as $size): ?>
                                        <div class="size-option" data-size-id="<?= $size['id'] ?>" data-additional-price="<?= $size['additional_price'] ?>">
                                            <div class="size-box">
                                                <span><?= htmlspecialchars($size['name']) ?></span>
                                                <?php if($size['additional_price'] > 0): ?>
                                                    <span class="text-danger" style="font-size: 10px; font-weight: normal;">+R <?= number_format($size['additional_price'], 2) ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Debug Information</h3>
                
                <h4>Colors with Additional Prices</h4>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Color</th>
                            <th>Additional Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($product['colors'] as $color): ?>
                            <tr>
                                <td><?= htmlspecialchars($color['name']) ?></td>
                                <td>R <?= number_format($color['additional_price'], 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <h4>Sizes with Additional Prices</h4>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Size</th>
                            <th>Additional Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($product['sizes'] as $size): ?>
                            <tr>
                                <td><?= htmlspecialchars($size['name']) ?></td>
                                <td>R <?= number_format($size['additional_price'], 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <h4>All Variants</h4>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Color</th>
                            <th>Size</th>
                            <th>Additional Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($product['variants'] as $variant): ?>
                            <tr>
                                <td><?= htmlspecialchars($variant['color_name']) ?></td>
                                <td><?= htmlspecialchars($variant['size_name']) ?></td>
                                <td>R <?= number_format($variant['additional_price'], 2) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
