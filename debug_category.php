<?php
include('admin/config/dbcon.php');

// Get the category filter from URL
$category_filter = isset($_GET['category']) ? $_GET['category'] : '0';

echo "<h2>Debug Category Filter</h2>";
echo "<p>Category Filter Value: '" . htmlspecialchars($category_filter) . "'</p>";
echo "<p>Type: " . gettype($category_filter) . "</p>";
echo "<p>Empty: " . (empty($category_filter) ? 'Yes' : 'No') . "</p>";
echo "<p>Comparison with '0': " . ($category_filter !== '0' ? 'Not Equal' : 'Equal') . "</p>";

// Test the SQL query
$sql = "SELECT p.id, p.name, p.category_id 
        FROM products p
        WHERE p.status = 0";

// Add category filter if selected
if ($category_filter !== '0') {
    $sql .= " AND (p.category_id = '$category_filter')";
}

echo "<p>SQL Query: " . htmlspecialchars($sql) . "</p>";

// Execute the query
$result = mysqli_query($conn, $sql);
if ($result) {
    echo "<p>Query executed successfully. Found " . mysqli_num_rows($result) . " products.</p>";
} else {
    echo "<p>Query error: " . mysqli_error($conn) . "</p>";
}

// Show links to test
echo "<h3>Test Links</h3>";
echo "<ul>";
echo "<li><a href='debug_category.php'>No parameter</a></li>";
echo "<li><a href='debug_category.php?category='>Empty parameter</a></li>";
echo "<li><a href='debug_category.php?category=0'>Category=0</a></li>";
echo "<li><a href='debug_category.php?category=1'>Category=1</a></li>";
echo "<li><a href='debug_category.php?category=3'>Category=3</a></li>";
echo "</ul>";

// Back to product list
echo "<p><a href='product_list.php'>Back to Product List</a></p>";
?>
