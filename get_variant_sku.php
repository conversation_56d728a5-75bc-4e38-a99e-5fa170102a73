<?php
header('Content-Type: application/json');

require 'admin/config/function.php';

$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
$color_id = isset($_GET['color_id']) ? intval($_GET['color_id']) : null;
$size_id = isset($_GET['size_id']) ? intval($_GET['size_id']) : null;

if (!$product_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid product ID']);
    exit;
}

// Try different strategies to find the variant
$variant = null;

// Strategy 1: Exact match (both color and size if provided)
if ($color_id && $size_id) {
    $query = "SELECT sku, additional_price, quantity, status
              FROM product_variants
              WHERE product_id = ? AND color_id = ? AND size_id = ?
              ORDER BY id ASC
              LIMIT 1";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "iii", $product_id, $color_id, $size_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && mysqli_num_rows($result) > 0) {
        $variant = mysqli_fetch_assoc($result);
    }
}

// Strategy 2: If only color is selected, find any variant with that color
if (!$variant && $color_id && !$size_id) {
    $query = "SELECT sku, additional_price, quantity, status
              FROM product_variants
              WHERE product_id = ? AND color_id = ?
              ORDER BY id ASC
              LIMIT 1";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ii", $product_id, $color_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && mysqli_num_rows($result) > 0) {
        $variant = mysqli_fetch_assoc($result);
    }
}

// Strategy 3: If only size is selected, find any variant with that size
if (!$variant && $size_id && !$color_id) {
    $query = "SELECT sku, additional_price, quantity, status
              FROM product_variants
              WHERE product_id = ? AND size_id = ?
              ORDER BY id ASC
              LIMIT 1";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ii", $product_id, $size_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && mysqli_num_rows($result) > 0) {
        $variant = mysqli_fetch_assoc($result);
    }
}

// Strategy 4: If color is selected but no exact match, try color-only variants
if (!$variant && $color_id) {
    $query = "SELECT sku, additional_price, quantity, status
              FROM product_variants
              WHERE product_id = ? AND color_id = ? AND size_id IS NULL
              ORDER BY id ASC
              LIMIT 1";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ii", $product_id, $color_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && mysqli_num_rows($result) > 0) {
        $variant = mysqli_fetch_assoc($result);
    }
}

// Strategy 5: If size is selected but no exact match, try size-only variants
if (!$variant && $size_id) {
    $query = "SELECT sku, additional_price, quantity, status
              FROM product_variants
              WHERE product_id = ? AND size_id = ? AND color_id IS NULL
              ORDER BY id ASC
              LIMIT 1";

    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ii", $product_id, $size_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && mysqli_num_rows($result) > 0) {
        $variant = mysqli_fetch_assoc($result);
    }
}

if ($variant) {
    echo json_encode([
        'success' => true,
        'sku' => $variant['sku'],
        'additional_price' => $variant['additional_price'],
        'quantity' => $variant['quantity'],
        'status' => $variant['status']
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'No variant found for this combination'
    ]);
}
?>
