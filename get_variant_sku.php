<?php
header('Content-Type: application/json');

require 'admin/config/function.php';

$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
$color_id = isset($_GET['color_id']) ? intval($_GET['color_id']) : null;
$size_id = isset($_GET['size_id']) ? intval($_GET['size_id']) : null;

if (!$product_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid product ID']);
    exit;
}

// Build the query to find the exact variant
$where_conditions = ["product_id = ?"];
$params = [$product_id];
$param_types = "i";

if ($color_id) {
    $where_conditions[] = "color_id = ?";
    $params[] = $color_id;
    $param_types .= "i";
} else {
    $where_conditions[] = "color_id IS NULL";
}

if ($size_id) {
    $where_conditions[] = "size_id = ?";
    $params[] = $size_id;
    $param_types .= "i";
} else {
    $where_conditions[] = "size_id IS NULL";
}

$where_clause = implode(" AND ", $where_conditions);

$query = "SELECT sku, additional_price, quantity, status 
          FROM product_variants 
          WHERE $where_clause 
          ORDER BY id ASC 
          LIMIT 1";

$stmt = mysqli_prepare($conn, $query);

if (!empty($params)) {
    mysqli_stmt_bind_param($stmt, $param_types, ...$params);
}

mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if ($result && mysqli_num_rows($result) > 0) {
    $variant = mysqli_fetch_assoc($result);
    
    echo json_encode([
        'success' => true,
        'sku' => $variant['sku'],
        'additional_price' => $variant['additional_price'],
        'quantity' => $variant['quantity'],
        'status' => $variant['status']
    ]);
} else {
    // If exact match not found, try to find a variant with just color or just size
    if ($color_id && $size_id) {
        // Try with just color
        $fallback_query = "SELECT sku, additional_price, quantity, status 
                          FROM product_variants 
                          WHERE product_id = ? AND color_id = ? AND size_id IS NULL
                          ORDER BY id ASC 
                          LIMIT 1";
        
        $fallback_stmt = mysqli_prepare($conn, $fallback_query);
        mysqli_stmt_bind_param($fallback_stmt, "ii", $product_id, $color_id);
        mysqli_stmt_execute($fallback_stmt);
        $fallback_result = mysqli_stmt_get_result($fallback_stmt);
        
        if ($fallback_result && mysqli_num_rows($fallback_result) > 0) {
            $variant = mysqli_fetch_assoc($fallback_result);
            
            echo json_encode([
                'success' => true,
                'sku' => $variant['sku'] . '-PARTIAL',
                'additional_price' => $variant['additional_price'],
                'quantity' => $variant['quantity'],
                'status' => $variant['status'],
                'note' => 'Partial match (color only)'
            ]);
            exit;
        }
        
        // Try with just size
        $fallback_query2 = "SELECT sku, additional_price, quantity, status 
                           FROM product_variants 
                           WHERE product_id = ? AND size_id = ? AND color_id IS NULL
                           ORDER BY id ASC 
                           LIMIT 1";
        
        $fallback_stmt2 = mysqli_prepare($conn, $fallback_query2);
        mysqli_stmt_bind_param($fallback_stmt2, "ii", $product_id, $size_id);
        mysqli_stmt_execute($fallback_stmt2);
        $fallback_result2 = mysqli_stmt_get_result($fallback_stmt2);
        
        if ($fallback_result2 && mysqli_num_rows($fallback_result2) > 0) {
            $variant = mysqli_fetch_assoc($fallback_result2);
            
            echo json_encode([
                'success' => true,
                'sku' => $variant['sku'] . '-PARTIAL',
                'additional_price' => $variant['additional_price'],
                'quantity' => $variant['quantity'],
                'status' => $variant['status'],
                'note' => 'Partial match (size only)'
            ]);
            exit;
        }
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'No variant found for this combination'
    ]);
}
?>
