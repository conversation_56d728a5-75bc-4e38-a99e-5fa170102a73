2025-04-13 15:37:33 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:37:33 - Parsed: product_id=29, colors=1,2,3, sizes=1,2
2025-04-13 15:37:37 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 2
        )

)

2025-04-13 15:37:37 - Parsed: product_id=29, colors=1,2,3, sizes=2
2025-04-13 15:37:38 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

)

2025-04-13 15:37:38 - Parsed: product_id=29, colors=1,2,3, sizes=
2025-04-13 15:37:42 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 1
        )

)

2025-04-13 15:37:42 - Parsed: product_id=29, colors=1,2,3, sizes=1
2025-04-13 15:37:45 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:37:45 - Parsed: product_id=29, colors=1,2, sizes=1,2
2025-04-13 15:37:46 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:37:46 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 15:37:48 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:37:48 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 15:38:07 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:38:07 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 15:38:10 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:38:10 - Parsed: product_id=29, colors=1,2, sizes=1,2
2025-04-13 15:38:11 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:38:11 - Parsed: product_id=29, colors=1,2,3, sizes=1,2
2025-04-13 15:38:12 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 2
        )

)

2025-04-13 15:38:12 - Parsed: product_id=29, colors=1,2,3, sizes=2
2025-04-13 15:38:13 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

)

2025-04-13 15:38:13 - Parsed: product_id=29, colors=1,2,3, sizes=
2025-04-13 15:38:14 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

)

2025-04-13 15:38:14 - Parsed: product_id=29, colors=1,2,3, sizes=
2025-04-13 15:38:16 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 1
        )

)

2025-04-13 15:38:16 - Parsed: product_id=29, colors=1,2,3, sizes=1
2025-04-13 15:38:18 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:38:18 - Parsed: product_id=29, colors=1,2,3, sizes=1,2
2025-04-13 15:38:19 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 2
        )

)

2025-04-13 15:38:19 - Parsed: product_id=29, colors=1,2,3, sizes=2
2025-04-13 15:38:20 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

)

2025-04-13 15:38:20 - Parsed: product_id=29, colors=1,2,3, sizes=
2025-04-13 15:38:23 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

)

2025-04-13 15:38:23 - Parsed: product_id=29, colors=1,2,3, sizes=
2025-04-13 15:38:24 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:38:24 - Parsed: product_id=29, colors=1,2, sizes=
2025-04-13 15:38:25 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

)

2025-04-13 15:38:25 - Parsed: product_id=29, colors=1, sizes=
2025-04-13 15:38:26 - Request: Array
(
    [product_id] => 29
)

2025-04-13 15:38:26 - Parsed: product_id=29, colors=, sizes=
2025-04-13 15:38:27 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

)

2025-04-13 15:38:27 - Parsed: product_id=29, colors=1, sizes=
2025-04-13 15:38:28 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:38:28 - Parsed: product_id=29, colors=1,2, sizes=
2025-04-13 15:38:28 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

)

2025-04-13 15:38:28 - Parsed: product_id=29, colors=1,2,3, sizes=
2025-04-13 15:38:29 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 1
        )

)

2025-04-13 15:38:29 - Parsed: product_id=29, colors=1,2,3, sizes=1
2025-04-13 15:38:30 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:38:30 - Parsed: product_id=29, colors=1,2,3, sizes=1,2
2025-04-13 15:38:31 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 2
            [2] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:38:31 - Parsed: product_id=29, colors=1,2,3, sizes=1,2
2025-04-13 15:44:28 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:44:28 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 15:44:32 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:44:32 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 15:44:33 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:44:33 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 15:44:34 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:44:34 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 15:44:35 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:44:35 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 15:44:36 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 2
        )

)

2025-04-13 15:44:36 - Parsed: product_id=29, colors=3,1, sizes=2
2025-04-13 15:44:37 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

)

2025-04-13 15:44:37 - Parsed: product_id=29, colors=3,1, sizes=
2025-04-13 15:44:38 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
        )

)

2025-04-13 15:44:38 - Parsed: product_id=29, colors=3,1, sizes=1
2025-04-13 15:44:39 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:44:39 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 15:54:45 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:54:45 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 15:54:54 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:54:54 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 15:54:55 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:54:55 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 15:54:57 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:54:57 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 15:54:58 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:54:58 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 15:54:59 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:54:59 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 15:55:00 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 2
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:00 - Parsed: product_id=29, colors=3,2, sizes=1,2
2025-04-13 15:55:00 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 2
            [2] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:00 - Parsed: product_id=29, colors=3,2,1, sizes=1,2
2025-04-13 15:55:03 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 2
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:03 - Parsed: product_id=29, colors=2,1, sizes=1,2
2025-04-13 15:55:03 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:03 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 15:55:04 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:04 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 15:55:09 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:09 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 15:55:09 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 2
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:09 - Parsed: product_id=29, colors=3,2, sizes=1,2
2025-04-13 15:55:11 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:11 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 15:55:12 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 15:55:12 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 16:03:59 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:03:59 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:04:03 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:04:03 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 16:04:04 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:04:04 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 16:04:25 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:04:25 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:04:27 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:04:27 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 16:04:29 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:04:29 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 16:07:43 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:07:43 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:07:46 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:07:46 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 16:07:47 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:07:47 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 16:07:48 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:07:48 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 16:07:49 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:07:49 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:07:55 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:07:55 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 16:07:56 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:07:56 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 16:08:10 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:08:10 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:08:12 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:08:12 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 16:08:13 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:08:13 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 16:12:36 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:12:36 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:12:38 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:12:38 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 16:12:39 - Request: Array
(
    [product_id] => 29
    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:12:39 - Parsed: product_id=29, colors=, sizes=1,2
2025-04-13 16:13:43 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:13:43 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:14:25 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
            [1] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:14:25 - Parsed: product_id=29, colors=1,3, sizes=1,2
2025-04-13 16:16:53 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:16:53 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:17:02 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:17:02 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:17:05 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:17:05 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 16:17:14 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:17:14 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:22:18 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:22:18 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:24:07 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:24:07 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:24:19 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:24:19 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:24:46 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:24:46 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:25:09 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:25:09 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:25:18 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:25:18 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:25:50 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:25:50 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:28:07 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:28:07 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:30:31 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:30:31 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:30:35 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:30:35 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:33:00 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:33:00 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:33:20 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:33:20 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:34:52 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:34:52 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:34:57 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:34:57 - Parsed: product_id=29, colors=3, sizes=1,2
2025-04-13 16:34:57 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:34:57 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:34:58 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:34:58 - Parsed: product_id=29, colors=1, sizes=1,2
2025-04-13 16:34:59 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:34:59 - Parsed: product_id=29, colors=3,1, sizes=1,2
2025-04-13 16:42:42 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:42:42 - Parsed: product_id=29, colors=3,1, sizes=1,2
