2025-04-13 14:33:22 - <PERSON><PERSON><PERSON> started
2025-04-13 14:33:22 - Basic product data prepared - Data: Array
(
    [category_id] => 1
    [name] => Very Simple Product 20250413143322
    [cost_price] => 100
    [sales_price] => 150
    [vat_percentage] => 0
    [vatT] => 0
    [price] => 150
    [quantity] => 10
    [barcode] => TEST9206
    [image] => default.jpg
    [status] => 0
)

2025-04-13 14:33:22 - SQL Query - Data: INSERT INTO products (
        category_id, name, description, barcode, cost_price, sales_price, 
        vat_percentage, vatT, price, quantity, image, status, created_at,
        featured, is_featured, product_type_id, can_be_component
    ) VALUES (
        '1', 'Very Simple Product 20250413143322', 'This is a test product created on 2025-04-13 14:33:22', 'TEST9206', '100', '150',
        '0', '0', '150', '10', 'default.jpg', '0', '2025-04-13 14:33:22',
        '0', '0', '1', '0'
    )
2025-04-13 14:33:22 - Query result - Data: Array
(
    [success] => true
    [product_id] => 24
    [error] => 
)

