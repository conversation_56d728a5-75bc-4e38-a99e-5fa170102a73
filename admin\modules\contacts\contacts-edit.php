<?php 
include('includes/header.php');

function getContactById($conn, $id) {
    $stmt = mysqli_prepare($conn, "SELECT * FROM contacts WHERE id = ? LIMIT 1");
    mysqli_stmt_bind_param($stmt, "i", $id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if(mysqli_num_rows($result) > 0) {
        return ['status' => 200, 'data' => mysqli_fetch_assoc($result)];
    }
    return ['status' => 404, 'message' => 'Contact Not Found'];
}

?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Contact
                <a href="contacts.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <?php
            $paramId = checkParamId('id');
            if(!is_numeric($paramId)) {
                echo '<h5>'.$paramId.'</h5>';
                return false;
            }

            $contact = getContactById($conn, $paramId);
            if($contact['status'] == 200)
            {
            ?>
                <form action="code.php" method="POST">
                    <input type="hidden" name="contactId" value="<?= $contact['data']['id']; ?>">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label>Contact Name *</label>
                            <input type="text" name="contact_name" required class="form-control" 
                                value="<?= $contact['data']['contact_name']; ?>" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label>Contact Number *</label>
                            <input type="text" name="contact_number" required class="form-control" 
                                value="<?= $contact['data']['contact_number']; ?>" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label>Contact Email *</label>
                            <input type="email" name="contact_email" required class="form-control" 
                                value="<?= $contact['data']['contact_email']; ?>" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label>Contact Description</label>
                            <input type="text" name="contact_description" class="form-control" 
                                value="<?= $contact['data']['contact_description']; ?>" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <button type="submit" name="updateContact" class="btn btn-primary">Update Contact</button>
                        </div>
                    </div>
                </form>
            <?php
            }
            else
            {
                echo "<h5>".$contact['message']."</h5>";
            }
            ?>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>
