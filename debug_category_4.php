<?php
include('admin/config/dbcon.php');

echo "<h2>Debug Category Filter for Category 4</h2>";

// Set the category filter
$category_filter = '4';

// Build the query
$sql = "SELECT p.id, p.name, p.category_id, c.name as category_name
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 0";

// Add category filter
if ($category_filter !== '0' && !empty($category_filter)) {
    $sql .= " AND (p.category_id = '" . mysqli_real_escape_string($conn, $category_filter) . "')";
}

$sql .= " ORDER BY p.name ASC";

echo "<p>SQL Query: " . htmlspecialchars($sql) . "</p>";

// Execute the query
$result = mysqli_query($conn, $sql);

// Display the results
if ($result && mysqli_num_rows($result) > 0) {
    echo "<p>Found " . mysqli_num_rows($result) . " products</p>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Category ID</th><th>Category Name</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['category_id'] . "</td>";
        echo "<td>" . $row['category_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No products found or error: " . mysqli_error($conn) . "</p>";
}

// Check the product directly
echo "<h3>Direct Product Check</h3>";
$direct_sql = "SELECT * FROM products WHERE category_id = '4'";
$direct_result = mysqli_query($conn, $direct_sql);

if ($direct_result && mysqli_num_rows($direct_result) > 0) {
    echo "<p>Found " . mysqli_num_rows($direct_result) . " products directly</p>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Category ID</th></tr>";
    while ($row = mysqli_fetch_assoc($direct_result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['category_id'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No products found directly or error: " . mysqli_error($conn) . "</p>";
}

// Back to product list
echo "<p><a href='product_list.php'>Back to Product List</a></p>";
echo "<p><a href='product_list.php?category=4'>View Category 4 Products</a></p>";
?>
