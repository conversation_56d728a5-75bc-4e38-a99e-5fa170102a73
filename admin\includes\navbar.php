<?php
    require_once(__DIR__ . '/../config/config.php');
?>
<nav class="sb-topnav navbar navbar-expand navbar-dark bg-dark">
    <!-- Navbar Brand-->
    <a class="navbar-brand ps-3" href="index.php">BizBox <br><h6>Business Assistant 2.0</h6></a>
    <!-- Sidebar Toggle-->
    <button class="btn btn-link btn-sm order-1 order-lg-0 me-4 me-lg-0" id="sidebarToggle" href="#!"><i class="fas fa-bars"></i></button>
    <!-- Navbar Search-->
    <form class="d-none d-md-inline-block form-inline ms-auto me-0 me-md-3 my-2 my-md-0">
        <!-- <div class="input-group">
            <input class="form-control" type="text" placeholder="Search for..." aria-label="Search for..." aria-describedby="btnNavbarSearch" />
            <button class="btn btn-primary" id="btnNavbarSearch" type="button"><i class="fas fa-search"></i></button>
        </div> -->
    </form>
    <!-- Navbar-->
    <div class="user-dropdown-container" style="position: relative; margin-left: auto; margin-right: 1rem;">
        <button onclick="toggleUserMenu()" class="user-dropdown-btn" style="background: none; border: none; color: white; cursor: pointer; display: flex; align-items: center; padding: 8px 16px;">
            <i class="fas fa-user fa-fw"></i>
            <span style="margin: 0 8px;"><?= $_SESSION['loggedInUser']['name']; ?></span>
            <i class="fas fa-caret-down"></i>
        </button>
        <div id="userDropdownMenu" style="display: none; position: absolute; right: 0; top: 100%; background-color: white; min-width: 200px; box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); z-index: 1000; border-radius: 4px; margin-top: 5px;">
            <a href="<?= $BASE_URL ?>/admin/modules/settings/settings.php" style="color: #333; padding: 12px 16px; text-decoration: none; display: block; border-bottom: 1px solid #eee;">Settings</a>
            <a href="https://mail.web4u.co.za" style="color: #333; padding: 12px 16px; text-decoration: none; display: block; border-bottom: 1px solid #eee;">Web Mail</a>
            <a href="http://www.web4u.co.za" style="color: #333; padding: 12px 16px; text-decoration: none; display: block; border-bottom: 1px solid #eee;">Web Site</a>
            <div style="height: 1px; background-color: #ccc; margin: 5px 0;"></div>
            <a href="<?= $BASE_URL ?>/admin/help/voice-commands.php" style="color: #333; padding: 12px 16px; text-decoration: none; display: block; border-bottom: 1px solid #eee;"><i class="fas fa-microphone"></i> Voice Commands</a>
            <a href="<?= $BASE_URL ?>/admin/system.php" style="color: #333; padding: 12px 16px; text-decoration: none; display: block; border-bottom: 1px solid #eee;"><i class="fas fa-cogs"></i> System</a>
            <div style="height: 1px; background-color: #ccc; margin: 5px 0;"></div>
            <a href="https://wa.me/797869698" style="color: #333; padding: 12px 16px; text-decoration: none; display: block; border-bottom: 1px solid #eee;">Support</a>
            <div style="height: 1px; background-color: #ccc; margin: 5px 0;"></div>
            <a href="<?= $BASE_URL ?>/admin/logout.php" style="color: #333; padding: 12px 16px; text-decoration: none; display: block;">Logout</a>
        </div>
    </div>
</nav>

<script>
// Simple vanilla JavaScript for dropdown toggle
function toggleUserMenu() {
    var menu = document.getElementById('userDropdownMenu');
    if (menu.style.display === 'none' || menu.style.display === '') {
        menu.style.display = 'block';
    } else {
        menu.style.display = 'none';
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(e) {
    var container = document.querySelector('.user-dropdown-container');
    var menu = document.getElementById('userDropdownMenu');

    if (container && !container.contains(e.target) && menu.style.display === 'block') {
        menu.style.display = 'none';
    }
});

// Prevent the dropdown from closing when clicking inside it
document.getElementById('userDropdownMenu').addEventListener('click', function(e) {
    e.stopPropagation();
});

// Add hover effect to dropdown items
var dropdownItems = document.querySelectorAll('#userDropdownMenu a');
dropdownItems.forEach(function(item) {
    item.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#f8f9fa';
    });
    item.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'transparent';
    });
});
</script>
