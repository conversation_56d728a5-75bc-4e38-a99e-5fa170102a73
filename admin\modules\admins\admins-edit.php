<?php 
include('includes/header.php');
checkUserRole('admin');

$adminId = checkParamId('id');
if(!is_numeric($adminId)){
    redirect('admins.php', 'Invalid ID');
    exit;
}

// Use prepared statement to get admin data
$stmt = mysqli_prepare($conn, "SELECT * FROM admins WHERE id = ?");
mysqli_stmt_bind_param($stmt, "i", $adminId);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$adminData = mysqli_fetch_assoc($result);

if(!$adminData) {
    redirect('admins.php', 'Admin not found');
    exit;
}
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Admin
                <a href="admins.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>
            <form action="code.php" method="POST">
                <input type="hidden" name="adminId" value="<?= htmlspecialchars($adminData['id']) ?>" />
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="name">Name *</label>
                        <input type="text" name="name" id="name" required value="<?= htmlspecialchars($adminData['name']) ?>" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="email">Email *</label>
                        <input type="email" name="email" id="email" required value="<?= htmlspecialchars($adminData['email']) ?>" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="password">Password</label>
                        <input type="password" name="password" id="password" class="form-control" />
                        <small class="text-muted">Leave blank to keep current password</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="phone">Phone Number</label>
                        <input type="number" name="phone" id="phone" value="<?= htmlspecialchars($adminData['phone']) ?>" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="is_ban">Status</label>
                        <br/>
                        <input type="checkbox" name="is_ban" id="is_ban" <?= $adminData['is_ban'] == 1 ? 'checked' : '' ?> style="width:30px;height:30px" />
                        <label for="is_ban">Banned</label>
                    </div>
                    <div class="col-md-6 mb-3 text-end">
                        <button type="submit" name="updateAdmin" class="btn btn-primary">Update Admin</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>
