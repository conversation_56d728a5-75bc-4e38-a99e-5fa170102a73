2025-04-13 14:05:10 - <PERSON><PERSON><PERSON> started
2025-04-13 14:05:10 - Basic product data prepared - Data: Array
(
    [category_id] => 1
    [name] => Test Product 20250413140510
    [description] => This is a test product created on 2025-04-13 14:05:10
    [cost_price] => 100
    [sales_price] => 150
    [quantity] => 10
    [barcode] => TEST2290
)

2025-04-13 14:07:38 - <PERSON>ript started
2025-04-13 14:07:39 - Basic product data prepared - Data: Array
(
    [category_id] => 1
    [name] => Test Product 20250413140739
    [description] => This is a test product created on 2025-04-13 14:07:39
    [cost_price] => 100
    [sales_price] => 150
    [quantity] => 10
    [barcode] => TEST9702
)

2025-04-13 14:07:39 - VAT calculations - Data: Array
(
    [vat_percentage] => 0
    [vatT] => 0
    [price] => 150
)

2025-04-13 14:07:39 - Product data prepared - Data: Array
(
    [category_id] => 1
    [name] => Test Product 20250413140739
    [description] => This is a test product created on 2025-04-13 14:07:39
    [cost_price] => 100
    [sales_price] => 150
    [vat_percentage] => 0
    [vatT] => 0
    [price] => 150
    [quantity] => 10
    [barcode] => TEST9702
    [image] => default.jpg
    [status] => 0
    [created_at] => 2025-04-13 14:07:39
    [featured] => 0
    [product_type_id] => 1
    [can_be_component] => 0
)

2025-04-13 14:07:39 - Inserting product
2025-04-13 14:07:39 - SQL Query - Data: INSERT INTO products (category_id, name, description, cost_price, sales_price, vat_percentage, vatT, price, quantity, barcode, image, status, created_at, featured, product_type_id, can_be_component) VALUES ('1', 'Test Product 20250413140739', 'This is a test product created on 2025-04-13 14:07:39', '100', '150', '0', '0', '150', '10', 'TEST9702', 'default.jpg', '0', '2025-04-13 14:07:39', '0', '1', '0')
2025-04-13 14:07:39 - Product inserted successfully with ID: 22
