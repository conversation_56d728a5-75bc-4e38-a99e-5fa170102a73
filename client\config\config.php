<?php
session_start();
// Define environment
$environment = 'local'; // Change to 'production' for live server

// Include base configuration
require_once(__DIR__ . '/base_config.php');

// Constants for database configuration
if ($environment === 'local') {
    // Local Database Settings
    define('DB_SERVER', "localhost");
    define('DB_USERNAME', "root");
    define('DB_PASSWORD', "");
    define('DB_DATABASE', "apppats");

    // Local Error Reporting
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

} else {
    // Production Database Settings
    define('DB_SERVER', "localhost");
    define('DB_USERNAME', "customap1_webapp");
    define('DB_PASSWORD', "n?r65P5bWxKpNRAp");
    define('DB_DATABASE', "customap1_webapp");

    // Production Error Reporting
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', '/path/to/error.log');
}

try {
    if ($environment === 'production') {
        // Production connection - removed SSL for standard hosting
        $conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_DATABASE);
        if (!$conn) {
            throw new Exception("Connection failed");
        }
    } else {
        // Simple local connection without SSL
        $conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_DATABASE);
        if (!$conn) {
            throw new Exception("Connection failed: " . mysqli_connect_error());
        }

        // Set a higher timeout to prevent 'MySQL server has gone away' errors
        mysqli_query($conn, "SET SESSION wait_timeout=28800");
        mysqli_query($conn, "SET SESSION interactive_timeout=28800");

        // Increase max allowed packet size
        mysqli_query($conn, "SET GLOBAL max_allowed_packet=16777216"); // 16M
    }

    // Common settings for both environments
    mysqli_set_charset($conn, 'utf8mb4');
    mysqli_query($conn, "SET SESSION sql_mode = 'STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'");

    if ($environment === 'production') {
        mysqli_query($conn, "SET session wait_timeout=600");
    }

} catch (Exception $e) {
    if ($environment === 'production') {
        error_log("Database connection failed: " . mysqli_connect_error());
        die("A database error occurred. Please contact support.");
    } else {
        die("Connection Failed: " . mysqli_connect_error());
    }
}
?>
