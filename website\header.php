<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>Custom Applications - Affordable Web Development & Business Solutions | Durban</title>
  <meta name="description" content="Custom Applications offers affordable web development, mobile-responsive websites, and all-in-one business management solutions in Durban. Custom PHP development and business automation.">
  <meta name="keywords" content="web development, affordable websites, business assistant, PHP development, mobile development, Durban web design, business automation, custom web solutions">
  <meta name="author" content="Custom Applications">
  <meta property="og:title" content="Custom Applications - Affordable Web Development & Business Solutions">
  <meta property="og:description" content="Professional web development and business management solutions at affordable prices. Custom websites, mobile apps, and business automation tools.">
  <meta property="og:image" content="assets/img/web4u.webp">
  <meta property="og:url" content="https://customapplication.co.za">
  <meta name="twitter:card" content="summary_large_image">

  <!-- Favicons -->
  <link href="assets/img/letter-w.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

<!-- Fonts -->
<link href="https://fonts.googleapis.com" rel="preconnect">
<link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" integrity="sha512-iBBXm8fW90+nuLcSKlbmrPcLa0OT92xO1BIsZ+ywDWZCvqsWgccV3gFoRBv0z+8dLJgyAHIhR35VZc2oM/gI1w==" crossorigin="anonymous" />

<!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
  <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <!-- Include Speech Functionality -->
  <?php include('includes/speech-include.php'); ?>

  <!-- Custom Animation Styles -->
  <style>
    /* Slide-in animation for tagline */
    .tagline-container {
      overflow: hidden;
    }

    .tagline-text {
      display: inline-block;
      transform: translateX(-100%);
      animation: slideIn 1s forwards;
    }

    @keyframes slideIn {
      to { transform: translateX(0); }
    }

    /* Delayed animations for each part */
    .tagline-part:nth-child(1) {
      animation-delay: 0.2s;
    }

    .tagline-part:nth-child(2) {
      animation-delay: 0.4s;
    }

    .tagline-part:nth-child(3) {
      animation-delay: 0.6s;
    }

    /* Icon hover effect */
    .animated-icon {
      transition: all 0.3s ease;
    }

    .animated-icon:hover {
      color: #ffffff !important;
      transform: scale(1.2);
    }
  </style>

  <!-- Structured Data for SEO -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Custom Applications",
    "url": "https://customapplication.co.za",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://customapplication.co.za/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  }
  </script>

  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Custom Applications",
    "image": "https://customapplication.co.za/assets/img/web4u.webp",
    "@id": "https://customapplication.co.za",
    "url": "https://customapplication.co.za",
    "telephone": "+***********",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "",
      "addressLocality": "Durban",
      "postalCode": "",
      "addressCountry": "ZA"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": -29.8587,
      "longitude": 31.0218
    },
    "openingHoursSpecification": {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday"
      ],
      "opens": "08:00",
      "closes": "17:00"
    },
    "sameAs": [
      "https://www.facebook.com/customapplications",
      "https://www.instagram.com/customapplications"
    ]
  }
  </script>
</head>

<body class="index-page">

  <header id="header" class="header d-flex align-items-center fixed-top">
    <div class="container-fluid container-xl position-relative d-flex align-items-center">

      <a href="index.php" class="logo d-flex flex-column align-items-start me-auto">
 <!-- <img src="assets/img/web4u.webp" alt="">  -->
        <div class="tagline-container" style="color: #ffffff; font-size: 18px; font-weight: 500; margin: 0; padding: 0; letter-spacing: 0.5px; font-style: italic;">
          <span class="tagline-text tagline-part">
            <i class="fas fa-database animated-icon" style="color: #25D366; margin-right: 4px;"></i> Your Data,
          </span>
          <span class="tagline-text tagline-part">
            <i class="far fa-clock animated-icon" style="color: #ff9800; margin: 0 4px;"></i> Anytime,
          </span>
          <span class="tagline-text tagline-part">
            <i class="fas fa-globe animated-icon" style="color: #3498db; margin: 0 4px;"></i> Anywhere
          </span>
        </div>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="index.php" class="active">Home</a></li>
          <li><a href="index.php#about">About</a></li>
          <li class="dropdown"><a href="index.php#services"><span>Services</span> <i class="bi bi-chevron-down dropdown-indicator"></i></a>
            <ul>
              <li><a href="services.php">All Services</a></li>
              <li><a href="services.php#web-development">Web Development</a></li>
              <li><a href="services.php#mobile-development">Mobile Development</a></li>
              <li><a href="services.php#php-development">PHP Development</a></li>
              <li><a href="services.php#business-assistant">Business Assistant</a></li>
              <li class="dropdown"><a href="#"><span>More Services</span> <i class="bi bi-chevron-right dropdown-indicator"></i></a>
                <ul>
                  <li><a href="services.php#seo-services">SEO Services</a></li>
                  <li><a href="services.php#domain-hosting">Domain & Hosting</a></li>
                  <li><a href="services.php#maintenance">Website Maintenance</a></li>
                </ul>
              </li>
            </ul>
          </li>
          <li><a href="index.php#portfolio">Portfolio</a></li>
          <li><a href="index.php#pricing">Pricing</a></li>
          <li><a href="#footer">Contact Us</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list" style="color: #ffffff; font-size: 28px; background-color: rgba(0,0,0,0.3); padding: 5px; border-radius: 4px;"></i>
      </nav>

      <!-- Mobile Tagline (visible only on mobile) -->
      <div class="mobile-tagline d-none">
        <span style="color: #ffffff; font-weight: 500; font-style: italic;">
          <i class="fas fa-database" style="color: #25D366;"></i> Your Data,
          <i class="far fa-clock" style="color: #ff9800;"></i> Anytime,
          <i class="fas fa-globe" style="color: #3498db;"></i> Anywhere
        </span>
      </div>

      <div class="d-flex header-buttons">
        <a class="btn-whatsapp-standard me-2 header-btn" href="https://wa.me/+***********?text=I'm%20interested%20in%20getting%20started%20with%20Custom%20Applications" target="_blank" style="font-size: 16px; padding: 12px 24px; display: inline-flex; align-items: center; font-weight: 500; width: 160px; justify-content: center;"><i class="bi bi-whatsapp me-1" style="font-size: 18px;"></i> Get Started</a>

        <button id="welcomeAudioBtn" class="btn btn-outline-light welcome-btn header-btn" style="background-color: #ff9800; color: white; border: none; padding: 12px 24px; font-size: 16px; display: inline-flex; align-items: center; transition: all 0.3s ease; width: 160px; justify-content: center;">
          <i class="fas fa-headphones me-2"></i> Welcome
        </button>
      </div>

      <style>
        .welcome-btn:hover {
          background-color: #e68a00 !important;
          color: #ffffff !important;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
        }

        /* Mobile Menu Fixes */
        .navmenu ul li a {
          color: #ffffff !important;
        }

        .navmenu ul li ul li a {
          color: #000000 !important;
        }

        @media (max-width: 768px) {
          .navmenu ul {
            background-color: rgba(0, 0, 0, 0.8) !important;
            padding: 10px !important;
            border-radius: 5px !important;
          }

          .navmenu ul li ul {
            background-color: #ffffff !important;
          }

        /* Mobile Responsive Fixes */
          .tagline-container {
            display: none !important; /* Hide desktop tagline on mobile */
          }

          .mobile-tagline {
            display: block !important; /* Show mobile tagline */
            text-align: center;
            width: 100%;
            font-size: 14px;
            margin-bottom: 10px;
            order: -1; /* Place above buttons */
          }

          .container-fluid.container-xl {
            flex-wrap: wrap;
            justify-content: center;
          }

          .header-buttons {
            flex-direction: column;
            width: 100%;
            align-items: center;
          }

          .header-btn {
            font-size: 12px !important;
            padding: 6px 10px !important;
            margin-top: 5px;
            width: 140px !important;
            justify-content: center !important;
          }

          .btn-whatsapp-standard.header-btn {
            margin-right: 0 !important;
            margin-bottom: 5px;
          }

          .btn-whatsapp-standard i, .welcome-btn i {
            font-size: 14px !important;
          }

          .logo {
            margin-right: 0 !important;
            margin-bottom: 10px;
            justify-content: center !important;
            width: 100%;
          }
        }

      </style>

    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const welcomeAudioBtn = document.getElementById('welcomeAudioBtn');
        const welcomeMessage = `Welcome to Custom Applications. We provide innovative business solutions that give you access to your data anytime, anywhere. Our all-in-one business assistant helps you manage your business efficiently with features like invoicing, inventory management, and customer relationship management. Contact us today to transform your business operations.`;

        welcomeAudioBtn.addEventListener('click', function() {
            // Stop any currently playing speech
            if (window.speechSynthesis) {
                window.speechSynthesis.cancel();
            }

            websiteTTS.speak(welcomeMessage, {
                userInitiated: true,
                rate: 0.9,
                volume: 1.0, // Maximum volume
                onStart: () => {
                    welcomeAudioBtn.classList.add('speaking');
                    welcomeAudioBtn.style.backgroundColor = '#e57373';
                    welcomeAudioBtn.innerHTML = '<i class="fas fa-volume-up me-2"></i> Playing...';
                },
                onEnd: () => {
                    welcomeAudioBtn.classList.remove('speaking');
                    welcomeAudioBtn.style.backgroundColor = '#ff9800';
                    welcomeAudioBtn.innerHTML = '<i class="fas fa-headphones me-2"></i> Welcome';
                },
                onError: () => {
                    welcomeAudioBtn.classList.remove('speaking');
                    welcomeAudioBtn.style.backgroundColor = '#ff9800';
                    welcomeAudioBtn.innerHTML = '<i class="fas fa-headphones me-2"></i> Welcome';
                    alert('Sorry, there was an error with the speech system');
                }
            });
        });

        // Check for browser support
        if (!websiteTTS.isSupported()) {
            welcomeAudioBtn.style.display = 'none';
        }
    });
    </script>
  </header>
