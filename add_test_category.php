<?php
include('admin/config/dbcon.php');

echo "<h2>Adding Test Category and Product</h2>";

// Function to add a test category
function addTestCategory($name, $description = "Test category description") {
    global $conn;
    
    // Check if category already exists
    $check_sql = "SELECT id FROM categories WHERE name = ?";
    $check_stmt = mysqli_prepare($conn, $check_sql);
    mysqli_stmt_bind_param($check_stmt, "s", $name);
    mysqli_stmt_execute($check_stmt);
    $result = mysqli_stmt_get_result($check_stmt);
    
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        echo "<p>Category '$name' already exists with ID: " . $row['id'] . ". Skipping creation.</p>";
        return $row['id'];
    }
    
    // Insert new category
    $sql = "INSERT INTO categories (name, description, status) VALUES (?, ?, 0)";
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "ss", $name, $description);
    
    if (mysqli_stmt_execute($stmt)) {
        $category_id = mysqli_insert_id($conn);
        echo "<p>Added category: $name (ID: $category_id)</p>";
        return $category_id;
    } else {
        echo "<p>Error adding category: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

// Function to add a test product
function addTestProduct($name, $category_id, $description = "Test product description") {
    global $conn;
    
    // Check if product already exists
    $check_sql = "SELECT id FROM products WHERE name = ?";
    $check_stmt = mysqli_prepare($conn, $check_sql);
    mysqli_stmt_bind_param($check_stmt, "s", $name);
    mysqli_stmt_execute($check_stmt);
    $result = mysqli_stmt_get_result($check_stmt);
    
    if (mysqli_num_rows($result) > 0) {
        echo "<p>Product '$name' already exists. Skipping.</p>";
        return false;
    }
    
    // Insert new product
    $sql = "INSERT INTO products (name, category_id, description, barcode, cost_price, sales_price, vat_percentage, vatT, price, quantity, image, status, created_at, featured) 
            VALUES (?, ?, ?, '0000', 100.00, 115.00, 15, 17.25, 132.25, 10, 'default.jpg', 0, NOW(), 1)";
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "sss", $name, $category_id, $description);
    
    if (mysqli_stmt_execute($stmt)) {
        $product_id = mysqli_insert_id($conn);
        echo "<p>Added product: $name (ID: $product_id) with category ID: $category_id</p>";
        return true;
    } else {
        echo "<p>Error adding product: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

// Add a new test category
$category_name = "Test Category " . date('YmdHis');
$category_id = addTestCategory($category_name, "This is a test category for testing the category filter");

if ($category_id) {
    // Add a product in the new category
    $product_name = "Product in " . $category_name;
    addTestProduct($product_name, $category_id, "This is a test product in the new test category");
    
    // Show link to view products in this category
    echo "<p><a href='product_list.php?category=$category_id'>View Products in $category_name</a></p>";
}

// Show link to product list
echo "<p><a href='product_list.php'>Go to Product List</a></p>";

// Show all categories
echo "<h3>All Categories</h3>";
$sql = "SELECT id, name, status FROM categories ORDER BY id";
$result = mysqli_query($conn, $sql);

if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Status</th><th>Action</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>" . ($row['status'] == 0 ? 'Active' : 'Inactive') . "</td>";
        echo "<td><a href='product_list.php?category=" . $row['id'] . "'>View Products</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No categories found or error: " . mysqli_error($conn) . "</p>";
}
?>
