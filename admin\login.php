<?php 
include('includes_login/header.php');

if(isset($_SESSION['loggedIn'])){ 
    header("Location: index.php");
    exit();
}
?>

<div class="py-5 bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow rounded-4">
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        Welcome to the administrator login page!
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <div class="p-5">
                        <h4 class="text-dark mb-3">Sign in as Administrator</h4>
                        <form action="login-code.php" method="POST">
                            <div class="mb-3">
                                <label>Email Id</label>
                                <input type="email" name="email" class="form-control" required />
                            </div>
                            <div class="mb-3">
                                <label>Password</label>
                                <input type="password" name="password" class="form-control" required />
                            </div>
                            <div class="mb-3">
                                <button type="submit" name="loginBtn" class="btn btn-primary w-100">Sign In</button>
                            </div>
                        </form>
                        <div class="mt-3 text-center">
                            <a href="../index.php" class="text-muted"><i class="fas fa-arrow-left"></i> Back to Login Options</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include('includes_login/footer.php'); ?>
