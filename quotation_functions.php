<?php
// Include session manager
require_once 'session_manager.php';

/**
 * Add item to quotation
 *
 * @param int $product_id Product ID
 * @param string $name Product name
 * @param float $price Product price
 * @param int $quantity Quantity (default: 1)
 * @param string $image Product image
 * @param int|null $color_id Color ID (optional)
 * @param string $color_name Color name (optional)
 * @param int|null $size_id Size ID (optional)
 * @param string $size_name Size name (optional)
 * @param float $additional_price Additional price for variants (optional)
 * @param array $bulk_pricing Bulk pricing rules (optional)
 * @param string|null $build_id Custom build ID (optional)
 * @param string $sku SKU for the variant (optional)
 * @return bool Success status
 */
function addToQuotation($product_id, $name, $price, $quantity = 1, $image = '', $color_id = null, $color_name = '', $size_id = null, $size_name = '', $additional_price = 0, $bulk_pricing = [], $build_id = null, $sku = '') {
    // Create a unique key for the item based on product ID, color, size, and build ID
    $item_key = $product_id;
    if ($color_id) $item_key .= '_c' . $color_id;
    if ($size_id) $item_key .= '_s' . $size_id;
    if ($build_id) $item_key .= '_b' . $build_id;

    // Check if product with the same variants already exists in quotation
    if (isset($_SESSION['quotation_items'][$item_key])) {
        // Update quantity
        $_SESSION['quotation_items'][$item_key]['quantity'] += $quantity;
    } else {
        // Add new item
        $_SESSION['quotation_items'][$item_key] = [
            'id' => $product_id,
            'name' => $name,
            'price' => $price,
            'quantity' => $quantity,
            'image' => $image,
            'color_id' => $color_id,
            'color_name' => $color_name,
            'size_id' => $size_id,
            'size_name' => $size_name,
            'additional_price' => $additional_price,
            'bulk_pricing' => $bulk_pricing,
            'build_id' => $build_id,
            'sku' => $sku
        ];
    }

    return true;
}

/**
 * Add a component to a custom build in the quotation
 *
 * @param int $product_id Product ID
 * @param string $name Product name
 * @param float $price Product price
 * @param int $quantity Quantity (default: 1)
 * @param string $image Product image
 * @param int|null $color_id Color ID (optional)
 * @param string $color_name Color name (optional)
 * @param int|null $size_id Size ID (optional)
 * @param string $size_name Size name (optional)
 * @param float $additional_price Additional price for variants (optional)
 * @param array $bulk_pricing Bulk pricing rules (optional)
 * @param string|null $build_id Custom build ID (required)
 * @return bool Success status
 */
function addComponentToQuotation($product_id, $name, $price, $quantity = 1, $image = '', $color_id = null, $color_name = '', $size_id = null, $size_name = '', $additional_price = 0, $bulk_pricing = [], $build_id = null) {
    // Create a unique key for the item based on product ID, color, size, and build ID
    $item_key = $product_id;
    if ($color_id) $item_key .= '_c' . $color_id;
    if ($size_id) $item_key .= '_s' . $size_id;
    if ($build_id) $item_key .= '_b' . $build_id;

    // Check if product with the same variants already exists in quotation
    if (isset($_SESSION['quotation_items'][$item_key])) {
        // Update quantity
        $_SESSION['quotation_items'][$item_key]['quantity'] += $quantity;
    } else {
        // Add new item
        $_SESSION['quotation_items'][$item_key] = [
            'id' => $product_id,
            'name' => $name,
            'price' => $price,
            'quantity' => $quantity,
            'image' => $image,
            'color_id' => $color_id,
            'color_name' => $color_name,
            'size_id' => $size_id,
            'size_name' => $size_name,
            'additional_price' => $additional_price,
            'bulk_pricing' => $bulk_pricing,
            'build_id' => $build_id,
            'is_component' => 1
        ];
    }

    return true;
}

/**
 * Remove item from quotation
 *
 * @param string $item_key Item key (product_id with optional color and size suffixes)
 * @return bool Success status
 */
function removeFromQuotation($item_key) {
    // Debug information
    error_log("Removing item with key: '$item_key'");
    $available_keys = array_keys($_SESSION['quotation_items'] ?? []);
    error_log("Available keys: '" . implode("', '", $available_keys) . "'");
    error_log("Session data: " . print_r($_SESSION, true));

    // Check if the item exists in the quotation
    if (isset($_SESSION['quotation_items'][$item_key])) {
        unset($_SESSION['quotation_items'][$item_key]);
        error_log("Item removed successfully");
        return true;
    }

    // Try to find the item by product ID (for backward compatibility)
    if (is_numeric($item_key)) {
        foreach ($_SESSION['quotation_items'] as $key => $item) {
            if ($item['id'] == $item_key) {
                unset($_SESSION['quotation_items'][$key]);
                error_log("Item removed by product ID");
                return true;
            }
        }
    }

    error_log("Item not found");
    return false;
}

/**
 * Update quotation item quantity
 *
 * @param string $item_key Item key (product_id with optional color and size suffixes)
 * @param int $quantity New quantity
 * @return bool Success status
 */
function updateQuotationQuantity($item_key, $quantity) {
    // Debug information
    error_log("Updating quantity for item with key: $item_key");
    error_log("Available keys: " . implode(', ', array_keys($_SESSION['quotation_items'] ?? [])));

    // Check if the item exists in the quotation
    if (isset($_SESSION['quotation_items'][$item_key])) {
        $_SESSION['quotation_items'][$item_key]['quantity'] = $quantity;
        error_log("Quantity updated successfully");
        return true;
    }

    // Try to find the item by product ID (for backward compatibility)
    if (is_numeric($item_key)) {
        foreach ($_SESSION['quotation_items'] as $key => $item) {
            if ($item['id'] == $item_key) {
                $_SESSION['quotation_items'][$key]['quantity'] = $quantity;
                error_log("Quantity updated by product ID");
                return true;
            }
        }
    }

    error_log("Item not found for quantity update");
    return false;
}

/**
 * Get all quotation items
 *
 * @return array Quotation items
 */
function getQuotationItems() {
    return $_SESSION['quotation_items'] ?? [];
}

/**
 * Get quotation total
 *
 * @return float Total amount
 */
function getQuotationTotal() {
    $total = 0;
    foreach ($_SESSION['quotation_items'] as $item) {
        // Get the base price
        $base_price = $item['price'];

        // Check if there's bulk pricing available
        $unit_price = $base_price;
        if (!empty($item['bulk_pricing'])) {
            $quantity = $item['quantity'];
            foreach ($item['bulk_pricing'] as $bulk) {
                if ($quantity >= $bulk['min_quantity'] &&
                    ($quantity <= $bulk['max_quantity'] || $bulk['max_quantity'] == 0)) {
                    $unit_price = $bulk['price'];
                    break;
                }
            }
        }

        // Add additional price for variants
        $color_additional_price = $item['color_additional_price'] ?? 0;
        $size_additional_price = $item['size_additional_price'] ?? 0;
        $total_additional_price = $color_additional_price + $size_additional_price;

        // Calculate the total for this item
        $item_total = ($unit_price + $total_additional_price) * $item['quantity'];
        $total += $item_total;
    }
    return $total;
}

/**
 * Get quotation item count
 *
 * @return int Item count
 */
function getQuotationItemCount() {
    return count($_SESSION['quotation_items'] ?? []);
}

/**
 * Clear quotation
 *
 * @return bool Success status
 */
function clearQuotation() {
    $_SESSION['quotation_items'] = [];
    return true;
}

/**
 * Debug session information
 *
 * @return array Session information
 */
function debugQuotationSession() {
    return [
        'session_id' => session_id(),
        'session_status' => session_status(),
        'session_name' => session_name(),
        'session_cookie_params' => session_get_cookie_params(),
        'quotation_items' => isset($_SESSION['quotation_items']) ? count($_SESSION['quotation_items']) : 0
    ];
}
?>
