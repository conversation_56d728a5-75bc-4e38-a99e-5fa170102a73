<?php
// Include database connection
require_once('admin/config/dbcon.php');

// Get the current description
$query = "SELECT description FROM products WHERE id = 10";
$result = mysqli_query($conn, $query);
$row = mysqli_fetch_assoc($result);
$current_description = $row['description'];

echo "<h2>Current Description in Database:</h2>";
echo "<pre>" . htmlspecialchars($current_description) . "</pre>";

// Display how it should render
echo "<h2>How it should render:</h2>";
echo "<div style='padding: 15px; border: 1px solid #ddd; margin-bottom: 20px;'>";
echo $current_description;
echo "</div>";

// Check if we need to update
if (isset($_GET['update'])) {
    // The description is already in the correct format, but let's make sure it's not escaped
    $update_query = "UPDATE products SET description = ? WHERE id = 10";
    $stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($stmt, "s", $current_description);
    $success = mysqli_stmt_execute($stmt);
    
    if ($success) {
        echo "<div style='padding: 15px; background-color: #d4edda; color: #155724; margin-bottom: 20px;'>";
        echo "<h3>✅ Database Updated Successfully!</h3>";
        echo "<p>The description has been updated to ensure proper display.</p>";
        echo "</div>";
    } else {
        echo "<div style='padding: 15px; background-color: #f8d7da; color: #721c24; margin-bottom: 20px;'>";
        echo "<h3>❌ Error Updating Database</h3>";
        echo "<p>Error: " . mysqli_error($conn) . "</p>";
        echo "</div>";
    }
}

// Create a simple edit form
echo "<h2>Edit Description:</h2>";
echo "<form method='post' action='update_description.php'>";
echo "<input type='hidden' name='product_id' value='10'>";
echo "<textarea name='description' rows='5' style='width: 100%; padding: 10px; margin-bottom: 10px;'>" . htmlspecialchars($current_description) . "</textarea>";
echo "<button type='submit' style='padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;'>Update Description</button>";
echo "</form>";

// Link to update the database
echo "<div style='margin-top: 20px;'>";
echo "<a href='?update=1' style='padding: 10px 15px; background-color: #28a745; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin-right: 10px;'>Fix Database Entry</a>";
echo "<a href='admin/modules/products/products-edit.php?id=10' style='padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block;'>Go to Product Edit Page</a>";
echo "</div>";
?>

<?php
// Create the update script
$update_script = '<?php
// Include database connection
require_once("admin/config/dbcon.php");

// Get the form data
$product_id = isset($_POST["product_id"]) ? intval($_POST["product_id"]) : 0;
$description = isset($_POST["description"]) ? $_POST["description"] : "";

if ($product_id > 0 && !empty($description)) {
    // Update the database
    $update_query = "UPDATE products SET description = ? WHERE id = ?";
    $stmt = mysqli_prepare($conn, $update_query);
    mysqli_stmt_bind_param($stmt, "si", $description, $product_id);
    $success = mysqli_stmt_execute($stmt);
    
    if ($success) {
        echo "<div style=\"padding: 15px; background-color: #d4edda; color: #155724; margin-bottom: 20px;\">";
        echo "<h3>✅ Description Updated Successfully!</h3>";
        echo "<p>The product description has been updated.</p>";
        echo "</div>";
    } else {
        echo "<div style=\"padding: 15px; background-color: #f8d7da; color: #721c24; margin-bottom: 20px;\">";
        echo "<h3>❌ Error Updating Description</h3>";
        echo "<p>Error: " . mysqli_error($conn) . "</p>";
        echo "</div>";
    }
} else {
    echo "<div style=\"padding: 15px; background-color: #f8d7da; color: #721c24; margin-bottom: 20px;\">";
    echo "<h3>❌ Invalid Input</h3>";
    echo "<p>Please provide a valid product ID and description.</p>";
    echo "</div>";
}

// Link to go back
echo "<div style=\"margin-top: 20px;\">";
echo "<a href=\"fix_product_10_display.php\" style=\"padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block;\">Go Back</a>";
echo "</div>";
?>';

// Save the update script
file_put_contents('update_description.php', $update_script);
?>
