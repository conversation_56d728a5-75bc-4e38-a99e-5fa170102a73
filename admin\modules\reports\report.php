<?php include('includes/header.php'); 
ini_set('display_errors',0); ?>


<!-- grid export -->
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/3.0.1/css/buttons.dataTables.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/3.0.1/css/buttons.dataTables.css">
<!-- grid export -->
<div class="container">
  <h1>Order Report</h1>
<form action="" method="post">
<div class="form-group row">
  <div class="col-sm-4">
    <label for="from_date">From Date:</label>
    <input type="date" id="from_date" name="from_date" class="form-control">
  </div>
  <div class="col-sm-4">
    <label for="to_date">To Date:</label>
    <input type="date" id="to_date" name="to_date" class="form-control">
  </div>
  <div class="col-sm-4">
    <label for="tracking_no">Tracking Number:</label>
    <input type="text" id="tracking_no" name="tracking_no" class="form-control">
  </div>
<div class="form-group row">
  <div class="col-sm-6">
    <label for="payment_status">Payment Status:</label>
    <select name="payment_status" class="form-control">
    <option value="">Select Option</option>
      <option value="Not Paid">Not Paid</option>
      <option value="Paid">Paid</option>
      <option value="Refunded">Refunded</option>
    </select>
  </div>
  <div class="col-sm-6">
    <label for="order_status">Order Status:</label>
    <select name="order_status" class="form-control">
    <option value="">Select Option</option>
      <option value="Pending">Pending</option>
      <option value="In Progress">In Progress</option>
      <option value="Shipped">Shipped</option>
      <option value="Delivered">Delivered</option>
      <option value="Cancelled">Cancelled</option>
    </select>
</div>
</div>
<div style="margin-bottom: 20px;"></div>
<div class="row">
  <div class="col-sm-6">
    <button type="submit" class="btn btn-primary" name="generate_report">Generate Report</button>
  </div>
  <div class="col-sm-6">
    <button type="reset" class="btn btn-secondary" onclick="resetTable()">Reset</button>
  </div>
</div>
</form>
<div style="margin-bottom: 20px;"></div>
<hr>
<?php if ($_SERVER["REQUEST_METHOD"] == "POST") { ?>
  <h2>Report Results</h2>
  <table class="table table-striped" id="report-table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Date</th>
        <th>Name</th>
        <th>Phone</th>
        <th>Customer ID</th>
        <th>Tracking No</th>
        <th>Total Amount</th>
        <th>Order Status</th>
        <th>Payment Mode</th>
        <th>Payment Status</th>
      </tr>
    </thead>
    <tbody id="table-body">
      <?php
      // Sanitize and validate inputs
      $from_date = validate($_POST['from_date']);
      $to_date = validate($_POST['to_date']);
      $tracking_no = validate($_POST['tracking_no']);
      $payment_status = validate($_POST['payment_status']);
      $order_status = validate($_POST['order_status']);

      // Build query using prepared statements
      $conditions = [];
      $params = [];
      $types = "";

      if (!empty($from_date) && !empty($to_date)) {
        $conditions[] = "date BETWEEN ? AND ?";
        $params[] = $from_date;
        $params[] = $to_date;
        $types .= "ss";
      }

      if (!empty($tracking_no)) {
        $conditions[] = "tracking_no LIKE ?";
        $params[] = "%$tracking_no%";
        $types .= "s";
      }

      if (!empty($payment_status)) {
        $conditions[] = "payment_status = ?";
        $params[] = $payment_status;
        $types .= "s";
      }

      if (!empty($order_status)) {
        $conditions[] = "order_status = ?";
        $params[] = $order_status;
        $types .= "s";
      }

      $sql = "SELECT * FROM orders";
      if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
      }

      // Prepare and execute the main query
      $stmt = mysqli_prepare($conn, $sql);
      if (!empty($params)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
      }
      mysqli_stmt_execute($stmt);
      $result = mysqli_stmt_get_result($stmt);

      // Display results
      while ($row = mysqli_fetch_assoc($result)) {
        $tracking_no = $row['tracking_no'];
        
        // Get total amount using prepared statement
        $stmt2 = mysqli_prepare($conn, "SELECT SUM(price) AS total_amount FROM order_items WHERE tracking_no = ?");
        mysqli_stmt_bind_param($stmt2, "s", $tracking_no);
        mysqli_stmt_execute($stmt2);
        $result2 = mysqli_stmt_get_result($stmt2);
        $row2 = mysqli_fetch_assoc($result2);
        $total_amount = $row2['total_amount'] ?? 0;

        // Output the row data safely using htmlspecialchars
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['date']) . "</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['phone']) . "</td>";
        echo "<td>" . htmlspecialchars($row['customer_id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['tracking_no']) . "</td>";
        echo "<td>" . htmlspecialchars($total_amount) . "</td>";
        echo "<td>" . htmlspecialchars($row['order_status']) . "</td>";
        echo "<td>" . htmlspecialchars($row['payment_mode']) . "</td>";
        echo "<td>" . htmlspecialchars($row['payment_status']) . "</td>";
        echo "</tr>";
      }
      ?>
    </tbody>
  </table>
<?php } ?>
<!-- grid export -->
<script src="https://cdn.datatables.net/buttons/3.0.1/js/dataTables.buttons.js"></script>
<script src="https://cdn.datatables.net/buttons/3.0.1/js/buttons.dataTables.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/3.0.1/js/buttons.html5.min.js"></script>
<script>
$.extend(true, $.fn.dataTable.defaults, {
  dom: 'Bfrtip',
  buttons: [
    'copyHtml5',
    'excelHtml5',
    'csvHtml5',
    {
      extend: 'pdfHtml5',
      orientation: 'landscape',
      pageSize: 'A3'
    }
  ]
});


new DataTable('#report-table', {
  layout: {
    topStart: {
      buttons: ['copyHtml5', 'excelHtml5', 'csvHtml5', 'pdfHtml5']
    }
  }
});
</script>

<script>
function resetTable() {
  document.getElementById("table-body").innerHTML = "";
}
</script>
<?php include('includes/footer.php'); ?>
