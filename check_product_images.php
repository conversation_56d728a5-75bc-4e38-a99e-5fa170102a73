<?php
require 'admin/config/function.php';

echo "<h2>Product Images Check</h2>";

// Get all products with their images
$products_query = "SELECT id, name, image, status FROM products WHERE status = 0 ORDER BY id DESC LIMIT 10";
$result = mysqli_query($conn, $products_query);

if($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Image Filename</th><th>File Exists</th><th>Image Preview</th></tr>";
    
    while($product = mysqli_fetch_assoc($result)) {
        $image_path = "uploads/products/" . $product['image'];
        $file_exists = file_exists($image_path) ? "✓ Yes" : "✗ No";
        $file_exists_color = file_exists($image_path) ? "green" : "red";
        
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td>" . htmlspecialchars($product['image']) . "</td>";
        echo "<td style='color: $file_exists_color;'>" . $file_exists . "</td>";
        echo "<td>";
        
        if(file_exists($image_path) && !empty($product['image'])) {
            echo "<img src='$image_path' style='width: 100px; height: 100px; object-fit: cover;' alt='Product Image'>";
        } else {
            echo "<span style='color: red;'>No image or file not found</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No products found.</p>";
}

// Check uploads directory permissions
echo "<h3>Directory Information</h3>";
echo "<p><strong>uploads/products directory exists:</strong> " . (is_dir('uploads/products') ? "✓ Yes" : "✗ No") . "</p>";
echo "<p><strong>uploads/products is readable:</strong> " . (is_readable('uploads/products') ? "✓ Yes" : "✗ No") . "</p>";
echo "<p><strong>uploads/products is writable:</strong> " . (is_writable('uploads/products') ? "✓ Yes" : "✗ No") . "</p>";

// List files in uploads/products
echo "<h3>Files in uploads/products (last 10):</h3>";
$files = glob('uploads/products/*');
$files = array_slice($files, -10); // Get last 10 files

if($files) {
    echo "<ul>";
    foreach($files as $file) {
        $filename = basename($file);
        $filesize = filesize($file);
        echo "<li>$filename (" . number_format($filesize) . " bytes)</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No files found in uploads/products directory.</p>";
}

// Test image display
echo "<h3>Test Image Display</h3>";
$test_files = array_slice($files, 0, 3); // Test first 3 files
foreach($test_files as $file) {
    $filename = basename($file);
    echo "<div style='margin: 10px; display: inline-block;'>";
    echo "<p>$filename:</p>";
    echo "<img src='uploads/products/$filename' style='width: 150px; height: 150px; object-fit: cover; border: 1px solid #ccc;' alt='Test Image'>";
    echo "</div>";
}
?>
