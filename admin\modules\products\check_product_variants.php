<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';
require '../../config/dbcon.php';

// Get product ID from URL
$product_id = isset($_GET['id']) ? $_GET['id'] : 0;

if(!$product_id) {
    echo "<p>No product ID provided. Please add ?id=XX to the URL.</p>";
    exit;
}

// Get product data
$product = getById('products', $product_id);

echo "<h2>Product: " . htmlspecialchars($product['data']['name']) . " (ID: $product_id)</h2>";

// Get product variants
$sql = "SELECT pv.*, pc.name as color_name, pc.color_code, ps.name as size_name 
        FROM product_variants pv
        LEFT JOIN product_colors pc ON pv.color_id = pc.id
        LEFT JOIN product_sizes ps ON pv.size_id = ps.id
        WHERE pv.product_id = $product_id";
$result = mysqli_query($conn, $sql);

if($result && mysqli_num_rows($result) > 0) {
    echo "<h3>Product Variants</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Color</th><th>Size</th><th>Additional Price</th><th>Quantity</th><th>Status</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>";
        if($row['color_id']) {
            echo "<span style='display: inline-block; width: 20px; height: 20px; background-color: " . $row['color_code'] . "; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;'></span>";
            echo $row['color_name'];
        } else {
            echo "N/A";
        }
        echo "</td>";
        echo "<td>" . ($row['size_id'] ? $row['size_name'] : 'N/A') . "</td>";
        echo "<td>R " . number_format($row['additional_price'], 2) . "</td>";
        echo "<td>" . $row['quantity'] . "</td>";
        echo "<td>" . ($row['status'] == 0 ? 'Visible' : 'Hidden') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No variants found for this product.</p>";
    echo "<p>SQL Error: " . mysqli_error($conn) . "</p>";
}

// Check if the product has the enable_variants flag
echo "<h3>Enable Variants Flag</h3>";
$sql = "SHOW COLUMNS FROM products LIKE 'enable_variants'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>The 'enable_variants' column exists in the products table.</p>";
    
    // Check if the product has this flag set
    $sql = "SELECT enable_variants FROM products WHERE id = $product_id";
    $result = mysqli_query($conn, $sql);
    
    if($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        echo "<p>Enable variants flag for this product: " . ($row['enable_variants'] ? 'Enabled' : 'Disabled') . "</p>";
    } else {
        echo "<p>Could not retrieve enable_variants flag for this product.</p>";
    }
} else {
    echo "<p>The 'enable_variants' column does not exist in the products table.</p>";
    
    // Add the column if it doesn't exist
    $sql = "ALTER TABLE products ADD COLUMN enable_variants TINYINT NOT NULL DEFAULT 0";
    if(mysqli_query($conn, $sql)) {
        echo "<p>Added 'enable_variants' column to the products table.</p>";
        
        // Set the flag for this product if it has variants
        $sql = "SELECT COUNT(*) as variant_count FROM product_variants WHERE product_id = $product_id";
        $result = mysqli_query($conn, $sql);
        $row = mysqli_fetch_assoc($result);
        
        if($row['variant_count'] > 0) {
            $sql = "UPDATE products SET enable_variants = 1 WHERE id = $product_id";
            if(mysqli_query($conn, $sql)) {
                echo "<p>Set enable_variants = 1 for this product because it has variants.</p>";
            }
        }
    } else {
        echo "<p>Failed to add 'enable_variants' column: " . mysqli_error($conn) . "</p>";
    }
}

// Fix the color_code/hex_code issue
echo "<h3>Color Code Field Check</h3>";
$sql = "SHOW COLUMNS FROM product_colors LIKE 'hex_code'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>The 'hex_code' column exists in the product_colors table.</p>";
    
    // Check if there's also a color_code column
    $sql = "SHOW COLUMNS FROM product_colors LIKE 'color_code'";
    $result = mysqli_query($conn, $sql);
    
    if(mysqli_num_rows($result) > 0) {
        echo "<p>The 'color_code' column also exists in the product_colors table.</p>";
        
        // Update the products-edit.php file to use the correct column
        $file_path = __DIR__ . '/products-edit.php';
        $file_content = file_get_contents($file_path);
        
        if(strpos($file_content, 'background-color: <?= $color[\'hex_code\'] ?>') !== false) {
            echo "<p>Found 'hex_code' in the products-edit.php file, but the table uses 'color_code'.</p>";
            
            // Create a backup of the file
            $backup_path = __DIR__ . '/products-edit.php.color_code.bak';
            file_put_contents($backup_path, $file_content);
            echo "<p>Created a backup of the file at: " . $backup_path . "</p>";
            
            // Update the file to use color_code instead of hex_code
            $updated_content = str_replace('background-color: <?= $color[\'hex_code\'] ?>', 'background-color: <?= $color[\'color_code\'] ?>', $file_content);
            
            // Save the updated file
            if(file_put_contents($file_path, $updated_content)) {
                echo "<p>Successfully updated the products-edit.php file to use 'color_code' instead of 'hex_code'.</p>";
            } else {
                echo "<p>Failed to update the file.</p>";
            }
        } else if(strpos($file_content, 'background-color: <?= $color[\'color_code\'] ?>') !== false) {
            echo "<p>The products-edit.php file is already using 'color_code'.</p>";
        } else {
            echo "<p>Could not find the color code reference in the products-edit.php file.</p>";
        }
    } else {
        echo "<p>Only the 'hex_code' column exists in the product_colors table.</p>";
    }
} else {
    echo "<p>The 'hex_code' column does not exist in the product_colors table.</p>";
    
    // Check if there's a color_code column
    $sql = "SHOW COLUMNS FROM product_colors LIKE 'color_code'";
    $result = mysqli_query($conn, $sql);
    
    if(mysqli_num_rows($result) > 0) {
        echo "<p>The 'color_code' column exists in the product_colors table.</p>";
        
        // Update the products-edit.php file to use the correct column
        $file_path = __DIR__ . '/products-edit.php';
        $file_content = file_get_contents($file_path);
        
        if(strpos($file_content, 'background-color: <?= $color[\'hex_code\'] ?>') !== false) {
            echo "<p>Found 'hex_code' in the products-edit.php file, but the table uses 'color_code'.</p>";
            
            // Create a backup of the file
            $backup_path = __DIR__ . '/products-edit.php.color_code.bak';
            file_put_contents($backup_path, $file_content);
            echo "<p>Created a backup of the file at: " . $backup_path . "</p>";
            
            // Update the file to use color_code instead of hex_code
            $updated_content = str_replace('background-color: <?= $color[\'hex_code\'] ?>', 'background-color: <?= $color[\'color_code\'] ?>', $file_content);
            
            // Save the updated file
            if(file_put_contents($file_path, $updated_content)) {
                echo "<p>Successfully updated the products-edit.php file to use 'color_code' instead of 'hex_code'.</p>";
            } else {
                echo "<p>Failed to update the file.</p>";
            }
        }
    } else {
        echo "<p>Neither 'hex_code' nor 'color_code' column exists in the product_colors table.</p>";
    }
}
?>
