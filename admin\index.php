<?php
include('includes/header.php');

// Allow both admin and user to access dashboard
checkUserRoles(['admin', 'user']);

// Get total customers (active only)
$totalCustomersResult = mysqli_query($conn, "SELECT COUNT(*) as total FROM customers WHERE status = 'Active'");
$totalCustomers = mysqli_fetch_assoc($totalCustomersResult)['total'] ?? 0;

// Get total products (active only)
$totalProductsResult = mysqli_query($conn, "SELECT COUNT(*) as total FROM products WHERE status = 'Active'");
$totalProducts = mysqli_fetch_assoc($totalProductsResult)['total'] ?? 0;

// Get unread call back requests
$unreadCallBackQuery = "SELECT COUNT(*) as total FROM request_call_back WHERE read_status = 0";
$unreadCallBackResult = mysqli_query($conn, $unreadCallBackQuery);
$unreadCallBacks = 0;
if ($unreadCallBackResult) {
    $unreadCallBacks = mysqli_fetch_assoc($unreadCallBackResult)['total'];
}

// Get unread service inquiries
$unreadServiceInfoQuery = "SELECT COUNT(*) as total FROM services_more_information WHERE read_status = 0";
$unreadServiceInfoResult = mysqli_query($conn, $unreadServiceInfoQuery);
$unreadServiceInfo = 0;
if ($unreadServiceInfoResult) {
    $unreadServiceInfo = mysqli_fetch_assoc($unreadServiceInfoResult)['total'];
}

// Get unread product inquiries
$unreadProductInfoQuery = "SELECT COUNT(*) as total FROM product_more_information WHERE read_status = 0";
$unreadProductInfoResult = mysqli_query($conn, $unreadProductInfoQuery);
$unreadProductInfo = 0;
if ($unreadProductInfoResult) {
    $unreadProductInfo = mysqli_fetch_assoc($unreadProductInfoResult)['total'];
}

// Get order statistics for the last 30 days
$orderQuery = "SELECT
    COUNT(DISTINCT CASE WHEN EXISTS (
        SELECT 1 FROM order_items oi WHERE oi.tracking_no = o.tracking_no
    ) THEN o.id END) as valid_orders,
    COUNT(DISTINCT CASE WHEN EXISTS (
        SELECT 1 FROM order_items oi WHERE oi.tracking_no = o.tracking_no
    ) AND o.payment_status = 'Paid' AND o.order_status != 'Cancelled'
    THEN o.id END) as paid_orders,
    COALESCE(SUM(CASE WHEN payment_status = 'Paid' AND order_status != 'Cancelled'
        AND EXISTS (SELECT 1 FROM order_items oi WHERE oi.tracking_no = o.tracking_no)
        THEN total_amount ELSE 0 END), 0) as total_revenue,
    COALESCE(SUM(CASE WHEN payment_status = 'Not Paid' AND order_status != 'Cancelled'
        AND EXISTS (SELECT 1 FROM order_items oi WHERE oi.tracking_no = o.tracking_no)
        THEN total_amount ELSE 0 END), 0) as unpaid_amount
    FROM orders o
    WHERE o.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";

// Function to get unpaid orders
function get_unpaid_orders() {
    global $conn;
    $query = "SELECT o.*, c.name as customer_name
              FROM orders o
              LEFT JOIN customers c ON o.customer_id = c.id
              WHERE o.payment_status = 'Not Paid'
              AND EXISTS (SELECT 1 FROM order_items oi WHERE oi.tracking_no = o.tracking_no)
              AND o.order_status != 'Cancelled'
              AND o.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
              ORDER BY o.date DESC";

    $result = mysqli_query($conn, $query);

    if($result) {
        return mysqli_fetch_all($result, MYSQLI_ASSOC);
    }
    return false;
}

$result = mysqli_query($conn, $orderQuery);
$orderStats = mysqli_fetch_assoc($result);

$validOrders = intval($orderStats['valid_orders'] ?? 0);
$paidOrders = intval($orderStats['paid_orders'] ?? 0);
$unpaidOrders = $validOrders - $paidOrders;
$totalRevenue = floatval($orderStats['total_revenue'] ?? 0);
$unpaidAmount = floatval($orderStats['unpaid_amount'] ?? 0);
?>

<?php
function formatNumberForSpeech($number) {
    return floor($number);
}

function formatCentsForSpeech($number) {
    $cents = round(($number - floor($number)) * 100);
    return str_pad($cents, 2, '0', STR_PAD_LEFT);
}
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0 d-flex align-items-center justify-content-between">
                <div>
                    BizBox Business Assistant 2.0
                    <?php include('includes/speech-include.php'); ?>
                    <!-- Add help button -->
                    <button class="btn btn-success speech-btn ms-2" type="button" id="helpButton" title="Listen to Dashboard Help">
                        <i class="fas fa-question-circle"></i>
                    </button>
                </div>
                <!-- Notifications Dropdown -->
                <div class="custom-dropdown me-2" style="position: relative; display: inline-block;">
                    <button class="btn position-relative" type="button" id="notificationsDropdownBtn" onclick="toggleNotificationsMenu()" style="background-color: #e67e22; color: white;">
                        <i class="fas fa-bell"></i> Notifications <i class="fas fa-caret-down ms-1"></i>
                        <?php
                        // Make sure all variables are defined
                        $unreadQuotations = $unreadQuotations ?? 0;
                        $viewedQuotations = $viewedQuotations ?? 0;
                        $totalUnread = $unreadCallBacks + $unreadServiceInfo + $unreadProductInfo + $unreadQuotations;
                        if($totalUnread > 0):
                        ?>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            <?= $totalUnread ?>
                            <span class="visually-hidden">unread notifications</span>
                        </span>
                        <?php endif; ?>
                    </button>
                    <div id="notificationsDropdownMenu" class="custom-dropdown-menu dropdown-menu dropdown-menu-end" style="display: none; position: absolute; right: 0; top: 100%; background-color: white; min-width: 250px; box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); z-index: 1000; border-radius: 4px; margin-top: 5px;">
                        <li><h6 class="dropdown-header">Unread Notifications</h6></li>

                        <!-- Call Back Requests -->
                        <li><a class="dropdown-item d-flex justify-content-between align-items-center" href="modules/call_back/call_back.php">
                            <span><i class="fas fa-phone-volume text-primary"></i> Call Back Requests</span>
                            <?php if($unreadCallBacks > 0): ?>
                                <span class="badge bg-danger rounded-pill"><?= $unreadCallBacks ?></span>
                            <?php endif; ?>
                        </a></li>

                        <!-- Service Inquiries -->
                        <li><a class="dropdown-item d-flex justify-content-between align-items-center" href="modules/service_info/service_info.php">
                            <span><i class="fas fa-cogs text-success"></i> Service Inquiries</span>
                            <?php if($unreadServiceInfo > 0): ?>
                                <span class="badge bg-danger rounded-pill"><?= $unreadServiceInfo ?></span>
                            <?php endif; ?>
                        </a></li>

                        <!-- Product Inquiries -->
                        <li><a class="dropdown-item d-flex justify-content-between align-items-center" href="modules/product_info/product_info.php">
                            <span><i class="fas fa-box text-info"></i> Product Inquiries</span>
                            <?php if($unreadProductInfo > 0): ?>
                                <span class="badge bg-danger rounded-pill"><?= $unreadProductInfo ?></span>
                            <?php endif; ?>
                        </a></li>

                        <!-- Separator -->
                        <li><hr class="dropdown-divider"></li>

                        <!-- Quotation Requests -->
                        <li><a class="dropdown-item d-flex justify-content-between align-items-center" href="modules/quotations/quotations.php">
                            <span><i class="fas fa-quote-left text-warning"></i> Quotation Requests</span>
                            <div>
                                <?php if($unreadQuotations > 0): ?>
                                    <span class="badge bg-danger rounded-pill" title="Unread"><?= $unreadQuotations ?></span>
                                <?php endif; ?>
                                <?php if($viewedQuotations > 0): ?>
                                    <span class="badge bg-secondary rounded-pill ms-1" title="Viewed"><?= $viewedQuotations ?></span>
                                <?php endif; ?>
                            </div>
                        </a></li>

                        <?php if($totalUnread == 0): ?>
                        <li><div class="dropdown-item text-muted">No unread notifications</div></li>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Access Dropdown -->
                <div class="custom-dropdown" style="position: relative; display: inline-block;">
                    <button class="btn btn-danger" type="button" id="quickAccessDropdownBtn" onclick="toggleQuickAccessMenu()">
                        <i class="fas fa-bolt"></i> Quick Access <i class="fas fa-caret-down ms-1"></i>
                    </button>
                    <div id="quickAccessDropdownMenu" class="custom-dropdown-menu dropdown-menu dropdown-menu-end" style="display: none; position: absolute; right: 0; top: 100%; background-color: white; min-width: 200px; box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2); z-index: 1000; border-radius: 4px; margin-top: 5px;">
                        <li><a class="dropdown-item" href="modules/customers/customers-create.php">
                            <i class="fas fa-user-plus"></i> Add New Client
                        </a></li>
                        <li><a class="dropdown-item" href="modules/orders/orders.php">
                            <i class="fas fa-list"></i> View Orders
                        </a></li>
                        <li><a class="dropdown-item" href="modules/orders/create-invoice.php">
                            <i class="fas fa-file-invoice"></i> Create Order
                        </a></li>
                        <li><a class="dropdown-item" href="modules/products/products-create.php">
                            <i class="fas fa-box-open"></i> Add New Product
                        </a></li>
                    </div>
                </div>
            </h4>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Original dashboard summary speech
            const dashboardContent = `Welcome to BizBox Business Assistant Dashboard. Here's your 30-day summary:
                         You have ${<?= $totalCustomers ?>} active customers,
                         ${<?= $totalProducts ?>} active products,
                         Total revenue of ${<?= formatNumberForSpeech($totalRevenue) ?>} rand and ${<?= formatCentsForSpeech($totalRevenue) ?>} cents from ${<?= $validOrders ?>} orders,
                         with ${<?= $unpaidOrders ?>} unpaid orders totaling ${<?= formatNumberForSpeech($unpaidAmount) ?>} rand and ${<?= formatCentsForSpeech($unpaidAmount) ?>} cents.`;

            // Help content explaining dashboard features
            const helpContent = `Welcome to your dashboard. Let me explain what you can find here:
                         At the top, you'll see four main cards:
                         The blue card shows your total active customers with quick access to customer management.
                         The green card displays your total active products with a link to product management.
                         The light blue card shows your 30-day revenue summary, including paid and total orders.
                         The red card highlights unpaid orders that need attention.
                         Below these cards, you'll find a detailed revenue chart for the past 30 days.
                         You can click any card's View Details link to see more information.
                         For quick navigation, look for the red Quick Access button in the top right corner.
                         This button provides instant access to create new clients, orders, and products without navigating through menus.`;

            // Setup speech buttons
            setupSpeechButton('speechButton', dashboardContent);
            setupSpeechButton('helpButton', helpContent);

            // Helper function to setup speech buttons
            function setupSpeechButton(buttonId, content) {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.addEventListener('click', function() {
                        mobileTTS.speak(content, {
                            userInitiated: true,
                            rate: 0.9,
                            onStart: () => {
                                button.classList.add('speaking');
                                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                            },
                            onEnd: () => {
                                button.classList.remove('speaking');
                                button.innerHTML = buttonId === 'speechButton' ?
                                    '<i class="fas fa-volume-up"></i>' :
                                    '<i class="fas fa-question-circle"></i>';
                            },
                            onError: (error) => {
                                console.error('Speech error:', error);
                                button.classList.remove('speaking');
                                button.innerHTML = buttonId === 'speechButton' ?
                                    '<i class="fas fa-volume-up"></i>' :
                                    '<i class="fas fa-question-circle"></i>';
                                alert('Sorry, there was an error with the speech system');
                            }
                        });
                    });
                }
            }
        });
        </script>

        <!-- Dashboard Stats -->
        <div class="row p-4">
            <!-- Total Customers -->
            <div class="col-xl-3 col-md-6">
                <div class="card bg-primary text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1">Total Customers</h6>
                                <h2 class="mb-0"><?= number_format($totalCustomers) ?></h2>
                            </div>
                            <div class="square-icon">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-white stretched-link" href="modules/customers/customers.php">View Details</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>

            <!-- Total Products -->
            <div class="col-xl-3 col-md-6">
                <div class="card bg-success text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1">Total Products</h6>
                                <h2 class="mb-0"><?= number_format($totalProducts) ?></h2>
                            </div>
                            <div class="square-icon">
                                <i class="fas fa-box fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-white stretched-link" href="modules/products/products.php">View Details</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="col-xl-3 col-md-6">
                <div class="card bg-info text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1">Total Revenue (30 Days)</h6>
                                <h2 class="mb-0">R <?= number_format($totalRevenue, 2) ?></h2>
                                <small class="text-white-50">
                                    <?= $validOrders ?> Valid Orders (<?= $paidOrders ?> Paid)
                                </small>
                            </div>
                            <div class="square-icon">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-white stretched-link" href="modules/orders/orders.php">View Details</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>

            <!-- Unpaid Orders -->
            <div class="col-xl-3 col-md-6">
                <div class="card bg-danger text-white mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-uppercase mb-1">Unpaid Orders (30 Days)</h6>
                                <h2 class="mb-0">R <?= number_format($unpaidAmount, 2) ?></h2>
                                <small class="text-white-50">
                                    <?= $unpaidOrders ?> of <?= $validOrders ?> Valid Orders
                                </small>
                            </div>
                            <div class="square-icon">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-white stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#unpaidModal">View Details</a>
                        <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row px-4 pb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0">Revenue Statistics - Last 30 Days</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="invoiceStatsChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Statistics Blocks -->
        <div class="row px-4 pb-4">
            <!-- Today's Call Back Requests -->
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-phone-volume me-2"></i>Today's Call Back Requests</h5>
                    </div>
                    <div class="card-body text-center">
                        <?php
                        // Get today's call back requests
                        $todayCallBackQuery = "SELECT COUNT(*) as total FROM request_call_back WHERE DATE(created_at) = CURDATE()";
                        $todayCallBackResult = mysqli_query($conn, $todayCallBackQuery);
                        $todayCallBacks = 0;
                        if ($todayCallBackResult) {
                            $todayCallBacks = mysqli_fetch_assoc($todayCallBackResult)['total'];
                        }
                        ?>
                        <h1 class="display-4 fw-bold"><?= $todayCallBacks ?></h1>
                        <p class="text-muted">New requests today</p>
                        <?php if($unreadCallBacks > 0): ?>
                            <div class="mt-2">
                                <span class="badge bg-danger p-2"><?= $unreadCallBacks ?> unread</span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-primary stretched-link" href="modules/call_back/call_back.php">View All Requests</a>
                        <div class="small text-primary"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>

            <!-- Services Information -->
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Service Inquiries</h5>
                    </div>
                    <div class="card-body text-center">
                        <?php
                        // Get total service inquiries
                        $serviceInfoQuery = "SELECT COUNT(*) as total FROM services_more_information";
                        $serviceInfoResult = mysqli_query($conn, $serviceInfoQuery);
                        $totalServiceInfo = 0;
                        if ($serviceInfoResult) {
                            $totalServiceInfo = mysqli_fetch_assoc($serviceInfoResult)['total'];
                        }
                        ?>
                        <h1 class="display-4 fw-bold"><?= $totalServiceInfo ?></h1>
                        <p class="text-muted">Service inquiries</p>
                        <?php if($unreadServiceInfo > 0): ?>
                            <div class="mt-2">
                                <span class="badge bg-danger p-2"><?= $unreadServiceInfo ?> unread</span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-success stretched-link" href="modules/service_info/service_info.php">View Inquiries</a>
                        <div class="small text-success"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>

            <!-- Products Information -->
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-box me-2"></i>Product Inquiries</h5>
                    </div>
                    <div class="card-body text-center">
                        <?php
                        // Get total product inquiries
                        $productInfoQuery = "SELECT COUNT(*) as total FROM product_more_information";
                        $productInfoResult = mysqli_query($conn, $productInfoQuery);
                        $totalProductInfo = 0;
                        if ($productInfoResult) {
                            $totalProductInfo = mysqli_fetch_assoc($productInfoResult)['total'];
                        }
                        ?>
                        <h1 class="display-4 fw-bold"><?= $totalProductInfo ?></h1>
                        <p class="text-muted">Product inquiries</p>
                        <?php if($unreadProductInfo > 0): ?>
                            <div class="mt-2">
                                <span class="badge bg-danger p-2"><?= $unreadProductInfo ?> unread</span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer d-flex align-items-center justify-content-between">
                        <a class="small text-info stretched-link" href="modules/product_info/product_info.php">View Inquiries</a>
                        <div class="small text-info"><i class="fas fa-angle-right"></i></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login History Section -->
    <div class="container-fluid px-4">
        <div class="row mt-4">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-history me-1"></i>
                        Recent Admin Logins
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr class="bg-light">
                                        <th style="width: 30%;">Admin Name</th>
                                        <th style="width: 15%;">User Type</th>
                                        <th style="width: 30%;">Login Time</th>
                                        <th style="width: 25%;">IP Address</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Check if admin_login_history table exists
                                    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'admin_login_history'");

                                    if(mysqli_num_rows($table_check) > 0) {
                                        // Table exists, get recent admin logins
                                        $login_query = "SELECT h.*, a.name as admin_name, a.user_type
                                                      FROM admin_login_history h
                                                      JOIN admins a ON h.admin_id = a.id
                                                      ORDER BY h.login_time DESC LIMIT 5";
                                        $login_result = mysqli_query($conn, $login_query);

                                        if($login_result && mysqli_num_rows($login_result) > 0) {
                                            while($login = mysqli_fetch_assoc($login_result)) {
                                                // Format user type for display (capitalize first letter)
                                                $user_type = ucfirst($login['user_type']);

                                                // Format date and time
                                                $login_time = date('d M Y H:i:s', strtotime($login['login_time']));

                                                echo "<tr>";
                                                echo "<td><strong>{$login['admin_name']}</strong></td>";
                                                echo "<td>{$user_type}</td>";
                                                echo "<td>{$login_time}</td>";
                                                echo "<td>{$login['ip_address']}</td>";
                                                echo "</tr>";
                                            }
                                        } else {
                                            echo "<tr><td colspan='4' class='text-center'>No login history found</td></tr>";
                                        }
                                    } else {
                                        // Table doesn't exist, create it
                                        $create_table_sql = "CREATE TABLE IF NOT EXISTS `admin_login_history` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `admin_id` int(11) NOT NULL,
                                            `login_time` datetime NOT NULL DEFAULT current_timestamp(),
                                            `ip_address` varchar(50) NOT NULL,
                                            PRIMARY KEY (`id`)
                                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

                                        mysqli_query($conn, $create_table_sql);
                                        echo "<tr><td colspan='4' class='text-center'>Login history tracking has been enabled</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup History Section -->
            <?php if (isset($_SESSION['loggedInUser']) && $_SESSION['loggedInUser']['user_type'] === 'admin'): ?>
            <div class="col-12 mt-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-database me-1"></i>
                        Recent Database Backups
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-sm">
                                <thead>
                                    <tr class="bg-light">
                                        <th style="width: 30%;">Date & Time</th>
                                        <th style="width: 30%;">Admin Name</th>
                                        <th style="width: 40%;">Filename</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Check if backup_history table exists
                                    $backup_table_check = mysqli_query($conn, "SHOW TABLES LIKE 'backup_history'");

                                    if(mysqli_num_rows($backup_table_check) > 0) {
                                        // Get backup history
                                        $backup_history_query = "SELECT * FROM backup_history ORDER BY backup_date DESC LIMIT 5";
                                        $backup_history_result = mysqli_query($conn, $backup_history_query);

                                        if ($backup_history_result && mysqli_num_rows($backup_history_result) > 0) {
                                            while ($backup = mysqli_fetch_assoc($backup_history_result)) {
                                                echo "<tr>";
                                                echo "<td>" . date('d M Y H:i', strtotime($backup['backup_date'])) . "</td>";
                                                echo "<td>" . htmlspecialchars($backup['user_name']) . "</td>";
                                                echo "<td>" . htmlspecialchars($backup['backup_filename']) . "</td>";
                                                echo "</tr>";
                                            }
                                        } else {
                                            echo "<tr><td colspan='3' class='text-center'>No backup history found</td></tr>";
                                        }
                                    } else {
                                        // Table doesn't exist
                                        echo "<tr><td colspan='3' class='text-center'>Backup history tracking has been enabled</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>

<!-- Add Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get the invoice stats data
    const invoiceStats = <?php echo json_encode(get_invoice_stats_last_30_days()); ?>;

    const ctx = document.getElementById('invoiceStatsChart');
    if (!ctx) {
        console.error('Canvas element not found');
        return;
    }

    if (!invoiceStats || invoiceStats.length === 0) {
        ctx.parentNode.innerHTML = '<div class="alert alert-info">No data available for the last 30 days</div>';
        return;
    }

    // Prepare the data
    const labels = invoiceStats.map(stat => {
        const date = new Date(stat.date);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });

    const processedData = invoiceStats.map(stat => ({
        paidAmount: parseFloat(stat.paid_amount) || 0,
        notPaidAmount: parseFloat(stat.not_paid_amount) || 0,
        totalAmount: (parseFloat(stat.paid_amount) || 0) + (parseFloat(stat.not_paid_amount) || 0),
        paidCount: parseInt(stat.paid) || 0,         // Changed from paid_count to paid
        notPaidCount: parseInt(stat.not_paid) || 0   // Changed from not_paid_count to not_paid
    }));

    // Chart configuration
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Paid Orders',
                data: processedData.map(d => d.paidAmount),
                backgroundColor: 'rgba(40, 167, 69, 0.5)',
                borderColor: 'rgb(40, 167, 69)',
                borderWidth: 1
            }, {
                label: 'Unpaid Orders',
                data: processedData.map(d => d.notPaidAmount),
                backgroundColor: 'rgba(220, 53, 69, 0.5)',
                borderColor: 'rgb(220, 53, 69)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const index = context.dataIndex;
                            const data = processedData[index];
                            const isFirstDataset = context.datasetIndex === 0;
                            const amount = isFirstDataset ? data.paidAmount : data.notPaidAmount;
                            const count = isFirstDataset ? data.paidCount : data.notPaidCount;
                            const total = data.totalAmount;
                            const percent = total > 0 ? ((amount / total) * 100).toFixed(1) : 0;
                            return `${context.dataset.label}: ${count} orders (${percent}%) R${amount.toFixed(2)}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    },
                    ticks: {
                        callback: function(value) {
                            return 'R ' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });
});
</script>

<style>
.square-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
}
</style>

<!-- Unpaid Invoices Modal -->
<div class="modal fade" id="unpaidModal" tabindex="-1" aria-labelledby="unpaidModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unpaidModalLabel">Unpaid Invoices</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $unpaidOrders = get_unpaid_orders();
                            if($unpaidOrders):
                                foreach($unpaidOrders as $order):
                            ?>
                            <tr>
                                <td><?= $order['tracking_no'] ?></td>
                                <td><?= $order['customer_name'] ?></td>
                                <td><?= date('d M Y', strtotime($order['date'])) ?></td>
                                <td>R <?= number_format($order['total_amount'], 2) ?></td>
                            </tr>
                            <?php
                                endforeach;
                            else:
                            ?>
                            <tr>
                                <td colspan="4" class="text-center">No unpaid invoices found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

function get_unpaid_orders()
{
    global $conn;
    $query = "SELECT o.*, c.name as customer_name
              FROM orders o
              LEFT JOIN customers c ON o.customer_id = c.id
              WHERE o.payment_status = 'Not Paid'
              ORDER BY o.date DESC";

    $result = mysqli_query($conn, $query);

    if($result) {
        return mysqli_fetch_all($result, MYSQLI_ASSOC);
    }
    return false;
}



<!-- Voice Control Buttons -->
<div class="position-fixed" style="bottom: 20px; right: 20px; z-index: 1050;">
    <button id="voiceCommandBtn" class="btn btn-primary"
            style="border-radius: 50%; width: 60px; height: 60px;">
        <i class="fas fa-microphone"></i>
    </button>

    <button class="btn btn-info ms-2"
            data-bs-toggle="popover"
            data-bs-placement="left"
            data-bs-html="true"
            data-bs-title="Available Voice Commands"
            data-bs-content="
                <ul class='list-unstyled mb-0'>
                    <li><i class='fas fa-chevron-right'></i> 'show orders' - Go to orders page</li>
                    <li><i class='fas fa-chevron-right'></i> 'show customers' - Go to customers page</li>
                </ul>
                <hr class='my-2'>
                <small><a href='help/voice-commands.php'>View all commands</a></small>
            "
            style="border-radius: 50%; width: 40px; height: 40px;">
        <i class="fas fa-question"></i>
    </button>
</div>

<!-- Initialize popovers -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    const popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            trigger: 'click'
        });
    });

    // Close popover when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('[data-bs-toggle="popover"]')) {
            popoverList.forEach(popover => {
                popover.hide();
            });
        }
    });
});
</script>

<!-- Voice Command Feedback Modal -->
<div class="modal fade" id="voiceFeedbackModal" tabindex="-1" role="dialog" aria-labelledby="voiceFeedbackLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <p id="voiceFeedbackText" role="status" aria-live="polite">Listening...</p>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS is loaded in footer.php -->

<script>
// Modified VoiceControl class
class VoiceControl {
    constructor(config) {
        this.config = {
            commands: {},
            feedbackDuration: 3000,
            maxRetries: 2,
            retryDelay: 3000,
            ...config
        };
        this.recognition = null;
        this.isListening = false;
        this.retryCount = 0;
        this.retryTimeout = null;

        // Add browser detection
        this.isBraveBrowser = this.detectBrave();
    }

    detectBrave() {
        return navigator.brave?.isBrave() ||
               (window.navigator.userAgent.includes("Brave"));
    }

    async init() {
        try {
            if (!('SpeechRecognition' in window || 'webkitSpeechRecognition' in window)) {
                throw new Error('Speech recognition not supported');
            }

            this.recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            this.recognition.lang = 'en-US';
            this.recognition.continuous = false;
            this.recognition.interimResults = false;

            this.setupEventListeners();
            console.log('Speech recognition initialized successfully');
        } catch (e) {
            console.error('Speech recognition not supported:', e);
            const voiceBtn = document.getElementById('voiceCommandBtn');
            if (voiceBtn) {
                voiceBtn.style.display = 'none';
                this.showFeedback('Speech recognition is not supported in this browser');
            }
        }
    }

    setupEventListeners() {
        const voiceBtn = document.getElementById('voiceCommandBtn');

        if (voiceBtn) {
            voiceBtn.addEventListener('click', () => {
                this.toggleListening();
            });
        }

        this.recognition.onstart = () => {
            this.isListening = true;
            if (voiceBtn) {
                voiceBtn.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                voiceBtn.classList.add('btn-danger');
            }
            this.showFeedback('Listening...');
        };

        this.recognition.onend = () => {
            this.isListening = false;
            if (voiceBtn) {
                voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                voiceBtn.classList.remove('btn-danger');
            }
        };

        this.recognition.onresult = (event) => {
            const command = event.results[0][0].transcript.toLowerCase();
            console.log('Voice command:', command);
            this.handleCommand(command);
        };
    }

    showFeedback(message, duration = 3000) {
        const feedbackText = document.getElementById('voiceFeedbackText');
        const feedbackModal = new bootstrap.Modal(document.getElementById('voiceFeedbackModal'));

        if (feedbackText) {
            feedbackText.innerHTML = message;
            feedbackModal.show();

            setTimeout(() => {
                feedbackModal.hide();
            }, duration);
        }
    }

    async toggleListening() {
        if (!this.isListening) {
            try {
                await this.recognition.start();
            } catch (error) {
                console.error('Error starting recognition:', error);
            }
        } else {
            this.stopListening();
        }
    }

    stopListening() {
        this.recognition.stop();
        this.isListening = false;
    }

    handleCommand(command) {
        for (const [pattern, handler] of Object.entries(this.config.commands)) {
            if (command.includes(pattern)) {
                this.showFeedback('Executing: ' + pattern);
                handler(command);
                return;
            }
        }
        this.showFeedback('Command not recognized: ' + command);
    }
}

// Initialize voice control
document.addEventListener('DOMContentLoaded', function() {
    const voiceControl = new VoiceControl({
        commands: {
            'show orders': () => {
                window.location.href = 'modules/orders/orders.php';
            },
            'show customers': () => {
                window.location.href = 'modules/customers/customers.php';
            }
        }
    });

    voiceControl.init();
});
</script>

<!-- Custom Dropdown Styles -->
<style>
.custom-dropdown-menu li {
    list-style: none;
}

.custom-dropdown-menu .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

.custom-dropdown-menu .dropdown-item:hover,
.custom-dropdown-menu .dropdown-item:focus {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa;
}

.custom-dropdown-menu .dropdown-header {
    display: block;
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: #6c757d;
    white-space: nowrap;
}

.custom-dropdown-menu .dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #e9ecef;
}
</style>

<!-- Custom Dropdown Scripts -->
<script>
// Function to toggle notifications dropdown
function toggleNotificationsMenu() {
    var menu = document.getElementById('notificationsDropdownMenu');
    if (menu.style.display === 'none' || menu.style.display === '') {
        // Close any other open dropdowns first
        closeAllDropdowns();
        // Then open this one
        menu.style.display = 'block';
    } else {
        menu.style.display = 'none';
    }
}

// Function to toggle quick access dropdown
function toggleQuickAccessMenu() {
    var menu = document.getElementById('quickAccessDropdownMenu');
    if (menu.style.display === 'none' || menu.style.display === '') {
        // Close any other open dropdowns first
        closeAllDropdowns();
        // Then open this one
        menu.style.display = 'block';
    } else {
        menu.style.display = 'none';
    }
}

// Function to close all dropdowns
function closeAllDropdowns() {
    var dropdowns = document.querySelectorAll('.custom-dropdown-menu');
    dropdowns.forEach(function(dropdown) {
        dropdown.style.display = 'none';
    });
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(e) {
    // Check if the click was outside all custom dropdowns
    if (!e.target.closest('.custom-dropdown')) {
        closeAllDropdowns();
    }
});

// Prevent dropdown menus from closing when clicking inside them
document.addEventListener('DOMContentLoaded', function() {
    var dropdownMenus = document.querySelectorAll('.custom-dropdown-menu');
    dropdownMenus.forEach(function(menu) {
        menu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // Add hover effect to dropdown items
    var dropdownItems = document.querySelectorAll('.custom-dropdown-menu .dropdown-item');
    dropdownItems.forEach(function(item) {
        item.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        item.addEventListener('mouseout', function() {
            this.style.backgroundColor = 'transparent';
        });
    });
});
</script>



<?php
// Check for backup success parameter
if (isset($_GET['backup']) && $_GET['backup'] === 'success') {
    echo "<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof bootstrap !== 'undefined') {
            const toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            toastContainer.innerHTML = `
                <div class='toast align-items-center text-white bg-success border-0' role='alert' aria-live='assertive' aria-atomic='true'>
                    <div class='d-flex'>
                        <div class='toast-body'>
                            <i class='fas fa-check-circle me-2'></i>Database backup completed successfully!
                        </div>
                        <button type='button' class='btn-close btn-close-white me-2 m-auto' data-bs-dismiss='toast' aria-label='Close'></button>
                    </div>
                </div>
            `;
            document.body.appendChild(toastContainer);

            const toastElement = toastContainer.querySelector('.toast');
            const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
            toast.show();
        }
    });
    </script>";
}
?>
