<?php
include('../../includes/header.php');

// Add the export function
function getProductsForExport() {
    global $conn;
    $sql = "SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY p.id DESC";

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_execute($stmt);
    return mysqli_stmt_get_result($stmt);
}
?>

<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Products
                <a href="products-create.php" class="btn btn-primary float-end">Add Product</a>
                <a href="../../index.php" class="btn btn-dark float-end me-2">Dashboard <i class="fas fa-tachometer-alt"></i></a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <?php
            $products = getProductsForExport();
            if(!$products){
                echo '<h4>Something Went Wrong!</h4>';
                return false;
            }

            if(mysqli_num_rows($products) > 0)
            {
            ?>
            <form action="code.php" method="POST">
                <table id="exportTable" class="display">
                    <thead>
                        <tr>
                            <th style="display:none;">ID</th>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Cost Price</th>
                            <th>Sales Price</th>
                            <th>VAT Amount</th>
                            <th>Final Price</th>
                            <th>Quantity</th>
                            <th>Barcode</th>
                            <th>Featured <i class="fas fa-question-circle" data-bs-toggle="tooltip" title="Check to show on demo page"></i></th>
                            <th>Visibility <i class="fas fa-question-circle" data-bs-toggle="tooltip" title="Shows if product is visible to customers"></i></th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($products as $item) : ?>
                        <tr>
                            <td style="display:none;"><?= $item['id'] ?></td>
                            <td>
                                <?php if(!empty($item['image'])): ?>
                                    <img src="../../../uploads/products/<?= $item['image'] ?>" width="50px" height="50px" alt="Product Image">
                                <?php else: ?>
                                    <span>No Image</span>
                                <?php endif; ?>
                            </td>
                            <td><?= $item['name'] ?></td>
                            <td><?= $item['category_name'] ?></td>
                            <td><?= $item['cost_price'] ?></td>
                            <td><?= $item['sales_price'] ?></td>
                            <td><?= $item['vatT'] ?></td>
                            <td><?= $item['price'] ?></td>
                            <td><?= $item['quantity'] ?></td>
                            <td><?= $item['barcode'] ?></td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="featured[]" value="<?= $item['id']; ?>"
                                        <?= ($item['featured'] == 1) ? 'checked' : ''; ?> style="width:40px;height:20px;" />
                                    <?php if($item['featured'] == 1): ?>
                                        <span class="badge bg-primary" style="margin-left:5px;">
                                            <i class="fas fa-star"></i> Featured
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php if($item['status'] == 1): ?>
                                    <span class="badge bg-danger" style="padding: 8px; font-size: 12px;">
                                        <i class="fas fa-eye-slash"></i> HIDDEN
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-success" style="padding: 8px; font-size: 12px;">
                                        <i class="fas fa-eye"></i> VISIBLE
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="d-flex gap-2">
                                    <a href="products-edit.php?id=<?= $item['id']; ?>" class="btn btn-primary btn-sm" data-bs-toggle="tooltip" title="Edit Product">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="product-view.php?id=<?= $item['id']; ?>" class="btn btn-outline-secondary btn-sm" data-bs-toggle="tooltip" title="View Product Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="mt-3">
                    <button type="submit" name="saveUpdateFeatured" class="btn btn-primary">Save Update Featured</button>
                </div>
            </form>
            <?php
            }
            else
            {
                echo '<h4 class="mb-0">No Record found</h4>';
            }
            ?>
        </div>
    </div>
</div>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<?php include('../../includes/footer.php'); ?>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

<script>
$(document).ready(function() {
    $('#exportTable').DataTable({
        dom: 'Bfrtip',
        order: [[0, 'desc']], // Order by the first column (ID) in descending order
        buttons: [
            {
                extend: 'copy',
                className: 'btn btn-secondary btn-sm',
                text: '<i class="fas fa-copy"></i> Copy'
            },
            {
                extend: 'csv',
                className: 'btn btn-primary btn-sm',
                text: '<i class="fas fa-file-csv"></i> CSV'
            },
            {
                extend: 'excel',
                className: 'btn btn-success btn-sm',
                text: '<i class="fas fa-file-excel"></i> Excel'
            },
            {
                extend: 'pdf',
                className: 'btn btn-danger btn-sm',
                text: '<i class="fas fa-file-pdf"></i> PDF'
            },
            {
                extend: 'print',
                className: 'btn btn-info btn-sm',
                text: '<i class="fas fa-print"></i> Print'
            }
        ]
    });
});
</script>

</body>
</html>
