<?php include('includes/header.php'); ?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Service Categories
                <a href="index.php" class="btn me-4 btn-dark float-end">Dashboard  <i class="fas fa-tachometer-alt"></i></a>
                <a href="services-categories-create.php" class="btn me-4 btn-primary float-end">Add Category</a>
            </h4>
        </div>
        <div class="card-body">

            <?php alertMessage(); ?>

            <?php
            function getServiceCategories() {
                global $conn;
                $stmt = mysqli_prepare($conn, "SELECT * FROM services_categories ORDER BY id DESC");
                mysqli_stmt_execute($stmt);
                return mysqli_stmt_get_result($stmt);
            }

            $categories = getServiceCategories();
            if($categories) {
            ?>
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($categories as $item) : ?>
                        <tr>
                            <td><?= htmlspecialchars($item['id']) ?></td>
                            <td><?= htmlspecialchars($item['name']) ?></td>
                            <td><?= htmlspecialchars($item['description']) ?></td>
                            <td>
                                <?= $item['status'] == 1 ? 'Hidden' : 'Visible' ?>
                            </td>
                            <td>
                                <a href="services-categories-edit.php?id=<?= urlencode($item['id']) ?>" 
                                   class="btn btn-success btn-sm">Edit</a>
                                <a href="services-categories-delete.php?id=<?= urlencode($item['id']) ?>" 
                                   class="btn btn-danger btn-sm" 
                                   onclick="return confirm('Are you sure?')">Delete</a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php
            }
            ?>
        </div>
    </div>
</div>
