2025-04-13 16:53:20 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:53:44 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:54:08 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:56:29 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:56:33 - Request: Array
(
    [product_id] => 29
)

2025-04-13 16:57:19 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:57:22 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 2
            [2] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:57:23 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

2025-04-13 16:57:31 - Request: Array
(
    [product_id] => 29
    [colors] => Array
        (
            [0] => 3
            [1] => 1
        )

    [sizes] => Array
        (
            [0] => 1
            [1] => 2
        )

)

