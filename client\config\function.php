<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require 'config.php';

function alertMessageCustomer($message, $type = 'danger') {
    echo '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
            ' . $message . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

function redirectCustomer($url, $status) {
    $_SESSION['status'] = $status;
    header('Location: ' . $url);
    exit(0);
}

function validateCustomer($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return mysqli_real_escape_string($conn, $data);
}

function getCustomerById($customerId) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "SELECT * FROM customers WHERE id = ? LIMIT 1");
    mysqli_stmt_bind_param($stmt, "i", $customerId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if(mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }
    return false;
}

function updateCustomerProfile($customerId, $data) {
    global $conn;
    
    $allowedFields = ['name', 'email', 'phone', 'address'];
    $updates = [];
    $types = '';
    $values = [];
    
    foreach($data as $key => $value) {
        if(in_array($key, $allowedFields)) {
            $updates[] = "$key = ?";
            $types .= 's';
            $values[] = validateCustomer($value);
        }
    }
    
    if(empty($updates)) {
        return false;
    }
    
    $types .= 'i'; // for customer_id
    $values[] = $customerId;
    
    $query = "UPDATE customers SET " . implode(', ', $updates) . " WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, $types, ...$values);
    
    return mysqli_stmt_execute($stmt);
}

function getCustomerOrders($customerId, $limit = null) {
    global $conn;
    
    $query = "SELECT * FROM orders WHERE customer_id = ? ORDER BY created_at DESC";
    if($limit) {
        $query .= " LIMIT ?";
    }
    
    $stmt = mysqli_prepare($conn, $query);
    if($limit) {
        mysqli_stmt_bind_param($stmt, "ii", $customerId, $limit);
    } else {
        mysqli_stmt_bind_param($stmt, "i", $customerId);
    }
    
    mysqli_stmt_execute($stmt);
    return mysqli_stmt_get_result($stmt);
}

function getOrderDetails($orderId, $customerId) {
    global $conn;
    
    $stmt = mysqli_prepare($conn, "
        SELECT o.*, oi.* 
        FROM orders o 
        JOIN order_items oi ON o.id = oi.order_id 
        WHERE o.id = ? AND o.customer_id = ?
    ");
    mysqli_stmt_bind_param($stmt, "ii", $orderId, $customerId);
    mysqli_stmt_execute($stmt);
    return mysqli_stmt_get_result($stmt);
}

function checkCustomerLogin() {
    if(!isset($_SESSION['customerLoggedIn']) || $_SESSION['customerLoggedIn'] !== true) {
        redirectCustomer('login.php', 'Please login to continue');
    }
}

function logoutCustomer() {
    unset($_SESSION['customerLoggedIn']);
    unset($_SESSION['customerLoggedInUser']);
    session_destroy();
    redirectCustomer('login.php', 'Logged out successfully');
}

function updateCustomerPassword($customerId, $currentPassword, $newPassword) {
    global $conn;
    
    // First verify current password
    $stmt = mysqli_prepare($conn, "SELECT password FROM customers WHERE id = ?");
    mysqli_stmt_bind_param($stmt, "i", $customerId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $customer = mysqli_fetch_assoc($result);
    
    if(!password_verify($currentPassword, $customer['password'])) {
        return false;
    }
    
    // Update to new password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    $stmt = mysqli_prepare($conn, "UPDATE customers SET password = ? WHERE id = ?");
    mysqli_stmt_bind_param($stmt, "si", $hashedPassword, $customerId);
    
    return mysqli_stmt_execute($stmt);
}

function createCustomerOrder($customerId, $items, $shippingAddress) {
    global $conn;
    
    try {
        mysqli_begin_transaction($conn);
        
        // Insert order
        $stmt = mysqli_prepare($conn, "
            INSERT INTO orders (customer_id, shipping_address, status) 
            VALUES (?, ?, 'pending')
        ");
        mysqli_stmt_bind_param($stmt, "is", $customerId, $shippingAddress);
        mysqli_stmt_execute($stmt);
        $orderId = mysqli_insert_id($conn);
        
        // Insert order items
        $stmt = mysqli_prepare($conn, "
            INSERT INTO order_items (order_id, product_id, quantity, price) 
            VALUES (?, ?, ?, ?)
        ");
        
        foreach($items as $item) {
            mysqli_stmt_bind_param($stmt, "iiid", 
                $orderId, 
                $item['product_id'], 
                $item['quantity'], 
                $item['price']
            );
            mysqli_stmt_execute($stmt);
        }
        
        mysqli_commit($conn);
        return $orderId;
        
    } catch (Exception $e) {
        mysqli_rollback($conn);
        return false;
    }
}

function sanitizeOutput($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

function formatPrice($price) {
    return number_format($price, 2);
}

function generateClientCode() {
    return strtoupper(substr(uniqid(), -6));
}
?>
