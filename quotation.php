<?php
// Include session manager at the very beginning
require_once 'session_manager.php';

// Include database connection
include('admin/config/dbcon.php');

// Include quotation functions
require_once 'quotation_functions.php';

// Get quotation items
$quotation_items = getQuotationItems();
$quotation_total = getQuotationTotal();
$item_count = getQuotationItemCount();

// Don't redefine quotation_item_count in header.php
$quotation_item_count = $item_count;

// Get company address from settings table
$settings_query = "SELECT companyname, address FROM settings LIMIT 1";
$settings_result = mysqli_query($conn, $settings_query);
$company_settings = mysqli_fetch_assoc($settings_result);
$company_address = $company_settings['address'] ?? 'Address not available';
$company_name = $company_settings['companyname'] ?? 'Our Company';

// Process form submission
$success_message = '';
$error_message = '';
$quotation_number = '';
$whatsapp_link = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_quotation'])) {
    // Validate form data
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $delivery_option = $_POST['delivery_option'] ?? 'pickup';
    $delivery_address = ($delivery_option === 'delivery') ? trim($_POST['delivery_address']) : '';
    $pickup_date = ($delivery_option === 'pickup' && !empty($_POST['pickup_date'])) ? $_POST['pickup_date'] : null;
    $pickup_time = ($delivery_option === 'pickup' && !empty($_POST['pickup_time'])) ? $_POST['pickup_time'] : null;
    $message = trim($_POST['message'] ?? '');

    // Get quotation items and total
    $quotation_items = getQuotationItems();
    $total_amount = getQuotationTotal();

    // Check if quotation has items
    if (empty($quotation_items)) {
        $error_message = 'Your quotation is empty. Please add products before submitting.';
    } else {
        // Generate a random quotation number (Q followed by 6 digits)
        $quotation_number = 'Q' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);

        // Insert quotation into database
        $query = "INSERT INTO quotations (quotation_number, name, email, phone, delivery_option, delivery_address, pickup_date, pickup_time, message, total_amount)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'sssssssssd', $quotation_number, $name, $email, $phone, $delivery_option, $delivery_address, $pickup_date, $pickup_time, $message, $total_amount);

        if (mysqli_stmt_execute($stmt)) {
            // Get the inserted quotation ID
            $quotation_id = mysqli_insert_id($conn);

            // Insert quotation items
            $item_query = "INSERT INTO quotation_items (quotation_id, product_id, color_id, color_name, size_id, size_name, sku, name, price, quantity, custom_build_id, is_component)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $item_stmt = mysqli_prepare($conn, $item_query);

            // Group items by build_id to organize custom builds
            $grouped_items = [];
            $build_items = [];

            foreach ($quotation_items as $key => $item) {
                if (isset($item['build_id'])) {
                    if (!isset($build_items[$item['build_id']])) {
                        $build_items[$item['build_id']] = [];
                    }
                    $build_items[$item['build_id']][] = $key;
                } else {
                    $grouped_items[] = $key;
                }
            }

            // First, add all regular items (not part of custom builds)
            foreach ($grouped_items as $key) {
                $item = $quotation_items[$key];

                // Calculate the unit price based on bulk pricing
                $unit_price = $item['price'];
                $quantity = $item['quantity'];

                // Apply bulk pricing if available
                if (!empty($item['bulk_pricing'])) {
                    foreach ($item['bulk_pricing'] as $bulk) {
                        if ($quantity >= $bulk['min_quantity'] &&
                            ($quantity <= $bulk['max_quantity'] || $bulk['max_quantity'] == 0)) {
                            $unit_price = $bulk['price'];
                            break;
                        }
                    }
                }

                // Add additional price for variants
                $color_additional_price = $item['color_additional_price'] ?? 0;
                $size_additional_price = $item['size_additional_price'] ?? 0;
                $total_additional_price = $color_additional_price + $size_additional_price;
                $final_price = $unit_price + $total_additional_price;

                // Get color and size information
                $color_id = $item['color_id'] ?? null;
                $color_name = $item['color_name'] ?? '';
                $size_id = $item['size_id'] ?? null;
                $size_name = $item['size_name'] ?? '';

                // Get custom build information
                $custom_build_id = null;
                $is_component = 0;

                // Insert the item
                $sku = $item['sku'] ?? '';
                mysqli_stmt_bind_param($item_stmt, 'iiissssssdsi', $quotation_id, $item['id'], $color_id, $color_name, $size_id, $size_name, $sku, $item['name'], $final_price, $item['quantity'], $custom_build_id, $is_component);
                mysqli_stmt_execute($item_stmt);
            }

            // Then, add all custom build items
            foreach ($build_items as $build_id => $item_keys) {
                // Find the main buildable product (the one that is not a component)
                $main_product_key = null;
                foreach ($item_keys as $key) {
                    if (!isset($quotation_items[$key]['is_component']) || $quotation_items[$key]['is_component'] == 0) {
                        $main_product_key = $key;
                        break;
                    }
                }

                if ($main_product_key !== null) {
                    // Add the main buildable product first
                    $item = $quotation_items[$main_product_key];

                    // Calculate price
                    $unit_price = $item['price'];
                    $color_additional_price = $item['color_additional_price'] ?? 0;
                    $size_additional_price = $item['size_additional_price'] ?? 0;
                    $total_additional_price = $color_additional_price + $size_additional_price;
                    $final_price = $unit_price + $total_additional_price;

                    // Get color and size information
                    $color_id = $item['color_id'] ?? null;
                    $color_name = $item['color_name'] ?? '';
                    $size_id = $item['size_id'] ?? null;
                    $size_name = $item['size_name'] ?? '';

                    // Insert the main buildable product
                    $custom_build_id = $build_id;
                    $is_component = 0;
                    $sku = $item['sku'] ?? '';
                    mysqli_stmt_bind_param($item_stmt, 'iiissssssdsi', $quotation_id, $item['id'], $color_id, $color_name, $size_id, $size_name, $sku, $item['name'], $final_price, $item['quantity'], $custom_build_id, $is_component);
                    mysqli_stmt_execute($item_stmt);

                    // Then add all component items
                    foreach ($item_keys as $key) {
                        if ($key !== $main_product_key) {
                            $component = $quotation_items[$key];

                            // Calculate price
                            $unit_price = $component['price'];
                            $color_additional_price = $component['color_additional_price'] ?? 0;
                            $size_additional_price = $component['size_additional_price'] ?? 0;
                            $total_additional_price = $color_additional_price + $size_additional_price;
                            $final_price = $unit_price + $total_additional_price;

                            // Get color and size information
                            $color_id = $component['color_id'] ?? null;
                            $color_name = $component['color_name'] ?? '';
                            $size_id = $component['size_id'] ?? null;
                            $size_name = $component['size_name'] ?? '';

                            // Insert the component
                            $is_component = 1;
                            $component_sku = $component['sku'] ?? '';
                            mysqli_stmt_bind_param($item_stmt, 'iiissssssdsi', $quotation_id, $component['id'], $color_id, $color_name, $size_id, $size_name, $component_sku, $component['name'], $final_price, $component['quantity'], $custom_build_id, $is_component);
                            mysqli_stmt_execute($item_stmt);
                        }
                    }
                }
            }

            // Clear the quotation after successful submission
            clearQuotation();

            // Set success message
            $success_message = "Your quotation request has been submitted successfully! Your quotation number is <strong>{$quotation_number}</strong>. We will contact you shortly.";

            // Create WhatsApp link for confirmation
            $whatsapp_number = "27797869698"; // Replace with your actual WhatsApp number
            $whatsapp_message = "Hello, I've submitted quotation {$quotation_number} and would like to confirm receipt. My name is {$name}.";
            $whatsapp_link = "https://wa.me/{$whatsapp_number}?text=" . urlencode($whatsapp_message);
        } else {
            $error_message = 'There was an error submitting your quotation. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Quotation | Custom Applications</title>
    <meta name="description" content="Request a quotation for our products. Custom Applications offers a range of solutions for your business needs.">

    <link href="assets/img/favicon.png" rel="icon">
    <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Jost:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <link href="assets_site/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets_site/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="assets_site/vendor/aos/aos.css" rel="stylesheet">
    <link href="assets_site/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
    <link href="assets_site/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <link href="assets_site/css/main.css" rel="stylesheet">

    <style>
        /* Add margin to account for fixed header */
        #quotation {
            margin-top: 120px;
            padding-bottom: 60px;
        }

        .quotation-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .quotation-title {
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
        }

        .quotation-title:after {
            content: '';
            position: absolute;
            width: 50px;
            height: 3px;
            background-color: #007bff;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }

        .quotation-subtitle {
            color: #666;
            font-size: 16px;
            max-width: 700px;
            margin: 0 auto;
        }

        .quotation-item {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .quotation-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quotation-item-image {
            width: 80px;
            height: 80px;
            object-fit: contain;
            border-radius: 5px;
            background-color: #fff;
            padding: 5px;
            border: 1px solid #e9ecef;
            flex-shrink: 0; /* Prevent image from shrinking */
        }

        .quotation-item-details {
            flex-grow: 1;
            padding-left: 15px; /* Add padding between image and details */
            min-width: 0; /* Prevent overflow issues */
        }

        .quotation-item-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .quotation-item-price {
            font-weight: 700;
            color: #28a745;
        }

        .quotation-summary {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .quotation-total {
            font-size: 24px;
            font-weight: 700;
            color: #28a745;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .empty-quotation {
            text-align: center;
            padding: 50px 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-top: 30px;
        }

        .empty-quotation i {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 20px;
            display: block;
        }

        .empty-quotation h4 {
            color: #666;
            margin-bottom: 10px;
        }

        .empty-quotation p {
            color: #999;
        }

        .quantity-input {
            width: 60px;
            text-align: center;
            border-radius: 5px;
            border: 1px solid #ced4da;
            padding: 5px;
        }

        .btn-update-quantity {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            display: inline-block;
            text-decoration: none;
        }

        .btn-update-quantity:hover {
            background-color: #5a6268;
            text-decoration: none;
            color: white;
        }

        .btn-remove-item {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            display: inline-block;
            text-decoration: none;
        }

        .btn-remove-item:hover {
            background-color: #c82333;
            text-decoration: none;
            color: white;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        /* Fix for overlapping images */
        .quotation-item {
            display: flex;
            align-items: center;
        }

        .quotation-item > div:first-child {
            margin-right: 15px;
            flex: 0 0 80px; /* Fixed width for image container */
        }

        .quotation-item-variants .badge {
            display: inline-block;
            margin-bottom: 5px;
        }

        /* Custom build styling */
        .custom-build-container {
            border-left: 3px solid #28a745;
            padding-left: 15px;
            margin-bottom: 15px;
        }

        .custom-build-component {
            margin-left: 20px;
            border-left: 2px dashed #17a2b8;
            padding-left: 15px;
        }
    </style>
</head>
<body>

<!-- Include Header -->
<?php include('includes_site/header.php'); ?>

<section id="quotation">
    <div class="container">
        <div class="quotation-header">
            <h1 class="quotation-title">Your Quotation</h1>
            <p class="quotation-subtitle">Review your selected products and submit your quotation request</p>
        </div>

        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success">
                <p><?= $success_message ?></p>
                <?php if (!empty($whatsapp_link)): ?>
                <div class="mt-3">
                    <a href="<?= $whatsapp_link ?>" target="_blank" class="btn btn-success">
                        <i class="bi bi-whatsapp"></i> Confirm via WhatsApp
                    </a>
                    <a href="index.php" class="btn btn-primary ms-2">
                        <i class="bi bi-house"></i> Return to Home
                    </a>
                </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger">
                <?= $error_message ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($_SESSION['message'])): ?>
            <div class="alert alert-<?= $_SESSION['message_type'] ?? 'info' ?>">
                <?= $_SESSION['message'] ?>
            </div>
            <?php
            // Clear the message after displaying it
            unset($_SESSION['message']);
            unset($_SESSION['message_type']);
            ?>
        <?php endif; ?>

        <?php if (!empty($quotation_items)): ?>
            <div class="row">
                <div class="col-lg-8">
                    <h3 class="mb-4">Selected Products</h3>

                    <?php foreach (array_values($quotation_items) as $index => $item): ?>
                        <div class="quotation-item d-flex align-items-center">
                            <div class="me-3">
                                <?php if (!empty($item['image'])): ?>
                                    <img src="uploads/products/<?= htmlspecialchars($item['image']) ?>"
                                         class="quotation-item-image"
                                         alt="<?= htmlspecialchars($item['name']) ?>"
                                         onerror="this.onerror=null; this.src='assets_site/img/placeholder.jpg'; this.alt='No Image Available';" />
                                <?php else: ?>
                                    <div class="quotation-item-image d-flex align-items-center justify-content-center">
                                        <i class="bi bi-image" style="font-size: 32px; color: #aaa;"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="quotation-item-details">
                                <h5 class="quotation-item-name"><?= htmlspecialchars($item['name']) ?></h5>
                                <?php if(isset($item['build_id']) && isset($item['is_component']) && $item['is_component'] == 1): ?>
                                <div class="quotation-item-variants mb-2">
                                    <span class="badge bg-info me-2">Component</span>
                                </div>
                                <?php elseif(isset($item['build_id']) && (!isset($item['is_component']) || $item['is_component'] == 0)): ?>
                                <div class="quotation-item-variants mb-2">
                                    <span class="badge bg-success me-2">Custom Build</span>
                                </div>
                                <?php endif; ?>
                                <?php if(!empty($item['color_name']) || !empty($item['size_name']) || !empty($item['sku'])): ?>
                                <div class="quotation-item-variants mb-2">
                                    <?php if(!empty($item['sku'])): ?>
                                    <span class="badge bg-info me-2">SKU: <?= htmlspecialchars($item['sku']) ?></span>
                                    <?php endif; ?>
                                    <?php if(!empty($item['color_name'])): ?>
                                    <span class="badge bg-primary me-2">Color: <?= htmlspecialchars($item['color_name']) ?></span>
                                    <?php endif; ?>
                                    <?php if(!empty($item['size_name'])): ?>
                                    <span class="badge bg-secondary">Size: <?= htmlspecialchars($item['size_name']) ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                                <p class="quotation-item-price">
                                    <?php
                                    // Calculate the unit price based on bulk pricing
                                    $unit_price = $item['price'];
                                    $color_additional_price = $item['color_additional_price'] ?? 0;
                                    $size_additional_price = $item['size_additional_price'] ?? 0;
                                    $total_additional_price = $color_additional_price + $size_additional_price;
                                    $quantity = $item['quantity'];
                                    $bulk_discount = 0;
                                    $original_price = $unit_price;

                                    if (!empty($item['bulk_pricing'])) {
                                        foreach ($item['bulk_pricing'] as $bulk) {
                                            if ($quantity >= $bulk['min_quantity'] &&
                                                ($quantity <= $bulk['max_quantity'] || $bulk['max_quantity'] == 0)) {
                                                $unit_price = $bulk['price'];
                                                $bulk_discount = $original_price - $unit_price;
                                                break;
                                            }
                                        }
                                    }

                                    // Calculate the total for this item
                                    $item_total = ($unit_price + $total_additional_price) * $quantity;
                                    ?>
                                    <strong>Base Price:</strong> R <?= number_format($unit_price, 2) ?>
                                    <?php if ($bulk_discount > 0): ?>
                                        <span class="text-success"><small>(Bulk discount: -R <?= number_format($bulk_discount, 2) ?>)</small></span>
                                    <?php endif; ?>
                                    <br>
                                    <?php if ($color_additional_price > 0): ?>
                                        <strong>Color (<?= htmlspecialchars($item['color_name']) ?>):</strong> +R <?= number_format($color_additional_price, 2) ?><br>
                                    <?php endif; ?>
                                    <?php if ($size_additional_price > 0): ?>
                                        <strong>Size (<?= htmlspecialchars($item['size_name']) ?>):</strong> +R <?= number_format($size_additional_price, 2) ?><br>
                                    <?php endif; ?>
                                    <strong>Quantity:</strong> <?= $quantity ?><br>
                                    <strong>Subtotal:</strong> R <?= number_format($item_total, 2) ?>
                                </p>
                            </div>
                            <div class="ms-auto d-flex align-items-center">
                                <div class="me-3">
                                    <input type="number" class="quantity-input" value="<?= $item['quantity'] ?>" min="1" max="100" id="quantity-<?= $index ?>">
                                    <a href="simple_update.php?index=<?= $index ?>&quantity=" onclick="return updateQuantity(<?= $index ?>);" class="btn-update-quantity ms-2">Update</a>
                                </div>
                                <a href="simple_remove.php?index=<?= $index ?>" class="btn-remove-item" onclick="return confirm('Are you sure you want to remove this item?');">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="product_list.php" class="btn btn-primary">
                            <i class="bi bi-arrow-left"></i> Continue Shopping
                        </a>
                        <a href="simple_clear.php" class="btn btn-danger" onclick="return confirm('Are you sure you want to clear your entire quotation?');">
                            <i class="bi bi-trash"></i> Clear Quotation
                        </a>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="quotation-summary">
                        <h3 class="mb-4">Quotation Summary</h3>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Items:</span>
                            <span><?= $item_count ?></span>
                        </div>
                        <div class="quotation-total d-flex justify-content-between">
                            <span>Total:</span>
                            <span>R <?= number_format($quotation_total, 2) ?></span>
                        </div>

                        <form method="post" class="mt-4">
                            <h4 class="mb-3">Your Information</h4>
                            <div class="mb-3">
                                <label for="name" class="form-label">Your Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="text" class="form-control" id="phone" name="phone" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Delivery Option</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="delivery_option" id="pickup" value="pickup" checked onclick="toggleDeliveryAddress(false)">
                                    <label class="form-check-label" for="pickup">
                                        Pick Up
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="delivery_option" id="delivery" value="delivery" onclick="toggleDeliveryAddress(true)">
                                    <label class="form-check-label" for="delivery">
                                        Delivery
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3" id="pickup_address_container">
                                <label class="form-label">Pick Up Address</label>
                                <div class="card p-3 bg-light">
                                    <p class="mb-0"><strong><?= htmlspecialchars($company_name) ?></strong></p>
                                    <p class="mb-0"><?= nl2br(htmlspecialchars($company_address)) ?></p>
                                </div>
                                <small class="text-muted">You will need to collect your items from this address.</small>

                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <label for="pickup_date" class="form-label">Preferred Pickup Date</label>
                                        <input type="date" class="form-control" id="pickup_date" name="pickup_date" min="<?= date('Y-m-d') ?>">
                                        <small class="text-muted">Select your preferred date for pickup</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="pickup_time" class="form-label">Preferred Pickup Time</label>
                                        <select class="form-select" id="pickup_time" name="pickup_time">
                                            <option value="">Select a time slot</option>
                                            <option value="Morning (9:00 AM - 12:00 PM)">Morning (9:00 AM - 12:00 PM)</option>
                                            <option value="Afternoon (12:00 PM - 3:00 PM)">Afternoon (12:00 PM - 3:00 PM)</option>
                                            <option value="Late Afternoon (3:00 PM - 5:00 PM)">Late Afternoon (3:00 PM - 5:00 PM)</option>
                                        </select>
                                        <small class="text-muted">Select your preferred time for pickup</small>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3" id="delivery_address_container" style="display: none;">
                                <label for="delivery_address" class="form-label">Delivery Address</label>
                                <textarea class="form-control" id="delivery_address" name="delivery_address" rows="3" placeholder="Please provide your full delivery address including postal code"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="message" class="form-label">Additional Information</label>
                                <textarea class="form-control" id="message" name="message" rows="3" placeholder="Any special requirements or notes"></textarea>
                            </div>
                            <button type="submit" name="submit_quotation" class="btn btn-success w-100">
                                <i class="bi bi-send"></i> Submit Quotation Request
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="empty-quotation">
                <i class="bi bi-cart-x"></i>
                <h4>Your Quotation is Empty</h4>
                <p>Browse our products and add items to your quotation</p>
                <a href="product_list.php" class="btn btn-primary mt-3">
                    <i class="bi bi-arrow-left"></i> Browse Products
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<script src="assets_site/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="assets_site/vendor/aos/aos.js"></script>
<script src="assets_site/vendor/glightbox/js/glightbox.min.js"></script>
<script src="assets_site/vendor/swiper/swiper-bundle.min.js"></script>
<script src="assets_site/js/main.js"></script>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="quotation_script.js"></script>

<script>
    // Function to toggle delivery address field
    function toggleDeliveryAddress(show) {
        const deliveryContainer = document.getElementById('delivery_address_container');
        const pickupContainer = document.getElementById('pickup_address_container');
        const addressField = document.getElementById('delivery_address');

        if (show) {
            // Show delivery address, hide pickup address
            deliveryContainer.style.display = 'block';
            pickupContainer.style.display = 'none';
            addressField.setAttribute('required', 'required');
        } else {
            // Show pickup address, hide delivery address
            deliveryContainer.style.display = 'none';
            pickupContainer.style.display = 'block';
            addressField.removeAttribute('required');
            addressField.value = ''; // Clear the field when hidden
        }
    }

    // Initialize the form on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Check if the delivery option is already selected (e.g., after form validation)
        const deliveryRadio = document.getElementById('delivery');
        if (deliveryRadio && deliveryRadio.checked) {
            toggleDeliveryAddress(true);
        }
    });
</script>

</body>
</html>
