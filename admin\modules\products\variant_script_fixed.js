// Function to refresh the variants table
function refreshVariantsTable() {
    console.log('Refreshing variants table...');
    const productId = document.querySelector('input[name="product_id"]').value;
    console.log('Product ID:', productId);

    const colorCheckboxes = document.querySelectorAll('.variant-color-checkbox:checked');
    console.log('Checked color checkboxes:', colorCheckboxes.length);

    const sizeCheckboxes = document.querySelectorAll('.variant-size-checkbox:checked');
    console.log('Checked size checkboxes:', sizeCheckboxes.length);

    // Get selected color IDs
    const colorIds = [];
    colorCheckboxes.forEach(function(checkbox) {
        colorIds.push(checkbox.value);
    });
    console.log('Selected color IDs:', colorIds);

    // Get selected size IDs
    const sizeIds = [];
    sizeCheckboxes.forEach(function(checkbox) {
        sizeIds.push(checkbox.value);
    });
    console.log('Selected size IDs:', sizeIds);

    // Build the URL
    let url = `dynamic_variants.php?product_id=${productId}`;

    if (colorIds.length > 0) {
        colorIds.forEach(function(id) {
            url += `&colors[]=${id}`;
        });
    }

    if (sizeIds.length > 0) {
        sizeIds.forEach(function(id) {
            url += `&sizes[]=${id}`;
        });
    }

    console.log('Request URL:', url);

    // Fetch the updated variants table
    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.error) {
                console.error('Error:', data.error);
                return;
            }

            // Update the variants table
            const variantsTableContainer = document.getElementById('variantsTableContainer');
            if (variantsTableContainer) {
                variantsTableContainer.innerHTML = data.html;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
        });
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Variant script initialized');

    // Get elements
    const refreshVariantsBtn = document.getElementById('refreshVariantsBtn');
    const updateVariantsBtn = document.getElementById('updateVariantsBtn');
    const variantUpdateForm = document.getElementById('variantUpdateForm');
    const autoRefreshCheckbox = document.getElementById('autoRefreshVariants');
    const colorCheckboxes = document.querySelectorAll('.variant-color-checkbox');
    const sizeCheckboxes = document.querySelectorAll('.variant-size-checkbox');

    console.log('Refresh button:', refreshVariantsBtn);
    console.log('Update button:', updateVariantsBtn);
    console.log('Variant form:', variantUpdateForm);
    console.log('Auto-refresh checkbox:', autoRefreshCheckbox);
    console.log('Color checkboxes:', colorCheckboxes.length);
    console.log('Size checkboxes:', sizeCheckboxes.length);

    // Add event listener to refresh button
    if (refreshVariantsBtn) {
        console.log('Adding click event to refresh button');
        refreshVariantsBtn.addEventListener('click', function() {
            console.log('Refresh button clicked');
            refreshVariantsTable();
        });
    }

    // Add event listeners to color and size checkboxes
    colorCheckboxes.forEach(function(checkbox) {
        console.log('Adding change event to color checkbox:', checkbox.id);
        checkbox.addEventListener('change', function() {
            console.log('Color checkbox changed:', this.id, 'Checked:', this.checked);
            // Auto-refresh when a checkbox is changed
            if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
                console.log('Auto-refreshing variants...');
                refreshVariantsTable();
            }
        });
    });

    sizeCheckboxes.forEach(function(checkbox) {
        console.log('Adding change event to size checkbox:', checkbox.id);
        checkbox.addEventListener('change', function() {
            console.log('Size checkbox changed:', this.id, 'Checked:', this.checked);
            // Auto-refresh when a checkbox is changed
            if (autoRefreshCheckbox && autoRefreshCheckbox.checked) {
                console.log('Auto-refreshing variants...');
                refreshVariantsTable();
            }
        });
    });

    // Initial refresh
    console.log('Performing initial refresh...');
    refreshVariantsTable();

    // Handle variant updates
    if (updateVariantsBtn && variantUpdateForm) {
        updateVariantsBtn.addEventListener('click', function() {
            // Clear any existing fields
            while (variantUpdateForm.children.length > 1) {
                variantUpdateForm.removeChild(variantUpdateForm.lastChild);
            }

            // Get all variant fields
            const variantIds = document.querySelectorAll('input[name="variant_ids[]"]');
            const variantColorIds = document.querySelectorAll('input[name="variant_color_ids[]"]');
            const variantSizeIds = document.querySelectorAll('input[name="variant_size_ids[]"]');
            const variantAdditionalPrices = document.querySelectorAll('input[name="variant_additional_prices[]"]');
            const variantQuantities = document.querySelectorAll('input[name="variant_quantities[]"]');
            const variantStatuses = document.querySelectorAll('input[name="variant_status[]"]');

            // Add them to the variant update form
            variantIds.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantColorIds.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantSizeIds.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantAdditionalPrices.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantQuantities.forEach(function(input) {
                const clone = input.cloneNode(true);
                variantUpdateForm.appendChild(clone);
            });

            variantStatuses.forEach(function(input) {
                if (input.checked) {
                    const clone = input.cloneNode(true);
                    variantUpdateForm.appendChild(clone);
                }
            });

            // Submit the form
            variantUpdateForm.submit();
        });
    }
});
