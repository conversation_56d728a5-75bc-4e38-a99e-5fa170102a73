<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Check if tables exist
$tables_to_check = [
    'products',
    'product_images',
    'product_variant_images',
    'product_secondary_categories',
    'product_colors',
    'product_sizes',
    'product_bulk_pricing',
    'product_labels',
    'product_types',
    'product_components'
];

echo "<h1>Table Status</h1>";
echo "<table border='1'>";
echo "<tr><th>Table Name</th><th>Status</th></tr>";

foreach ($tables_to_check as $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = mysqli_query($conn, $query);
    $exists = mysqli_num_rows($result) > 0;
    
    echo "<tr>";
    echo "<td>$table</td>";
    echo "<td style='background-color: " . ($exists ? "#d4edda" : "#f8d7da") . "'>" . ($exists ? "Exists" : "Missing") . "</td>";
    echo "</tr>";
}

echo "</table>";

// Check if product_images table exists, if not create it
$table_check = mysqli_query($conn, "SHOW TABLES LIKE 'product_images'");
if (mysqli_num_rows($table_check) == 0) {
    echo "<h2>Creating product_images table...</h2>";
    
    $create_table = "CREATE TABLE `product_images` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `product_id` int(11) NOT NULL,
      `image` varchar(255) NOT NULL,
      `display_order` int(11) NOT NULL DEFAULT 0,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    if (mysqli_query($conn, $create_table)) {
        echo "<p>product_images table created successfully!</p>";
    } else {
        echo "<p>Error creating product_images table: " . mysqli_error($conn) . "</p>";
    }
}

// Check if product_variant_images table exists, if not create it
$table_check = mysqli_query($conn, "SHOW TABLES LIKE 'product_variant_images'");
if (mysqli_num_rows($table_check) == 0) {
    echo "<h2>Creating product_variant_images table...</h2>";
    
    $create_table = "CREATE TABLE `product_variant_images` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `product_id` int(11) NOT NULL,
      `color_id` int(11) NOT NULL,
      `image` varchar(255) NOT NULL,
      `display_order` int(11) NOT NULL DEFAULT 0,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `product_id` (`product_id`),
      KEY `color_id` (`color_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    if (mysqli_query($conn, $create_table)) {
        echo "<p>product_variant_images table created successfully!</p>";
    } else {
        echo "<p>Error creating product_variant_images table: " . mysqli_error($conn) . "</p>";
    }
}

// Check if product_secondary_categories table exists, if not create it
$table_check = mysqli_query($conn, "SHOW TABLES LIKE 'product_secondary_categories'");
if (mysqli_num_rows($table_check) == 0) {
    echo "<h2>Creating product_secondary_categories table...</h2>";
    
    $create_table = "CREATE TABLE `product_secondary_categories` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `product_id` int(11) NOT NULL,
      `category_id` int(11) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `product_id` (`product_id`),
      KEY `category_id` (`category_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    if (mysqli_query($conn, $create_table)) {
        echo "<p>product_secondary_categories table created successfully!</p>";
    } else {
        echo "<p>Error creating product_secondary_categories table: " . mysqli_error($conn) . "</p>";
    }
}
?>
