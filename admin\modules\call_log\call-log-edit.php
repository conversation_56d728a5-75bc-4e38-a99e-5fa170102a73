<?php include('includes/header.php'); ?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Call Log
                <a href="call-log.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">

            <?php alertMessage(); ?>

            <form action="code.php" method="POST">

                <?php
                $parmValue = checkParamId('id');
                if(!is_numeric($parmValue)){
                    echo '<h5>'.$parmValue.'</h5>';
                    return false;
                }

                $call_log = getById('call_log',$parmValue);
                if($call_log['status'] == 200)
                {
                ?>

                <input type="hidden" name="call_logId" value="<?= $call_log['data']['id']; ?>">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="">Company Name *</label>
                        <input type="text" name="company_name" value="<?= $call_log['data']['company_name']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Number Called *</label>
                        <input type="text" name="number_called" value="<?= $call_log['data']['number_called']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Date *</label>
                        <input type="date" name="date" value="<?= $call_log['data']['date']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Time *</label>
                        <input type="time" name="time" value="<?= $call_log['data']['time']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <label for="">Call Log Note *</label>
                        <input type="text" name="call_log_note" value="<?= $call_log['data']['call_log_note']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3 text-end">
                        <br/>
                        <button type="submit" name="updateCall_log" class="btn btn-primary">Update</button>
                    </div>
                </div>
                <?php
                }
                else
                {
                    echo '<h5>'.$category['message'].'</h5>';
                }
                ?>
            </form>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>