<?php
// This file contains the integrated variant management section for products-edit.php

// Determine the correct product ID based on context
$current_product_id = isset($product['data']['id']) ? $product['data']['id'] : $product['id'];
$current_product_name = isset($product['data']['name']) ? $product['data']['name'] : $product['name'];

// Get selected colors
$selected_colors = [];
$selected_colors_query = mysqli_query($conn, "SELECT DISTINCT color_id FROM product_variants WHERE product_id = {$current_product_id} AND color_id IS NOT NULL");
if($selected_colors_query) {
    while($row = mysqli_fetch_assoc($selected_colors_query)) {
        $selected_colors[] = $row['color_id'];
    }
}

// Get selected sizes
$selected_sizes = [];
$selected_sizes_query = mysqli_query($conn, "SELECT DISTINCT size_id FROM product_variants WHERE product_id = {$current_product_id} AND size_id IS NOT NULL");
if($selected_sizes_query) {
    while($row = mysqli_fetch_assoc($selected_sizes_query)) {
        $selected_sizes[] = $row['size_id'];
    }
}

// Check if product has variants
$has_variants = !empty($selected_colors) || !empty($selected_sizes);
?>

<!-- Integrated Variant Management Section -->
<div class="col-md-12 mb-4">
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 d-flex align-items-center">
                    <i class="fas fa-tags me-2"></i>Product Variants
                </h5>
                <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#variantsAccordion" aria-expanded="false" aria-controls="variantsAccordion">
                    <i class="fas fa-cog me-1"></i> Configure Variants
                </button>
            </div>
        </div>

        <div class="card-body">
            <?php
            // Display variant summary
            $variant_count = 0;
            $variant_query = mysqli_query($conn, "SELECT COUNT(*) as variant_count FROM product_variants WHERE product_id = {$current_product_id}");
            if($variant_query) {
                $variant_count = mysqli_fetch_assoc($variant_query)['variant_count'];
            }

            // Get variant summary
            $colors_summary = [];
            $sizes_summary = [];

            if($variant_count > 0) {
                // Get colors
                $colors_query = mysqli_query($conn, "SELECT DISTINCT pc.name
                                            FROM product_variants pv
                                            JOIN product_colors pc ON pv.color_id = pc.id
                                            WHERE pv.product_id = {$current_product_id}
                                            AND pv.color_id IS NOT NULL");
                if($colors_query) {
                    while($row = mysqli_fetch_assoc($colors_query)) {
                        $colors_summary[] = $row['name'];
                    }
                }

                // Get sizes
                $sizes_query = mysqli_query($conn, "SELECT DISTINCT ps.name
                                            FROM product_variants pv
                                            JOIN product_sizes ps ON pv.size_id = ps.id
                                            WHERE pv.product_id = {$current_product_id}
                                            AND pv.size_id IS NOT NULL");
                if($sizes_query) {
                    while($row = mysqli_fetch_assoc($sizes_query)) {
                        $sizes_summary[] = $row['name'];
                    }
                }
            }
            ?>

            <div class="alert <?= $variant_count > 0 ? 'alert-success' : 'alert-info' ?>">
                <i class="fas <?= $variant_count > 0 ? 'fa-check-circle' : 'fa-info-circle' ?> me-2"></i>
                <?php if($variant_count > 0): ?>
                    This product has <strong><?= $variant_count ?> variant(s)</strong>.
                <?php else: ?>
                    This product does not have any variants yet.
                <?php endif; ?>
            </div>

            <?php if($variant_count > 0): ?>
            <div class="row mb-3">
                <?php if(!empty($colors_summary)): ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0">Colors</h6>
                        </div>
                        <div class="card-body py-2">
                            <?= implode(', ', $colors_summary) ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if(!empty($sizes_summary)): ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-light py-2">
                            <h6 class="mb-0">Sizes</h6>
                        </div>
                        <div class="card-body py-2">
                            <?= implode(', ', $sizes_summary) ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Variants Accordion -->
            <div class="collapse mt-3" id="variantsAccordion">
                <div class="card">
                    <div class="card-body">
                        <!-- Hidden form for variant updates -->
                        <div id="variantUpdateFormContainer" style="display:none;">
                            <form action="update_variants.php" method="POST" id="variantUpdateForm">
                                <input type="hidden" name="product_id" value="<?= $current_product_id; ?>">
                                <?php
                                // Determine source page based on current file
                                $current_file = basename($_SERVER['PHP_SELF']);
                                $source_page = ($current_file === 'product-variants.php') ? 'standalone' : 'integrated';
                                ?>
                                <input type="hidden" name="source_page" value="<?= $source_page; ?>">
                                <!-- Variant fields will be added dynamically -->
                            </form>
                        </div>

                        <div class="row">
                            <div class="col-md-5">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">Colors and Sizes</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="enableVariants" <?= $has_variants ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enableVariants">
                                                Enable product variants (colors and sizes)
                                            </label>
                                        </div>

                                        <div id="variantsContainer" style="display: <?= $has_variants ? 'block' : 'none'; ?>;">
                                            <div class="mb-4">
                                                <h6>Available Colors</h6>
                                                <div class="border p-3 rounded">
                                                    <?php
                                                    $colors_query = mysqli_query($conn, "SELECT * FROM product_colors WHERE status = 0 ORDER BY name ASC");
                                                    if(mysqli_num_rows($colors_query) > 0) {
                                                        while($color = mysqli_fetch_assoc($colors_query)) {
                                                            $is_selected = in_array($color['id'], $selected_colors);
                                                            ?>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" name="variant_colors[]" value="<?= $color['id'] ?>" id="color<?= $color['id'] ?>" <?= $is_selected ? 'checked' : '' ?>>
                                                                <label class="form-check-label" for="color<?= $color['id'] ?>">
                                                                    <span style="display: inline-block; width: 20px; height: 20px; background-color: <?= $color['color_code'] ?>; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                                                                    <?= htmlspecialchars($color['name']) ?>
                                                                </label>
                                                            </div>
                                                            <?php
                                                        }
                                                    } else {
                                                        echo '<p>No colors available.</p>';
                                                    }
                                                    ?>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <h6>Available Sizes</h6>
                                                <div class="border p-3 rounded">
                                                    <?php
                                                    $sizes_query = mysqli_query($conn, "SELECT * FROM product_sizes WHERE status = 0 ORDER BY name ASC");
                                                    if(mysqli_num_rows($sizes_query) > 0) {
                                                        while($size = mysqli_fetch_assoc($sizes_query)) {
                                                            $is_selected = in_array($size['id'], $selected_sizes);
                                                            ?>
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" name="variant_sizes[]" value="<?= $size['id'] ?>" id="size<?= $size['id'] ?>" <?= $is_selected ? 'checked' : '' ?>>
                                                                <label class="form-check-label" for="size<?= $size['id'] ?>">
                                                                    <?= htmlspecialchars($size['name']) ?>
                                                                </label>
                                                            </div>
                                                            <?php
                                                        }
                                                    } else {
                                                        echo '<p>No sizes available.</p>';
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-7">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">Variant Combinations</h5>
                                            <div>
                                                <button type="button" id="updateVariantsBtn" class="btn btn-danger">
                                                    <i class="fas fa-save me-1"></i> Save Variants
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted small mb-3">Manage product variants manually. Add, edit, or remove variants as needed. Changes require clicking the <span class="text-danger fw-bold">"Save Variants"</span> button to be stored.</p>
                                        <div id="variantsTableContainer">
                                            <!-- Variants will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Integrated Variants -->
<script>
// Function to generate variants table HTML directly
function generateVariantsTable() {
    console.log('Generating variants table...');
    const productId = <?= $current_product_id ?>;

    // Get selected colors and sizes
    const colorCheckboxes = document.querySelectorAll('input[name="variant_colors[]"]:checked');
    const sizeCheckboxes = document.querySelectorAll('input[name="variant_sizes[]"]:checked');

    const colorIds = Array.from(colorCheckboxes).map(checkbox => checkbox.value);
    const sizeIds = Array.from(sizeCheckboxes).map(checkbox => checkbox.value);

    console.log('Selected color IDs:', colorIds);
    console.log('Selected size IDs:', sizeIds);

    // If no colors or sizes selected, show message
    if (colorIds.length === 0 && sizeIds.length === 0) {
        const html = `
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Color</th>
                            <th>Size</th>
                            <th>Additional Price</th>
                            <th>Quantity</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="5" class="text-center">No variants selected. Please select colors and/or sizes above.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;

        document.getElementById('variantsTableContainer').innerHTML = html;
        return;
    }

    // Generate combinations
    let combinations = [];

    if (colorIds.length === 0) {
        // Only sizes
        sizeIds.forEach(sizeId => {
            combinations.push({ colorId: null, sizeId });
        });
    } else if (sizeIds.length === 0) {
        // Only colors
        colorIds.forEach(colorId => {
            combinations.push({ colorId, sizeId: null });
        });
    } else {
        // Both colors and sizes
        colorIds.forEach(colorId => {
            sizeIds.forEach(sizeId => {
                combinations.push({ colorId, sizeId });
            });
        });
    }

    // Start building HTML
    let html = `
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>SKU</th>
                        <th>Color</th>
                        <th>Size</th>
                        <th>Additional Price</th>
                        <th>Quantity</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // Get color and size data
    const colors = {};
    const sizes = {};

    document.querySelectorAll('input[name="variant_colors[]"]').forEach(checkbox => {
        const colorId = checkbox.value;
        const colorName = checkbox.nextElementSibling.textContent.trim();
        const colorSwatch = checkbox.nextElementSibling.querySelector('span');
        const colorCode = colorSwatch ? colorSwatch.style.backgroundColor : '#FFFFFF';

        colors[colorId] = { name: colorName, code: colorCode };
    });

    document.querySelectorAll('input[name="variant_sizes[]"]').forEach(checkbox => {
        const sizeId = checkbox.value;
        const sizeName = checkbox.nextElementSibling.textContent.trim();

        sizes[sizeId] = { name: sizeName };
    });

    // Get existing variants
    const existingVariants = <?= json_encode(getBasicProductVariants($current_product_id)) ?>;

    // Add rows for each combination
    combinations.forEach(combo => {
        const { colorId, sizeId } = combo;

        // Find if this variant exists
        const existingVariant = existingVariants.find(v =>
            (v.color_id == colorId || (!colorId && !v.color_id)) &&
            (v.size_id == sizeId || (!sizeId && !v.size_id))
        );

        const variantId = existingVariant ? existingVariant.id : 0;
        const sku = existingVariant ? existingVariant.sku : '';
        const additionalPrice = existingVariant ? existingVariant.additional_price : 0;
        const quantity = existingVariant ? existingVariant.quantity : 0;
        const status = existingVariant ? existingVariant.status : 0;

        // Color cell
        let colorCell = 'N/A';
        if (colorId && colors[colorId]) {
            colorCell = `
                <span style="display: inline-block; width: 20px; height: 20px; background-color: ${colors[colorId].code}; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                ${colors[colorId].name}
            `;
        }

        // Size cell
        let sizeCell = 'N/A';
        if (sizeId && sizes[sizeId]) {
            sizeCell = sizes[sizeId].name;
        }

        html += `
            <tr>
                <td>
                    <input type="text" name="variant_skus[]" class="form-control form-control-sm" value="${sku}" placeholder="Enter SKU">
                </td>
                <td>${colorCell}</td>
                <td>${sizeCell}</td>
                <td>
                    <input type="hidden" name="variant_ids[]" value="${variantId}">
                    <input type="hidden" name="variant_color_ids[]" value="${colorId || ''}">
                    <input type="hidden" name="variant_size_ids[]" value="${sizeId || ''}">
                    <input type="number" step="0.01" name="variant_additional_prices[]" class="form-control" value="${additionalPrice}">
                </td>
                <td>
                    <input type="number" name="variant_quantities[]" class="form-control" value="${quantity}" min="0">
                </td>
                <td>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="variant_status[]" value="${variantId}" id="variantStatus${variantId}" ${status == 0 ? 'checked' : ''}>
                        <label class="form-check-label" for="variantStatus${variantId}">
                            Visible
                        </label>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    document.getElementById('variantsTableContainer').innerHTML = html;
    console.log('Variants table generated successfully');
}

// Function to update variants
function updateVariants() {
    const variantUpdateForm = document.getElementById('variantUpdateForm');
    if (!variantUpdateForm) return;

    // Clear any existing fields
    while (variantUpdateForm.children.length > 2) { // Keep the first 2 hidden fields
        variantUpdateForm.removeChild(variantUpdateForm.lastChild);
    }

    // Get all variant fields
    const variantIds = document.querySelectorAll('input[name="variant_ids[]"]');
    const variantColorIds = document.querySelectorAll('input[name="variant_color_ids[]"]');
    const variantSizeIds = document.querySelectorAll('input[name="variant_size_ids[]"]');
    const variantSkus = document.querySelectorAll('input[name="variant_skus[]"]');
    const variantAdditionalPrices = document.querySelectorAll('input[name="variant_additional_prices[]"]');
    const variantQuantities = document.querySelectorAll('input[name="variant_quantities[]"]');
    const variantStatuses = document.querySelectorAll('input[name="variant_status[]"]');

    console.log('Variant fields found:', {
        variantIds: variantIds.length,
        variantColorIds: variantColorIds.length,
        variantSizeIds: variantSizeIds.length,
        variantSkus: variantSkus.length,
        variantAdditionalPrices: variantAdditionalPrices.length,
        variantQuantities: variantQuantities.length,
        variantStatuses: variantStatuses.length
    });

    // Add them to the variant update form
    variantIds.forEach(function(input) {
        const clone = input.cloneNode(true);
        variantUpdateForm.appendChild(clone);
    });

    variantColorIds.forEach(function(input) {
        const clone = input.cloneNode(true);
        variantUpdateForm.appendChild(clone);
    });

    variantSizeIds.forEach(function(input) {
        const clone = input.cloneNode(true);
        variantUpdateForm.appendChild(clone);
    });

    variantSkus.forEach(function(input) {
        const clone = input.cloneNode(true);
        variantUpdateForm.appendChild(clone);
    });

    variantAdditionalPrices.forEach(function(input) {
        const clone = input.cloneNode(true);
        variantUpdateForm.appendChild(clone);
    });

    variantQuantities.forEach(function(input) {
        const clone = input.cloneNode(true);
        variantUpdateForm.appendChild(clone);
    });

    variantStatuses.forEach(function(input) {
        if (input.checked) {
            const clone = input.cloneNode(true);
            variantUpdateForm.appendChild(clone);
        }
    });

    // Submit the form
    console.log('Submitting variant update form');
    variantUpdateForm.submit();
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Integrated variants script initialized');

    // Get elements
    const enableVariantsCheckbox = document.getElementById('enableVariants');
    const variantsContainer = document.getElementById('variantsContainer');
    const updateVariantsBtn = document.getElementById('updateVariantsBtn');
    const colorCheckboxes = document.querySelectorAll('input[name="variant_colors[]"]');
    const sizeCheckboxes = document.querySelectorAll('input[name="variant_sizes[]"]');

    console.log('Elements found:', {
        enableVariantsCheckbox,
        variantsContainer,
        updateVariantsBtn,
        colorCheckboxes: colorCheckboxes.length,
        sizeCheckboxes: sizeCheckboxes.length
    });

    // Toggle variants container
    if (enableVariantsCheckbox && variantsContainer) {
        enableVariantsCheckbox.addEventListener('change', function() {
            variantsContainer.style.display = this.checked ? 'block' : 'none';
        });
    }

    // Add event listener to update button
    if (updateVariantsBtn) {
        updateVariantsBtn.addEventListener('click', function() {
            console.log('Update button clicked');
            updateVariants();
        });
    }

    // Manual variant generation button
    const generateVariantsBtn = document.createElement('button');
    generateVariantsBtn.type = 'button';
    generateVariantsBtn.className = 'btn btn-outline-primary btn-sm mb-3';
    generateVariantsBtn.innerHTML = '<i class="bi bi-plus-circle"></i> Generate Variant Combinations';
    generateVariantsBtn.onclick = function() {
        generateVariantsTable();
    };

    // Insert the button before the variants table container
    const variantsTableContainer = document.getElementById('variantsTableContainer');
    if (variantsTableContainer) {
        variantsTableContainer.parentNode.insertBefore(generateVariantsBtn, variantsTableContainer);
    }

    // Initial generation of variants table
    console.log('Performing initial variants table generation...');
    setTimeout(function() {
        generateVariantsTable();
    }, 500); // Slight delay to ensure DOM is fully loaded
});
</script>

<?php
// Helper function to get basic product variants (used only in this file)
function getBasicProductVariants($product_id) {
    global $conn;
    $variants = [];

    $query = mysqli_query($conn, "SELECT * FROM product_variants WHERE product_id = $product_id");
    if($query) {
        while($row = mysqli_fetch_assoc($query)) {
            $variants[] = $row;
        }
    }

    return $variants;
}
?>
