<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Create a log file
$log_file = __DIR__ . '/very_simple_form_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - <PERSON>ript started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    log_message("Form submitted", $_POST);
    
    try {
        // Basic product data
        $category_id = validate($_POST['category_id']);
        $name = validate($_POST['name']);
        $description = $_POST['description']; // Don't validate HTML content
        $barcode = isset($_POST['barcode']) ? validate($_POST['barcode']) : '0000';
        $cost_price = validate($_POST['cost_price']);
        $sales_price = validate($_POST['sales_price']);
        $vat_percentage = isset($_POST['vat_percentage']) ? validate($_POST['vat_percentage']) : 0;
        $quantity = validate($_POST['quantity']);
        $status = isset($_POST['status']) ? 1 : 0;
        
        // Calculate VAT
        $vatT = ($sales_price * $vat_percentage) / 100;
        $price = $sales_price + $vatT;
        
        log_message("Basic product data", [
            'category_id' => $category_id,
            'name' => $name,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price,
            'quantity' => $quantity,
            'barcode' => $barcode,
            'status' => $status
        ]);
        
        // Handle main image upload
        $finalImage = "default.jpg"; // Default image
        if(isset($_FILES['image']['name']) && !empty($_FILES['image']['name'])) {
            $upload_path = "../../../uploads/products/";
            $image = $_FILES['image']['name'];
            
            if($image != "") {
                $finalImage = time() . '_main_' . $image;
                $upload_to = $upload_path . $finalImage;
                
                if (!file_exists($upload_path)) {
                    mkdir($upload_path, 0777, true);
                }
                
                if(move_uploaded_file($_FILES['image']['tmp_name'], $upload_to)) {
                    log_message("Main image uploaded successfully", $finalImage);
                } else {
                    log_message("Failed to upload main image", $_FILES['image']['error']);
                }
            }
        }
        
        // Other fields with default values
        $featured = isset($_POST['featured']) ? 1 : 0;
        $is_featured = 0;
        $product_type_id = isset($_POST['product_type_id']) ? validate($_POST['product_type_id']) : 1;
        $can_be_component = isset($_POST['can_be_component']) ? 1 : 0;
        $created_at = date('Y-m-d H:i:s');
        
        // Direct SQL query
        $sql = "INSERT INTO products (
            category_id, name, description, barcode, cost_price, sales_price, 
            vat_percentage, vatT, price, quantity, image, status, created_at,
            featured, is_featured, product_type_id, can_be_component
        ) VALUES (
            '$category_id', '$name', '$description', '$barcode', '$cost_price', '$sales_price',
            '$vat_percentage', '$vatT', '$price', '$quantity', '$finalImage', '$status', '$created_at',
            '$featured', '$is_featured', '$product_type_id', '$can_be_component'
        )";
        
        log_message("SQL Query", $sql);
        
        $result = mysqli_query($conn, $sql);
        $product_id = mysqli_insert_id($conn);
        
        log_message("Query result", [
            'success' => $result ? 'true' : 'false',
            'product_id' => $product_id,
            'error' => mysqli_error($conn)
        ]);
        
        if($result) {
            // Success - redirect to products page
            log_message("Product created successfully, redirecting to products.php");
            redirect('products.php', 'Product Created Successfully!');
        } else {
            // Failure - redirect back to create page
            log_message("Failed to create product");
            redirect('products-create.php', 'Something Went Wrong! MySQL Error: ' . mysqli_error($conn));
        }
    } catch (Exception $e) {
        log_message("Exception", [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        redirect('products-create.php', 'Error: ' . $e->getMessage());
    } catch (Error $e) {
        log_message("Error", [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        redirect('products-create.php', 'Error: ' . $e->getMessage());
    }
} else {
    log_message("Form not submitted");
    redirect('products-create.php', 'Invalid request method');
}
?>
