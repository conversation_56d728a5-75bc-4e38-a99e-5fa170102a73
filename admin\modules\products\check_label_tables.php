<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Check if product_labels table exists
$sql = "SHOW TABLES LIKE 'product_labels'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>product_labels table exists.</p>";
    
    // Show table structure
    $sql = "DESCRIBE product_labels";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>product_labels table structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Show existing labels
    $sql = "SELECT * FROM product_labels";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>Existing product labels:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Background Color</th><th>Text Color</th><th>Status</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td style='background-color:" . $row['background_color'] . "'>" . $row['background_color'] . "</td>";
        echo "<td style='color:" . $row['text_color'] . "'>" . $row['text_color'] . "</td>";
        echo "<td>" . ($row['status'] == 0 ? 'Visible' : 'Hidden') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>product_labels table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE product_labels (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        background_color VARCHAR(20) NOT NULL DEFAULT '#000000',
        text_color VARCHAR(20) NOT NULL DEFAULT '#FFFFFF',
        status TINYINT NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if(mysqli_query($conn, $sql)) {
        echo "<p>product_labels table created successfully.</p>";
        
        // Insert some default labels
        $labels = [
            ['New', '#28a745', '#FFFFFF'],
            ['Sale', '#dc3545', '#FFFFFF'],
            ['Hot', '#fd7e14', '#FFFFFF'],
            ['Featured', '#17a2b8', '#FFFFFF']
        ];
        
        foreach($labels as $label) {
            $sql = "INSERT INTO product_labels (name, background_color, text_color) VALUES ('{$label[0]}', '{$label[1]}', '{$label[2]}')";
            mysqli_query($conn, $sql);
        }
        
        echo "<p>Default labels created.</p>";
    } else {
        echo "<p>Error creating product_labels table: " . mysqli_error($conn) . "</p>";
    }
}

// Check if product_label_assignments table exists
$sql = "SHOW TABLES LIKE 'product_label_assignments'";
$result = mysqli_query($conn, $sql);

if(mysqli_num_rows($result) > 0) {
    echo "<p>product_label_assignments table exists.</p>";
    
    // Show table structure
    $sql = "DESCRIBE product_label_assignments";
    $result = mysqli_query($conn, $sql);
    
    echo "<h3>product_label_assignments table structure:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>product_label_assignments table does not exist. Creating it now...</p>";
    
    // Create the table
    $sql = "CREATE TABLE product_label_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        label_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (label_id) REFERENCES product_labels(id) ON DELETE CASCADE
    )";
    
    if(mysqli_query($conn, $sql)) {
        echo "<p>product_label_assignments table created successfully.</p>";
    } else {
        echo "<p>Error creating product_label_assignments table: " . mysqli_error($conn) . "</p>";
    }
}
?>
