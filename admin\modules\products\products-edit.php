<?php
require '../../config/function.php';
require '../../config/dbcon.php';
require '../../includes/header.php';
include('product_audio.php'); // Include the product audio functionality
require_once('product_variant_functions.php'); // Include product variant functions
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Product
                <a href="products.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <form action="code.php" method="POST" enctype="multipart/form-data" id="mainProductForm">
                <!-- Main form for product details -->

                <!-- Hidden form for variant updates -->
                <div id="variantUpdateFormContainer" style="display:none;">
                    <form action="update_variants.php" method="POST" id="variantUpdateForm">
                        <input type="hidden" name="product_id" value="<?= $product['data']['id']; ?>">
                        <!-- Variant fields will be added dynamically -->
                    </form>
                </div>

                <?php
                $paramValue = checkParamId('id');
                if(!is_numeric($paramValue)){
                    echo '<h5>'.$paramValue.'</h5>';
                    return false;
                }

                $product = getById('products', $paramValue);
                if($product['status'] == 200)
                {
                ?>

                <input type="hidden" name="product_id" value="<?= $product['data']['id']; ?>">

                <!-- Display product name -->
                <div class="alert alert-info mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Editing: <strong><?= $product['data']['name']; ?></strong></h5>
                        <div>
                            <a href="product-variants.php?id=<?= $product['data']['id']; ?>" class="btn btn-warning btn-sm">
                                <i class="fas fa-cogs"></i> Manage Product Variants
                            </a>
                            <a href="product-view.php?id=<?= $product['data']['id']; ?>" class="btn btn-info btn-sm">
                                <i class="fas fa-eye"></i> View Product
                            </a>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label>Select Category *</label>
                        <select name="category_id" class="form-select">
                            <option value="">Select Category</option>
                            <?php
                            $categories = getAll('categories');
                            if($categories){
                                if(mysqli_num_rows($categories) > 0){
                                    foreach($categories as $category){
                                        ?>
                                        <option value="<?= $category['id']; ?>"
                                            <?= $product['data']['category_id'] == $category['id'] ? 'selected':''; ?> >
                                            <?= $category['name']; ?>
                                        </option>
                                        <?php
                                    }
                                }else{
                                    echo '<option value="">No Categories Found</option>';
                                }
                            }else{
                                echo '<option value="">Something Went Wrong!</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label for="">Product Name *</label>
                        <input type="text" name="name" value="<?= $product['data']['name']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <label for="">Description</label>
                        <textarea name="description" id="editor" class="form-control" rows="3"><?= htmlspecialchars_decode($product['data']['description']); ?></textarea>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">Cost Price *</label>
                        <input type="text" name="cost_price" value="<?= $product['data']['cost_price']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">Sales Price *</label>
                        <input type="text" name="sales_price" value="<?= $product['data']['sales_price']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">VAT Percentage *</label>
                        <input type="text" name="vat_percentage" value="<?= $product['data']['vat_percentage']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">Quantity *</label>
                        <input type="text" name="quantity" value="<?= $product['data']['quantity']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">Barcode</label>
                        <input type="text" name="barcode" value="<?= $product['data']['barcode']; ?>" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Image (Main)</label>
                        <input type="file" name="image" class="form-control" accept="image/*" />
                        <div class="mt-2">
                            <small class="text-muted">Current image:</small>
                            <?php if(!empty($product['data']['image'])): ?>
                                <img src="../../../uploads/products/<?= $product['data']['image']; ?>" style="width:60px;height:60px;object-fit:cover;" class="img-thumbnail" alt="Main Image" />
                            <?php else: ?>
                                <span class="text-muted">No image uploaded</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Additional Images</label>
                        <input type="file" name="additional_images[]" class="form-control" accept="image/*" multiple />
                        <small class="text-muted">You can select up to 3 additional images (hold Ctrl to select multiple)</small>

                        <div class="mt-2">
                            <small class="text-muted">Current additional images:</small>
                            <div class="d-flex flex-wrap gap-2 mt-1">
                                <?php
                                // Get additional images for this product
                                $additional_images_query = mysqli_query($conn, "SELECT * FROM product_images WHERE product_id = '{$product['data']['id']}' ORDER BY display_order ASC");

                                if($additional_images_query && mysqli_num_rows($additional_images_query) > 0) {
                                    while($img = mysqli_fetch_assoc($additional_images_query)) {
                                        echo '<div class="position-relative">';
                                        echo '<img src="../../../uploads/products/'.$img['image'].'" style="width:50px;height:50px;object-fit:cover;" class="img-thumbnail" alt="Additional Image" />';
                                        echo '<a href="code.php?action=delete_product_image&id='.$img['id'].'&product_id='.$product['data']['id'].'" class="position-absolute top-0 end-0 btn btn-danger btn-sm" style="padding: 0 5px; font-size: 10px;" onclick="return confirm(\'Are you sure you want to delete this image?\');">×</a>';
                                        echo '</div>';
                                    }
                                } else {
                                    echo '<span class="text-muted">No additional images</span>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    <!-- Product Labels -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-tag me-2"></i>Product Labels
                                    <?php createAudioButton('labelsAudioBtn', 'Listen to Product Labels explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Select a label to display on this product:</p>
                                <div class="d-flex flex-wrap gap-3">
                                    <?php
                                    // Get current product labels
                                    $current_labels = [];
                                    $label_query = mysqli_query($conn, "SELECT label_id FROM product_label_assignments WHERE product_id = {$product['data']['id']}");
                                    if(mysqli_num_rows($label_query) > 0) {
                                        while($label_row = mysqli_fetch_assoc($label_query)) {
                                            $current_labels[] = $label_row['label_id'];
                                        }
                                    }

                                    // Get all labels
                                    $labels = mysqli_query($conn, "SELECT * FROM product_labels WHERE status = 0 ORDER BY name ASC");
                                    if(mysqli_num_rows($labels) > 0) {
                                        while($label = mysqli_fetch_assoc($labels)) {
                                            $checked = in_array($label['id'], $current_labels) ? 'checked' : '';
                                            ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="product_labels[]" value="<?= $label['id'] ?>" id="label<?= $label['id'] ?>" <?= $checked ?>>
                                                <label class="form-check-label" for="label<?= $label['id'] ?>">
                                                    <span class="badge" style="background-color: <?= $label['background_color'] ?>; color: <?= $label['text_color'] ?>; padding: 5px 10px;">
                                                        <?= htmlspecialchars($label['name']) ?>
                                                    </span>
                                                </label>
                                            </div>
                                            <?php
                                        }
                                    } else {
                                        echo '<p>No labels available. <a href="labels-create.php">Create some labels</a> first.</p>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Discounted Price -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-tags me-2"></i>Discounted Price
                                    <?php createAudioButton('discountedPriceAudioBtn', 'Listen to Discounted Price explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableDiscountedPrice" name="enable_discounted_price" value="1" <?= !empty($product['data']['discounted_price']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="enableDiscountedPrice">
                                                Enable discounted price
                                            </label>
                                        </div>
                                        <small class="text-muted">Set a discounted price for this product</small>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body p-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="fw-bold">Final Price:</span>
                                                    <span class="fs-5 text-success" id="finalPriceDisplay">R 0.00</span>
                                                </div>
                                                <small class="text-muted">Price including VAT</small>
                                                <div id="discountedPriceInfo" style="display: none;" class="mt-1">
                                                    <span class="badge bg-danger">Discounted</span>
                                                    <small class="text-muted ms-1">Original: <span id="originalPriceDisplay">R 0.00</span></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="discounted-price-container mt-3" style="display: <?= !empty($product['data']['discounted_price']) ? 'block' : 'none' ?>">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>Discounted Price *</label>
                                            <input type="number" step="0.01" name="discounted_price" id="discounted_price" class="form-control" value="<?= $product['data']['discounted_price'] ?? '' ?>" onchange="calculatePrice()" />
                                            <small class="text-muted">Enter the new discounted price (before VAT)</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label>Savings</label>
                                            <div class="input-group">
                                                <input type="text" id="savings_amount" class="form-control" readonly />
                                                <span class="input-group-text">R</span>
                                            </div>
                                            <div class="mt-2">
                                                <span id="savings_percentage" class="badge bg-success">0% Off</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Type -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-cube me-2"></i>Product Type
                                    <?php createAudioButton('typeAudioBtn', 'Listen to Product Type explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="productType">Select Product Type</label>
                                    <select name="product_type_id" id="productType" class="form-select">
                                        <?php
                                        $product_types_query = mysqli_query($conn, "SELECT * FROM product_types WHERE status = 0");
                                        if($product_types_query && mysqli_num_rows($product_types_query) > 0) {
                                            while($type = mysqli_fetch_assoc($product_types_query)) {
                                                $selected = ($product['data']['product_type_id'] == $type['id']) ? 'selected' : '';
                                                echo "<option value='{$type['id']}' {$selected}>{$type['name']}</option>";
                                            }
                                        } else {
                                            // Fallback options if no product types found in database
                                            $product_type_id = $product['data']['product_type_id'] ?? 1;
                                            echo "<option value='1' ".($product_type_id == 1 ? 'selected' : '')."'>Standard</option>";
                                            echo "<option value='2' ".($product_type_id == 2 ? 'selected' : '')."'>Buildable</option>";
                                            echo "<option value='3' ".($product_type_id == 3 ? 'selected' : '')."'>Component</option>";
                                        }
                                        ?>
                                    </select>
                                    <small class="text-muted">Standard: Regular product | Buildable: Product that can be built from components | Component: Can be used in buildable products</small>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="can_be_component" id="canBeComponent" value="1" <?= $product['data']['can_be_component'] == 1 ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="canBeComponent">
                                        This product can be used as a component in buildable products
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Variants section moved to separate page: product-variants.php -->

                    <!-- Bulk Pricing -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-money-bill-wave me-2"></i>Bulk Pricing
                                    <?php createAudioButton('bulkPricingAudioBtn', 'Listen to Bulk Pricing explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php
                                // Check if product has bulk pricing
                                $has_bulk_pricing = hasProductBulkPricing($product['data']['id']);
                                ?>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableBulkPricing" name="enable_bulk_pricing" value="1" <?= $has_bulk_pricing ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="enableBulkPricing">
                                        Enable bulk pricing
                                    </label>
                                </div>
                                <small class="text-muted">Set different prices for quantity ranges</small>

                                <div class="bulk-pricing-container mt-3" style="display: <?= $has_bulk_pricing ? 'block' : 'none' ?>;">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Quantity Range</th>
                                                    <th>Price Per Unit</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody id="bulkPricingTableBody">
                                                <?php
                                                // Fetch existing bulk pricing for this product
                                                $bulk_pricing_query = mysqli_query($conn, "SELECT * FROM product_bulk_pricing WHERE product_id = '{$product['data']['id']}' ORDER BY min_quantity ASC");

                                                if($bulk_pricing_query && mysqli_num_rows($bulk_pricing_query) > 0) {
                                                    // Display existing bulk pricing rows
                                                    while($bulk_price = mysqli_fetch_assoc($bulk_pricing_query)) {
                                                        ?>
                                                        <tr>
                                                            <td>
                                                                <div class="input-group">
                                                                    <input type="number" name="bulk_min_qty[]" class="form-control" placeholder="Min" min="2" value="<?= $bulk_price['min_quantity'] ?>">
                                                                    <span class="input-group-text">-</span>
                                                                    <input type="number" name="bulk_max_qty[]" class="form-control" placeholder="Max" min="5" value="<?= $bulk_price['max_quantity'] ?>">
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <input type="number" step="0.01" name="bulk_price[]" class="form-control" placeholder="Price per unit" value="<?= $bulk_price['price'] ?>">
                                                            </td>
                                                            <td>
                                                                <button type="button" class="btn btn-sm btn-danger remove-bulk-row">Remove</button>
                                                            </td>
                                                        </tr>
                                                        <?php
                                                    }
                                                } else {
                                                    // Display a default empty row if no bulk pricing exists
                                                    ?>
                                                    <tr>
                                                        <td>
                                                            <div class="input-group">
                                                                <input type="number" name="bulk_min_qty[]" class="form-control" placeholder="Min" min="2" value="2">
                                                                <span class="input-group-text">-</span>
                                                                <input type="number" name="bulk_max_qty[]" class="form-control" placeholder="Max" min="5" value="5">
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <input type="number" step="0.01" name="bulk_price[]" class="form-control" placeholder="Price per unit">
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-danger remove-bulk-row">Remove</button>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success" id="addBulkPricingRow">Add Range</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="">Product Visibility</label>
                        <br/>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="statusSwitch" name="status" <?= $product['data']['status'] == 1 ? 'checked':''; ?> style="width:50px;height:25px;" />
                            <label class="form-check-label" for="statusSwitch">
                                <span id="statusText" style="font-weight:bold;color:<?= $product['data']['status'] == 1 ? 'red':'green'; ?>">
                                    <?= $product['data']['status'] == 1 ? 'HIDDEN - Not visible to customers' : 'VISIBLE - Shown to customers'; ?>
                                </span>
                            </label>
                        </div>
                        <small class="text-muted">Toggle the switch to change visibility</small>
                    </div>

                    <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const statusSwitch = document.getElementById('statusSwitch');
                        const statusText = document.getElementById('statusText');

                        statusSwitch.addEventListener('change', function() {
                            if (this.checked) {
                                statusText.textContent = 'HIDDEN - Not visible to customers';
                                statusText.style.color = 'red';
                            } else {
                                statusText.textContent = 'VISIBLE - Shown to customers';
                                statusText.style.color = 'green';
                            }
                        });
                    });
                    </script>
                    <!-- Submit button -->
                    <div class="col-md-6">
                        <button type="submit" name="updateProduct" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Update Product
                        </button>
                    </div>
                </div>

            <?php
                }
                else
                {
                    echo '<h5>'.$product['message'].'</h5>';
                }
                ?>

            </form>

        </div>
    </div>
</div>

<!-- CKEditor CDN -->
<script src="https://cdn.ckeditor.com/ckeditor5/38.1.0/classic/ckeditor.js"></script>

<script>
// Calculate price function
function calculatePrice() {
    const costPrice = parseFloat(document.getElementById("cost_price")?.value) || 0;
    const salesPrice = parseFloat(document.getElementById("sales_price")?.value) || 0;
    const vat = parseFloat(document.getElementById("vat_percentage")?.value) || 0;

    // Check if discounted price is enabled and has a value
    const enableDiscountedPrice = document.getElementById("enableDiscountedPrice")?.checked;
    const discountedPriceInput = document.getElementById("discounted_price");
    const discountedPrice = enableDiscountedPrice && discountedPriceInput ? parseFloat(discountedPriceInput.value) || 0 : 0;

    // Use discounted price if enabled, otherwise use sales price
    const basePrice = enableDiscountedPrice && discountedPrice > 0 ? discountedPrice : salesPrice;

    const vatAmount = basePrice * (vat/100);
    const finalPrice = basePrice + vatAmount;

    // Update the final price display
    const finalPriceDisplay = document.getElementById("finalPriceDisplay");
    if (finalPriceDisplay) {
        finalPriceDisplay.textContent = `R ${finalPrice.toFixed(2)}`;
    }

    // Update the discounted price info
    const discountedPriceInfo = document.getElementById("discountedPriceInfo");
    const originalPriceDisplay = document.getElementById("originalPriceDisplay");

    if (discountedPriceInfo && originalPriceDisplay) {
        if (enableDiscountedPrice && discountedPrice > 0 && salesPrice > 0) {
            // Show the discounted price info
            discountedPriceInfo.style.display = "block";

            // Calculate the original price with VAT
            const originalVatAmount = salesPrice * (vat/100);
            const originalFinalPrice = salesPrice + originalVatAmount;

            // Display the original price
            originalPriceDisplay.textContent = `R ${originalFinalPrice.toFixed(2)}`;
        } else {
            // Hide the discounted price info
            discountedPriceInfo.style.display = "none";
        }
    }

    // Calculate and display savings if discounted price is enabled
    if (enableDiscountedPrice && discountedPrice > 0 && salesPrice > 0) {
        const savingsAmount = salesPrice - discountedPrice;
        const savingsPercentage = (savingsAmount / salesPrice) * 100;

        const savingsAmountElement = document.getElementById("savings_amount");
        const savingsPercentageElement = document.getElementById("savings_percentage");

        if (savingsAmountElement) {
            savingsAmountElement.value = savingsAmount.toFixed(2);
        }

        if (savingsPercentageElement) {
            savingsPercentageElement.textContent = `${savingsPercentage.toFixed(0)}% Off`;
        }
    }
}

// Initialize CKEditor and other functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor
    ClassicEditor
        .create(document.querySelector('#editor'), {
            toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote'],
            heading: {
                options: [
                    { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                    { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                ]
            }
        })
        .catch(error => {
            console.error(error);
        });

    // Product Type Toggle
    const productTypeSelect = document.getElementById('productType');
    const canBeComponentCheckbox = document.getElementById('canBeComponent');
    const buildableOptionsContainer = document.getElementById('buildableOptions');

    // Get the container of the canBeComponent checkbox
    const canBeComponentContainer = canBeComponentCheckbox.closest('.form-check');

    productTypeSelect.addEventListener('change', function() {
        // If product type is Component (id=3), disable the can_be_component checkbox
        if (this.value === '3') {
            canBeComponentCheckbox.checked = false;
            canBeComponentCheckbox.disabled = true;
            canBeComponentContainer.style.display = 'block'; // Show the checkbox but disabled
            if (buildableOptionsContainer) buildableOptionsContainer.style.display = 'none';
        } else if (this.value === '2') { // Buildable product
            canBeComponentCheckbox.checked = false;
            canBeComponentCheckbox.disabled = true;
            canBeComponentContainer.style.display = 'none'; // Hide the checkbox completely
            if (buildableOptionsContainer) buildableOptionsContainer.style.display = 'block';
        } else { // Standard product
            canBeComponentCheckbox.disabled = false;
            canBeComponentContainer.style.display = 'block'; // Show the checkbox
            if (buildableOptionsContainer) buildableOptionsContainer.style.display = 'none';
        }
    });

    // Trigger the change event to set initial state
    productTypeSelect.dispatchEvent(new Event('change'));

    // Discounted Price Toggle
    const enableDiscountedPriceCheckbox = document.getElementById('enableDiscountedPrice');
    const discountedPriceContainer = document.querySelector('.discounted-price-container');

    if (enableDiscountedPriceCheckbox && discountedPriceContainer) {
        enableDiscountedPriceCheckbox.addEventListener('change', function() {
            discountedPriceContainer.style.display = this.checked ? 'block' : 'none';
            calculatePrice(); // Recalculate price when toggling
        });
    }

    // Add event listeners to price fields
    const priceFields = ['cost_price', 'sales_price', 'vat_percentage', 'discounted_price'];
    priceFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', calculatePrice);
            field.addEventListener('change', calculatePrice);
        }
    });

    // Initialize price calculation
    calculatePrice();

    // Variants management moved to separate page (product-variants.php)

    // Bulk Pricing Toggle
    const enableBulkPricingCheckbox = document.getElementById('enableBulkPricing');
    const bulkPricingContainer = document.querySelector('.bulk-pricing-container');

    if (bulkPricingContainer) {
        enableBulkPricingCheckbox.addEventListener('change', function() {
            if (this.checked) {
                bulkPricingContainer.style.display = 'block';
            } else {
                bulkPricingContainer.style.display = 'none';
            }
        });

        // Set initial state
        bulkPricingContainer.style.display = enableBulkPricingCheckbox.checked ? 'block' : 'none';

        // Add Bulk Pricing Row
        const addBulkPricingRowBtn = document.getElementById('addBulkPricingRow');
        const bulkPricingTableBody = document.getElementById('bulkPricingTableBody');

        if (addBulkPricingRowBtn && bulkPricingTableBody) {
            addBulkPricingRowBtn.addEventListener('click', function() {
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td>
                        <div class="input-group">
                            <input type="number" name="bulk_min_qty[]" class="form-control" placeholder="Min" min="2">
                            <span class="input-group-text">-</span>
                            <input type="number" name="bulk_max_qty[]" class="form-control" placeholder="Max" min="5">
                        </div>
                    </td>
                    <td>
                        <input type="number" step="0.01" name="bulk_price[]" class="form-control" placeholder="Price per unit">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-bulk-row">Remove</button>
                    </td>
                `;

                bulkPricingTableBody.appendChild(newRow);

                // Add event listener to the new remove button
                newRow.querySelector('.remove-bulk-row').addEventListener('click', function() {
                    this.closest('tr').remove();
                });
            });

            // Remove Bulk Pricing Row
            document.querySelectorAll('.remove-bulk-row').forEach(function(button) {
                button.addEventListener('click', function() {
                    this.closest('tr').remove();
                });
            });
        }
    }
});
</script>



<?php include('../../includes/footer.php'); ?>
