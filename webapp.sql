-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Apr 14, 2025 at 08:11 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `webapp`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int(11) NOT NULL,
  `name` varchar(191) NOT NULL,
  `email` varchar(191) NOT NULL,
  `password` varchar(191) NOT NULL,
  `phone` varchar(191) DEFAULT NULL,
  `is_ban` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=not_ban,1=ban',
  `user_type` varchar(255) NOT NULL,
  `created_at` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `name`, `email`, `password`, `phone`, `is_ban`, `user_type`, `created_at`) VALUES
(4, 'Taariq', '<EMAIL>', '$2y$10$13Fxit5XS7sH9fG6rZSkEeRFoNRQ9Wo25Xq7aH0C5x9aFblwRBxJG', '0797869698', 0, 'admin', '2024-03-07'),
(5, 'Yasmin', '<EMAIL>', '$2y$10$YEdDH6VzSJxdneinb2kiHutChx4HtEKL/2ZkdKzCb1cl4P.lGolYy', '0797869698', 0, 'user', '0000-00-00'),
(6, 'testAdmin', '<EMAIL>', 'canton786', 'admin', 0, '', '2024-11-30 19:47:02');

-- --------------------------------------------------------

--
-- Table structure for table `admin_login_history`
--

CREATE TABLE `admin_login_history` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `login_time` datetime NOT NULL,
  `ip_address` varchar(50) NOT NULL,
  `user_agent` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_login_history`
--

INSERT INTO `admin_login_history` (`id`, `admin_id`, `login_time`, `ip_address`, `user_agent`) VALUES
(1, 4, '2025-04-08 12:09:22', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(2, 4, '2025-04-08 12:12:30', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(3, 4, '2025-04-09 10:03:09', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(4, 4, '2025-04-10 13:11:34', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(5, 4, '2025-04-12 18:21:00', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
(6, 4, '2025-04-14 07:43:50', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');

-- --------------------------------------------------------

--
-- Table structure for table `backup_history`
--

CREATE TABLE `backup_history` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_name` varchar(255) NOT NULL,
  `backup_date` datetime NOT NULL,
  `backup_filename` varchar(255) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `backup_history`
--

INSERT INTO `backup_history` (`id`, `user_id`, `user_name`, `backup_date`, `backup_filename`, `notes`) VALUES
(1, 0, 'Taariq', '2025-04-10 13:48:57', 'webapp_backup_2025-04-10.sql', NULL),
(2, 0, 'Taariq', '2025-04-10 13:52:09', 'webapp_backup_2025-04-10.sql', NULL),
(3, 0, 'Taariq', '2025-04-10 13:53:32', 'webapp_backup_2025-04-10.sql', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `call_log`
--

CREATE TABLE `call_log` (
  `id` int(11) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `number_called` varchar(255) NOT NULL,
  `date` date NOT NULL,
  `time` time NOT NULL,
  `call_log_note` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `call_log`
--

INSERT INTO `call_log` (`id`, `company_name`, `number_called`, `date`, `time`, `call_log_note`) VALUES
(1, 'ABC Technologies', '0*********', '2024-03-20', '09:30:00', 'Discussed website development project. Client interested in e-commerce solution.'),
(2, 'XYZ Solutions', '0827654321', '2024-03-20', '11:15:00', 'Follow-up call about hosting services. Need to send pricing proposal.'),
(3, 'Global Traders', '0113456789', '2024-03-19', '14:45:00', 'Technical support call. Resolved email configuration issues.'),
(4, 'Smart Systems', '0824567890', '2024-03-19', '16:20:00', 'New client inquiry about web design services. Scheduled meeting for next week.'),
(5, 'Tech Innovators', '0785556666', '2024-03-18', '10:00:00', 'Maintenance contract discussion. Client will review proposal.'),
(6, 'Digital Solutions', '0829876543', '2024-03-18', '13:30:00', 'Website update request. Added to project queue.'),
(7, 'Quick Services', '0112223333', '2024-03-17', '15:45:00', 'Domain renewal reminder call. Payment processed.'),
(8, 'Market Masters', '0836667777', '2024-03-17', '09:15:00', 'SEO service inquiry. Sent information package.'),
(9, 'Cloud Systems', '0784445555', '2024-03-16', '11:45:00', 'Cloud hosting migration discussion. Schedule technical assessment.'),
(10, 'Web Experts', '0821112222', '2024-03-16', '14:00:00', 'Website security audit request. Quoted services.');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` mediumtext DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `description`, `parent_id`, `status`) VALUES
(1, 'Category Product', 'Category Product', NULL, 0),
(2, 'Service Category 2008', 'Service Category Description', NULL, 1),
(3, 'Add Category 008', 'Add Category 008', NULL, 0),
(4, 'Test Category 20250410144326', 'This is a test category for testing the category filter', NULL, 0),
(5, 'Computer', 'Computer', NULL, 0),
(6, 'Memory', 'For RAM modules', 5, 0),
(7, 'Processors', 'For CPU components', 5, 0),
(8, 'Storage', 'For SSDs and HDDs', 5, 0),
(9, 'Graphics Cards', 'For GPUs', 5, 0),
(10, 'Power Supplies', 'For PSUs', 5, 0),
(11, 'Custom Build', 'Custom Build Your own pc', 5, 0);

-- --------------------------------------------------------

--
-- Table structure for table `comments`
--

CREATE TABLE `comments` (
  `comment_id` int(11) NOT NULL,
  `content` text NOT NULL,
  `post_id` int(11) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contacts`
--

CREATE TABLE `contacts` (
  `id` int(11) NOT NULL,
  `contact_name` varchar(255) NOT NULL,
  `contact_number` varchar(255) NOT NULL,
  `contact_email` varchar(255) NOT NULL,
  `contact_description` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `contacts`
--

INSERT INTO `contacts` (`id`, `contact_name`, `contact_number`, `contact_email`, `contact_description`) VALUES
(1, 'Doctor update', '031*********', '<EMAIL>', 'Doc');

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `client_code` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`id`, `name`, `email`, `phone`, `status`, `client_code`) VALUES
(1, 'taariq', '<EMAIL>', '0797869698', 0, NULL),
(2, 'test add customer', '<EMAIL>', '0797869698', 0, NULL),
(3, 'Add Customer Test 0072', '<EMAIL>', '*********', 0, NULL),
(4, 'Sarah Johnson', '<EMAIL>', '0829876543', 0, 'CT778235'),
(5, 'Quotation Test', '<EMAIL>', '0797869690', 0, 'CT302125'),
(6, 'uk 2', '<EMAIL>', '*********0', 0, 'CT099283'),
(7, '007uk', '<EMAIL>', '*********', 0, 'CT987365');

-- --------------------------------------------------------

--
-- Table structure for table `custom_product_builds`
--

CREATE TABLE `custom_product_builds` (
  `id` int(11) NOT NULL,
  `buildable_product_id` int(11) NOT NULL,
  `session_id` varchar(255) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `custom_product_build_items`
--

CREATE TABLE `custom_product_build_items` (
  `id` int(11) NOT NULL,
  `build_id` int(11) NOT NULL,
  `component_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `date` tinytext NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `tracking_no` varchar(100) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `vat_amount` decimal(10,2) NOT NULL,
  `order_status` varchar(100) DEFAULT NULL,
  `payment_mode` varchar(50) NOT NULL DEFAULT 'cash' COMMENT 'cash, online, card',
  `payment_status` varchar(20) NOT NULL DEFAULT 'Not Paid' COMMENT 'Not Paid, Paid',
  `order_placed_by_id` int(11) DEFAULT NULL,
  `issue_date` datetime DEFAULT NULL COMMENT 'Date when invoice was generated',
  `payment_date` datetime DEFAULT NULL COMMENT 'Date when payment was marked as paid'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `date`, `name`, `phone`, `customer_id`, `tracking_no`, `total_amount`, `vat_amount`, `order_status`, `payment_mode`, `payment_status`, `order_placed_by_id`, `issue_date`, `payment_date`) VALUES
(1, '2025-03-26', 'taariq', '0797869698', 1, '95546', 0.00, 0.00, 'Processing', 'cash', 'Not Paid', 1, NULL, NULL),
(2, '2025-03-26', 'taariq', '0797869698', 1, '45480', 0.00, 0.00, 'Pending', 'cash', 'Not Paid', 1, NULL, NULL),
(3, '2025-03-26', 'taariq', '0797869698', 1, '19938', 0.00, 0.00, 'Pending', 'cash', 'Not Paid', 1, NULL, NULL),
(4, '2025-03-26 21:16:10', 'test add customer', '0797869698', 2, '27636', 1275.95, 172.50, 'Pending', 'cash', 'Paid', 4, NULL, NULL),
(5, '2025-03-27 12:13:04', 'test add customer', '0797869698', 2, '49370', 1204.75, 86.25, 'Processing', 'cash', 'Paid', 1, NULL, '2025-03-27 18:04:56'),
(6, '2025-03-27 18:08:53', 'test add customer', '0797869698', 2, '70255', 629.75, 0.00, 'Pending', 'cash', 'Not Paid', 1, '2025-03-27 18:08:53', NULL),
(7, '2025-04-07 16:30:47', 'test add customer', '0797869698', 2, '78354', 5565.00, 86.25, 'Pending', 'card', 'Paid', 1, '2025-04-07 16:30:47', '2025-04-08 14:21:48'),
(10, '2025-04-09 14:57:18', 'Quotation Test', '0797869690', 5, 'TRK917588', 895.95, 0.00, 'pending', 'cash', 'Not Paid', 1, '2025-04-09 14:57:18', NULL),
(11, '2025-04-09 15:01:33', 'Quotation Test', '0797869690', 5, 'TRK719456', 875.95, 0.00, 'pending', 'cash', 'Not Paid', 1, '2025-04-09 15:01:33', NULL),
(12, '2025-04-09 15:01:59', 'Quotation Test', '0797869690', 5, 'TRK903643', 875.95, 0.00, 'pending', 'cash', 'Not Paid', 1, '2025-04-09 15:01:59', NULL),
(13, '2025-04-09 15:07:27', 'Quotation Test', '0797869690', 5, 'TRK683210', 1275.95, 0.00, 'pending', 'cash', 'Not Paid', 1, '2025-04-09 15:07:27', NULL),
(14, '2025-04-09 15:57:29', 'Add Customer Test 0072', '*********', 3, 'TRK664910', 1150.00, 0.00, NULL, 'cash', 'Not Paid', NULL, NULL, NULL),
(15, '2025-04-09 16:06:41', '007uk', '*********', 7, 'TRK373062', 2841.90, 0.00, 'pending', 'cash', 'Not Paid', 1, '2025-04-09 16:06:41', NULL),
(16, '2025-04-11 09:51:06', 'balloon', '*********0', 6, 'TRK810844', 1675.95, 0.00, 'pending', 'cash', 'Not Paid', 1, '2025-04-11 09:51:06', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `tracking_no` varchar(11) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `vat` varchar(11) NOT NULL,
  `price` varchar(100) NOT NULL,
  `quantity` int(11) NOT NULL,
  `date` date DEFAULT NULL,
  `product_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `tracking_no`, `item_name`, `vat`, `price`, `quantity`, `date`, `product_id`) VALUES
(1, 4, '27636', 'Product', '0', '125.95', 1, '2025-03-26', 1),
(2, 4, '27636', 'Web Development', '15', '1150', 1, '2025-03-26', 3),
(3, 5, '49370', 'Product', '0', '125.95', 5, '2025-03-27', 1),
(4, 5, '49370', 'SEO Optimization', '15', '575', 1, '2025-03-27', 4),
(5, 6, '70255', 'Product', '0', '125.95', 5, '2025-03-27', 1),
(6, 7, '78354', 'Add Product Image 007', '0', '2495', 2, '2025-04-07', 4),
(7, 7, '78354', 'SEO Optimization', '15', '575', 1, '2025-04-07', 4),
(8, 10, 'TRK917588', 'Add Product', '0', '325', 2, '2025-04-09', 3),
(9, 10, 'TRK917588', 'Product', '0', '125', 1, '2025-04-09', 1),
(10, 11, 'TRK719456', 'Add Product', '0', '325', 2, '2025-04-09', 3),
(11, 11, 'TRK719456', 'Product', '0', '125', 1, '2025-04-09', 1),
(12, 12, 'TRK903643', 'Add Product', '0', '325', 2, '2025-04-09', 3),
(13, 12, 'TRK903643', 'Product', '0', '125', 1, '2025-04-09', 1),
(14, 13, 'TRK683210', 'Add Product', '0', '325', 2, '2025-04-09', 3),
(15, 13, 'TRK683210', 'Product', '0', '125', 1, '2025-04-09', 1),
(16, 13, 'TRK683210', 'Delivery Fee', '0', '500', 1, '2025-04-09', 0),
(17, 14, 'TRK664910', 'Delivery Fee', '0', '250', 1, '2025-04-09', 0),
(18, 14, 'TRK664910', 'Add Product', '0', '325', 1, '2025-04-09', 3),
(19, 14, 'TRK664910', 'SEO Optimization', '0', '575', 1, '2025-04-09', 4),
(20, 15, 'TRK373062', 'Add Product Test', '0', '1295', 2, '2025-04-09', 6),
(21, 15, 'TRK373062', 'Delivery Fee', '0', '250', 1, '2025-04-09', 0),
(22, 16, 'TRK810844', 'Add Product Test', '0', '1295', 1, '2025-04-11', 6),
(23, 16, 'TRK810844', 'colour product test', '0', '130', 1, '2025-04-11', 10),
(24, 16, 'TRK810844', 'Delivery Fee', '0', '250', 1, '2025-04-11', 0);

-- --------------------------------------------------------

--
-- Table structure for table `order_report`
--

CREATE TABLE `order_report` (
  `id` int(11) NOT NULL,
  `invoice_number` varchar(255) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `tracking_number` varchar(255) DEFAULT NULL,
  `total_amount` decimal(10,2) DEFAULT NULL,
  `order_status` varchar(255) DEFAULT NULL,
  `payment_status` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `category_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `barcode` varchar(255) NOT NULL DEFAULT '0000',
  `cost_price` decimal(10,2) NOT NULL,
  `sales_price` decimal(10,2) NOT NULL,
  `vat_percentage` int(11) NOT NULL DEFAULT 0,
  `vatT` decimal(10,2) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `image` varchar(255) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` varchar(255) NOT NULL DEFAULT '0000-00-00',
  `featured` tinyint(1) DEFAULT 0,
  `is_featured` tinyint(1) DEFAULT 0,
  `product_type_id` int(11) DEFAULT 1 COMMENT '1=Standard, 2=Buildable, 3=Component',
  `can_be_component` tinyint(1) DEFAULT 0 COMMENT '0=No, 1=Yes',
  `enable_variants` tinyint(4) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `category_id`, `name`, `description`, `barcode`, `cost_price`, `sales_price`, `vat_percentage`, `vatT`, `price`, `quantity`, `image`, `status`, `created_at`, `featured`, `is_featured`, `product_type_id`, `can_be_component`, `enable_variants`) VALUES
(1, '1', 'Product', 'Description', '0070072', 99.95, 125.95, 0, 0.00, 125.95, 25, '1743180631_productplaceholder.jpg', 0, '0000-00-00', 1, 0, 1, 0, 0),
(2, '1', 'Product 2', '<p><i>Product 2 </i><br><strong>Product 2 </strong></p>', '0070072', 99.95, 125.95, 0, 0.00, 125.95, 50, '1743180631_productplaceholder.jpg', 0, '0000-00-00', 1, 0, 1, 0, 0),
(3, '1', 'Add Product', '&lt;h4&gt;&lt;i&gt;Add Product&lt;/i&gt;&lt;/h4&gt;', '1234', 220.00, 325.00, 0, 0.00, 325.00, 49, '1743180631_productplaceholder.jpg', 0, '0000-00-00', 1, 0, 1, 0, 0),
(4, '1', 'Add Product Image 007', '&lt;p&gt;Add Product Image&lt;/p&gt;', '0000', 19995.00, 2495.00, 0, 0.00, 2495.00, 5, '1743181337_productplaceholder.jpg', 0, '0000-00-00', 1, 0, 1, 0, 0),
(5, '3', 'Product add 002', 'Product add 002', '12345', 19995.95, 2495.95, 0, 0.00, 2495.95, 25, '1743239186_productplaceholder.jpg', 1, '0000-00-00', 1, 0, 1, 0, 0),
(6, '1', 'Add Product Test', '&lt;p&gt;Description&lt;br&gt;&lt;strong&gt;Description&lt;/strong&gt;&lt;/p&gt;', '0007', 995.95, 1295.95, 0, 0.00, 0.00, 25, '1744113797_producthere.jpg', 0, '0000-00-00', 1, 0, 1, 0, 0),
(7, '1', 'Test Product - Category 1', 'This is a test product in Category 1 for testing the category filter.', '0000', 100.00, 115.00, 15, 17.25, 132.25, 10, 'default.jpg', 0, '2025-04-10 14:42:16', 1, 0, 1, 0, 0),
(8, '3', 'Test Product - Category 3', 'This is a test product in Category 3 for testing the category filter.', '0000', 100.00, 115.00, 15, 17.25, 132.25, 10, 'default.jpg', 0, '2025-04-10 14:42:16', 1, 0, 1, 0, 0),
(9, '4', 'Product in Test Category 20250410144326', 'This is a test product in the new test category', '0000', 100.00, 115.00, 15, 17.25, 132.25, 10, 'default.jpg', 0, '2025-04-10 14:43:26', 1, 0, 1, 0, 0),
(10, '3', 'colour product test', '<p>colour <i>product</i> <strong>test</strong></p>', '0007', 125.00, 125.00, 0, 0.00, 0.00, 25, '1744292596_black-dress.webp', 0, '0000-00-00', 1, 0, 1, 0, 0),
(11, '7', 'Intel Core i5-12600K Processor', 'The Intel Core i5-12600K is a high-performance desktop processor with 10 cores (6P+4E) and 16 threads. Features include 3.7 GHz base frequency, 4.9 GHz max turbo, 20MB cache, and support for DDR5 and PCIe 5.0.', 'CPU-I5-12600K', 3500.00, 4200.00, 0, 0.00, 4200.00, 10, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(12, '7', 'AMD Ryzen 7 5800X Processor', 'The AMD Ryzen 7 5800X is an 8-core, 16-thread processor with 3.8 GHz base clock, 4.7 GHz max boost, 36MB total cache, and 105W TDP. Excellent for gaming and content creation.', 'CPU-AMD-R7', 3800.00, 4500.00, 0, 0.00, 4500.00, 8, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(13, '6', 'Corsair Vengeance RGB Pro 16GB (2x8GB) DDR4 3200MHz', 'Corsair Vengeance RGB Pro 16GB (2x8GB) DDR4 3200MHz memory kit with dynamic multi-zone RGB lighting. CL16 latency and aluminum heat spreader.', 'MEM-CORSAIR-16GB', 1200.00, 1500.00, 0, 0.00, 1500.00, 15, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(14, '6', 'Kingston HyperX Fury 32GB (2x16GB) DDR4 3600MHz', 'Kingston HyperX Fury 32GB (2x16GB) DDR4 3600MHz memory kit with low-profile heat spreader. CL17 latency and XMP 2.0 support.', 'MEM-KINGSTON-32GB', 2000.00, 2400.00, 0, 0.00, 2400.00, 10, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(15, '8', 'Samsung 970 EVO Plus 1TB NVMe SSD', 'Samsung 970 EVO Plus 1TB NVMe M.2 SSD with sequential read speeds up to 3,500 MB/s and write speeds up to 3,300 MB/s. Perfect for gaming and content creation.', 'SSD-SAMSUNG-1TB', 1800.00, 2200.00, 0, 0.00, 2200.00, 12, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(16, '8', 'Seagate Barracuda 2TB 7200RPM HDD', 'Seagate Barracuda 2TB 3.5-inch internal hard drive with 7200RPM and 256MB cache. Reliable mass storage solution for your data.', 'HDD-SEAGATE-2TB', 800.00, 1000.00, 0, 0.00, 1000.00, 15, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(17, '9', 'NVIDIA GeForce RTX 3070 8GB', 'NVIDIA GeForce RTX 3070 with 8GB GDDR6 memory, ray tracing cores, and DLSS AI acceleration. Excellent 1440p and 4K gaming performance.', 'GPU-NVIDIA-3070', 8000.00, 9500.00, 0, 0.00, 9500.00, 5, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(18, '9', 'AMD Radeon RX 6700 XT 12GB', 'AMD Radeon RX 6700 XT with 12GB GDDR6 memory, ray accelerators, and AMD Infinity Cache. Great for high-refresh 1440p gaming.', 'GPU-AMD-6700XT', 7000.00, 8200.00, 0, 0.00, 8200.00, 6, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(19, '10', 'Corsair RM750x 750W 80+ Gold PSU', 'Corsair RM750x 750W power supply with 80+ Gold efficiency, fully modular cables, and silent operation. 10-year warranty included.', 'PSU-CORSAIR-750W', 1500.00, 1800.00, 0, 0.00, 1800.00, 10, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(20, '10', 'EVGA SuperNOVA 850W 80+ Platinum PSU', 'EVGA SuperNOVA 850W power supply with 80+ Platinum efficiency, fully modular design, and ECO mode for silent operation at low loads.', 'PSU-EVGA-850W', 2000.00, 2400.00, 0, 0.00, 2400.00, 8, 'default.png', 0, '0000-00-00', 0, 0, 1, 1, 0),
(21, '5', 'Custom PC', 'Build your own custom PC by selecting components that meet your specific needs.', 'CUSTOM-PC', 0.00, 0.00, 0, 0.00, 0.00, 10, 'default.png', 0, '0000-00-00', 1, 0, 2, 0, 0),
(22, '1', 'Test Product 20250413140739', 'This is a test product created on 2025-04-13 14:07:39', 'TEST9702', 100.00, 150.00, 0, 0.00, 150.00, 10, 'default.jpg', 0, '2025-04-13 14:07:39', 0, 0, 1, 0, 0),
(23, '1', 'Test Product 20250413142536', 'This is a test product created on 2025-04-13 14:25:36', 'TEST5493', 100.00, 150.00, 0, 0.00, 150.00, 10, 'default.jpg', 0, '2025-04-13 14:25:36', 0, 0, 1, 0, 0),
(24, '1', 'Very Simple Product 20250413143322', 'This is a test product created on 2025-04-13 14:33:22', 'TEST9206', 100.00, 150.00, 0, 0.00, 150.00, 10, 'default.jpg', 0, '2025-04-13 14:33:22', 0, 0, 1, 0, 0),
(25, '1', 'Direct SQL Product 20250413144757', 'This is a test product created on 2025-04-13 14:47:57', 'TEST3368', 100.00, 150.00, 0, 0.00, 150.00, 10, 'default.jpg', 0, '2025-04-13 14:47:57', 0, 0, 1, 0, 0),
(26, '1', 'Dress', '', '0007', 200.00, 600.00, 0, 0.00, 600.00, 25, '1744548772_producthere.jpg', 0, '2025-04-13 14:52:52', 0, 0, 1, 0, 0),
(27, '3', 'Dress Images', '', '0000', 200.00, 600.00, 0, 0.00, 600.00, 25, '1744549275_producthere.jpg', 0, '2025-04-13 15:01:15', 0, 0, 1, 0, 0),
(28, '1', 'Dress Labels', '<p>Description</p>', '0007', 200.00, 600.00, 0, 0.00, 600.00, 25, '1744549519_black-dress.webp', 0, '2025-04-13 15:05:19', 0, 0, 1, 0, 0),
(29, '1', 'dress size and colour', '<p>Description</p>', '0009', 200.00, 600.00, 0, 0.00, 600.00, 36, '1744549909_black-dress.webp', 0, '2025-04-13 15:11:49', 0, 0, 1, 0, 1),
(30, '1', 'Dress All', '<p>Description</p>', '0007', 190.00, 590.00, 0, 0.00, 520.00, 25, '1744610541_black-dress.webp', 0, '2025-04-14 08:02:21', 0, 0, 1, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `product_bulk_pricing`
--

CREATE TABLE `product_bulk_pricing` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `min_quantity` int(11) NOT NULL,
  `max_quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_bulk_pricing`
--

INSERT INTO `product_bulk_pricing` (`id`, `product_id`, `min_quantity`, `max_quantity`, `price`, `created_at`) VALUES
(7, 10, 1, 4, 125.00, '2025-04-10 17:27:17'),
(8, 10, 5, 9, 115.00, '2025-04-10 17:27:17'),
(9, 10, 10, 14, 107.00, '2025-04-10 17:27:17');

-- --------------------------------------------------------

--
-- Table structure for table `product_colors`
--

CREATE TABLE `product_colors` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `color_code` varchar(20) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_colors`
--

INSERT INTO `product_colors` (`id`, `name`, `color_code`, `description`, `status`, `created_at`) VALUES
(1, 'Red', '#FF0000', 'Red Colour', 0, '2025-04-10 13:28:15'),
(2, 'Green', '#00FF00', 'Green', 0, '2025-04-10 13:28:44'),
(3, 'black', '#000000', 'black', 0, '2025-04-10 17:26:08');

-- --------------------------------------------------------

--
-- Table structure for table `product_components`
--

CREATE TABLE `product_components` (
  `id` int(11) NOT NULL,
  `buildable_product_id` int(11) NOT NULL COMMENT 'ID of the buildable product',
  `component_category_id` int(11) NOT NULL COMMENT 'Category ID of allowed components',
  `min_required` int(11) NOT NULL DEFAULT 1 COMMENT 'Minimum number of components required',
  `max_allowed` int(11) NOT NULL DEFAULT 1 COMMENT 'Maximum number of components allowed',
  `is_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=Optional, 1=Required',
  `display_order` int(11) NOT NULL DEFAULT 0 COMMENT 'Order to display in builder interface',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_components`
--

INSERT INTO `product_components` (`id`, `buildable_product_id`, `component_category_id`, `min_required`, `max_allowed`, `is_required`, `display_order`, `created_at`) VALUES
(6, 21, 7, 1, 1, 1, 1, '2025-04-12 17:07:44'),
(7, 21, 6, 1, 4, 1, 2, '2025-04-12 17:07:44'),
(8, 21, 8, 1, 2, 1, 3, '2025-04-12 17:07:44'),
(9, 21, 9, 0, 1, 0, 4, '2025-04-12 17:07:44'),
(10, 21, 10, 1, 1, 1, 5, '2025-04-12 17:07:44');

-- --------------------------------------------------------

--
-- Table structure for table `product_component_products`
--

CREATE TABLE `product_component_products` (
  `id` int(11) NOT NULL,
  `component_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_images`
--

CREATE TABLE `product_images` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `image` varchar(255) NOT NULL,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_images`
--

INSERT INTO `product_images` (`id`, `product_id`, `image`, `display_order`, `created_at`) VALUES
(1, 27, '1744549275_additional_0_reddress1.jpg', 1, '2025-04-13 13:01:15'),
(2, 27, '1744549275_additional_1_reddress2.jpg', 2, '2025-04-13 13:01:15'),
(3, 27, '1744549275_additional_2_reddress3.jpg', 3, '2025-04-13 13:01:15'),
(4, 28, '1744549519_additional_0_reddress1.jpg', 1, '2025-04-13 13:05:19'),
(5, 28, '1744549519_additional_1_reddress2.jpg', 2, '2025-04-13 13:05:19'),
(6, 28, '1744549519_additional_2_reddress3.jpg', 3, '2025-04-13 13:05:19'),
(7, 29, '1744549909_additional_0_reddress1.jpg', 1, '2025-04-13 13:11:49'),
(8, 29, '1744549909_additional_1_reddress2.jpg', 2, '2025-04-13 13:11:49'),
(10, 30, '1744610541_additional_0_reddress2.jpg', 1, '2025-04-14 06:02:21'),
(11, 30, '1744610541_additional_1_reddress3.jpg', 2, '2025-04-14 06:02:21'),
(12, 30, '1744610541_additional_2_reddress4.jpg', 3, '2025-04-14 06:02:21');

-- --------------------------------------------------------

--
-- Table structure for table `product_labels`
--

CREATE TABLE `product_labels` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `background_color` varchar(20) NOT NULL DEFAULT '#ff0000',
  `text_color` varchar(20) NOT NULL DEFAULT '#ffffff',
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_labels`
--

INSERT INTO `product_labels` (`id`, `name`, `background_color`, `text_color`, `status`, `created_at`) VALUES
(1, 'New', '#28a745', '#ffffff', 0, '2025-04-12 17:45:23'),
(2, 'Sale', '#dc3545', '#ffffff', 0, '2025-04-12 17:45:23'),
(3, 'Special', '#ffc107', '#000000', 0, '2025-04-12 17:45:23'),
(4, 'Featured', '#17a2b8', '#ffffff', 0, '2025-04-12 17:45:23');

-- --------------------------------------------------------

--
-- Table structure for table `product_label_assignments`
--

CREATE TABLE `product_label_assignments` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `label_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_label_assignments`
--

INSERT INTO `product_label_assignments` (`id`, `product_id`, `label_id`, `created_at`) VALUES
(1, 28, 1, '2025-04-13 13:05:19'),
(2, 29, 3, '2025-04-13 13:11:49'),
(3, 30, 2, '2025-04-14 06:02:21');

-- --------------------------------------------------------

--
-- Table structure for table `product_more_information`
--

CREATE TABLE `product_more_information` (
  `id` int(11) NOT NULL,
  `product_name` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `number` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `read_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=Unread, 1=Read'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_more_information`
--

INSERT INTO `product_more_information` (`id`, `product_name`, `name`, `number`, `email`, `message`, `created_at`, `read_status`) VALUES
(1, 'Product', 'John Smith', '0721234567', '<EMAIL>', 'I would like more information about the product specifications and availability.', '2024-03-15 09:30:00', 0),
(2, 'Product 2', 'Sarah Johnson', '0829876543', '<EMAIL>', 'Interested in bulk pricing for Product 2. Please provide details.', '2024-03-15 10:45:00', 0),
(3, 'Product', 'Michael Brown', '0731112222', '<EMAIL>', 'When will this product be back in stock? Looking to place an order soon.', '2024-03-14 14:20:00', 0),
(4, 'Product 2', 'Emma Davis', '0842223333', '<EMAIL>', 'Do you offer installation services with this product?', '2024-03-14 16:15:00', 0),
(5, 'Product', 'David Wilson', '0753334444', '<EMAIL>', 'Requesting a demo of this product. Please contact me.', '2024-03-13 11:30:00', 0),
(6, 'Product 2', 'Lisa Anderson', '0864445555', '<EMAIL>', 'Need technical specifications and warranty information.', '2024-03-13 13:45:00', 0),
(7, 'Product', 'James Taylor', '0775556666', '<EMAIL>', 'Is this product compatible with existing systems?', '2024-03-12 09:00:00', 0),
(8, 'Product 2', 'Rachel Green', '0886667777', '<EMAIL>', 'Looking for customization options for this product.', '2024-03-12 10:30:00', 0),
(9, 'Product', 'Peter Parker', '0797778888', '<EMAIL>', 'What are the maintenance requirements for this product?', '2024-03-11 15:20:00', 0),
(10, 'Product 2', 'Mary Johnson', '0908889999', '<EMAIL>', 'Interested in volume discounts. Please provide pricing tiers.', '2024-03-11 16:45:00', 0),
(11, 'Add Product (ID:3)', '88Test', '0797869698', '<EMAIL>', 'Inquire About Add Product', '2025-04-08 18:40:00', 0),
(12, 'Add Product (ID:3)', 'uk 2', '*********0', '<EMAIL>', 'TEST', '2025-04-09 13:47:46', 1);

-- --------------------------------------------------------

--
-- Table structure for table `product_secondary_categories`
--

CREATE TABLE `product_secondary_categories` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_sizes`
--

CREATE TABLE `product_sizes` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_sizes`
--

INSERT INTO `product_sizes` (`id`, `name`, `description`, `status`, `created_at`) VALUES
(1, 'Small', 'Small', 0, '2025-04-10 13:29:01'),
(2, 'Small Medium', 'Small Medium', 0, '2025-04-10 13:29:35');

-- --------------------------------------------------------

--
-- Table structure for table `product_types`
--

CREATE TABLE `product_types` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_types`
--

INSERT INTO `product_types` (`id`, `name`, `description`, `status`, `created_at`) VALUES
(1, 'Standard', 'Regular product', 0, '2025-04-12 16:22:10'),
(2, 'Buildable', 'Product that can be built from components', 0, '2025-04-12 16:22:10'),
(3, 'Component', 'Can be used as a component in buildable products', 0, '2025-04-12 16:22:10');

-- --------------------------------------------------------

--
-- Table structure for table `product_variants`
--

CREATE TABLE `product_variants` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) DEFAULT NULL,
  `size_id` int(11) DEFAULT NULL,
  `additional_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_variants`
--

INSERT INTO `product_variants` (`id`, `product_id`, `color_id`, `size_id`, `additional_price`, `quantity`, `status`, `created_at`) VALUES
(9, 10, 3, 1, 2.00, 10, 0, '2025-04-10 17:27:17'),
(10, 10, 3, 2, 2.50, 10, 0, '2025-04-10 17:27:17'),
(11, 10, 2, 1, 0.00, 10, 0, '2025-04-10 17:27:17'),
(12, 10, 2, 2, 0.00, 10, 0, '2025-04-10 17:27:17'),
(13, 10, 1, 1, 0.00, 10, 0, '2025-04-10 17:27:17'),
(14, 10, 1, 2, 0.00, 10, 0, '2025-04-10 17:27:17'),
(15, 29, 3, 1, 0.00, 10, 0, '2025-04-13 13:11:49'),
(16, 29, 3, 2, 50.00, 8, 0, '2025-04-13 13:11:49'),
(17, 29, 1, 1, 0.00, 10, 0, '2025-04-13 13:11:49'),
(18, 29, 1, 2, 50.00, 8, 0, '2025-04-13 13:11:49'),
(19, 30, 3, 1, 0.00, 10, 0, '2025-04-14 06:02:21'),
(20, 30, 3, 2, 0.00, 8, 0, '2025-04-14 06:02:21'),
(21, 30, 1, 1, 0.00, 10, 0, '2025-04-14 06:02:21'),
(22, 30, 1, 2, 0.00, 8, 0, '2025-04-14 06:02:21');

-- --------------------------------------------------------

--
-- Table structure for table `product_variant_images`
--

CREATE TABLE `product_variant_images` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) NOT NULL,
  `image` varchar(255) NOT NULL,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `product_variant_images`
--

INSERT INTO `product_variant_images` (`id`, `product_id`, `color_id`, `image`, `display_order`, `created_at`) VALUES
(1, 1, 1, 'test_image.jpg', 1, '2025-04-13 09:53:51'),
(2, 1, 1, '1744538109_variant_test_black-dress.webp', 1, '2025-04-13 09:55:09'),
(3, 1, 1, '1744538143_variant_test_black-dress.webp', 1, '2025-04-13 09:55:43');

-- --------------------------------------------------------

--
-- Table structure for table `quotations`
--

CREATE TABLE `quotations` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `quotation_number` varchar(10) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `delivery_option` varchar(20) NOT NULL DEFAULT 'pickup',
  `delivery_address` text DEFAULT NULL,
  `pickup_date` date DEFAULT NULL,
  `pickup_time` varchar(50) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'pending, processed, completed, cancelled',
  `read_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=Unread, 1=Read',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `quotations`
--

INSERT INTO `quotations` (`id`, `customer_id`, `quotation_number`, `name`, `email`, `phone`, `delivery_option`, `delivery_address`, `pickup_date`, `pickup_time`, `message`, `total_amount`, `status`, `read_status`, `created_at`, `updated_at`) VALUES
(1, 5, 'Q104939', 'Quotation Test', '<EMAIL>', '0797869690', 'pickup', '', '2025-04-11', 'Afternoon (12:00 PM - 3:00 PM)', 'Additional Information', 775.95, 'invoiced', 1, '2025-04-09 09:55:12', '2025-04-09 12:57:18'),
(2, 7, 'Q460378', '007uk', '<EMAIL>', '*********', 'delivery', 'test', NULL, NULL, 'test', 2591.90, 'invoiced', 1, '2025-04-09 14:04:57', '2025-04-09 14:06:41'),
(3, NULL, 'Q073569', 'Quotation Test Size', '<EMAIL>', '*********', 'pickup', '', '2025-04-14', 'Afternoon (12:00 PM - 3:00 PM)', 'Quotation Test Size', 600.00, 'pending', 0, '2025-04-11 07:18:00', '2025-04-11 07:33:30'),
(4, NULL, 'Q779649', 'test Baloon', '<EMAIL>', '*********', 'pickup', '', '2025-04-15', 'Late Afternoon (3:00 PM - 5:00 PM)', 'test info', 130.00, 'pending', 0, '2025-04-11 07:40:22', '2025-04-11 07:40:22'),
(5, 6, 'Q237520', 'balloon', '<EMAIL>', '*********0', 'pickup', '', '2025-04-15', 'Late Afternoon (3:00 PM - 5:00 PM)', 'info', 1425.95, 'invoiced', 1, '2025-04-11 07:46:59', '2025-04-11 07:51:06');

-- --------------------------------------------------------

--
-- Table structure for table `quotation_items`
--

CREATE TABLE `quotation_items` (
  `id` int(11) NOT NULL,
  `quotation_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `color_id` int(11) DEFAULT NULL,
  `color_name` varchar(100) DEFAULT NULL,
  `size_id` int(11) DEFAULT NULL,
  `size_name` varchar(100) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `custom_build_id` int(11) DEFAULT NULL,
  `is_component` tinyint(1) DEFAULT 0 COMMENT '0=No, 1=Yes'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `quotation_items`
--

INSERT INTO `quotation_items` (`id`, `quotation_id`, `product_id`, `color_id`, `color_name`, `size_id`, `size_name`, `name`, `price`, `quantity`, `created_at`, `custom_build_id`, `is_component`) VALUES
(1, 1, 3, NULL, NULL, NULL, NULL, 'Add Product', 325.00, 2, '2025-04-09 09:55:12', NULL, 0),
(2, 1, 1, NULL, NULL, NULL, NULL, 'Product', 125.95, 1, '2025-04-09 09:55:12', NULL, 0),
(3, 2, 6, NULL, NULL, NULL, NULL, 'Add Product Test', 1295.95, 2, '2025-04-09 14:04:57', NULL, 0),
(4, 3, 10, 3, 'Blue', 2, 'Large', 'colour product test', 120.00, 5, '2025-04-11 07:18:00', NULL, 0),
(5, 4, 10, 3, 'black', 2, 'Small Medium', 'colour product test', 130.00, 1, '2025-04-11 07:40:22', NULL, 0),
(6, 5, 6, NULL, '', NULL, '', 'Add Product Test', 1295.95, 1, '2025-04-11 07:46:59', NULL, 0),
(7, 5, 10, 3, 'black', 2, 'Small Medium', 'colour product test', 130.00, 1, '2025-04-11 07:46:59', NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `request_call_back`
--

CREATE TABLE `request_call_back` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `number` varchar(20) NOT NULL,
  `email` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=Unread, 1=Read'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `request_call_back`
--

INSERT INTO `request_call_back` (`id`, `name`, `number`, `email`, `message`, `created_at`, `read_status`) VALUES
(1, 'taariq', '0797869698', '<EMAIL>', 'Test Request a Call Back', '2025-03-21 12:05:34', 0),
(2, 'John Doe', '0821234567', '<EMAIL>', 'Need assistance with web app', '2024-03-21 11:05:34', 0),
(3, 'Jane Smith', '0831234567', '<EMAIL>', 'Interested in custom development', '2024-03-21 12:05:34', 0),
(4, 'Mike Johnson', '0841234567', '<EMAIL>', 'Please call regarding pricing', '2024-03-21 13:05:34', 0),
(5, 'Sarah Wilson', '0851234567', '<EMAIL>', 'Want to discuss new project', '2024-03-21 14:05:34', 0),
(6, 'David Brown', '0861234567', '<EMAIL>', 'Need technical consultation', '2024-03-21 15:05:34', 0),
(7, 'Emma Davis', '0871234567', '<EMAIL>', 'Requesting information', '2024-03-21 16:05:34', 0),
(8, 'James Wilson', '0881234567', '<EMAIL>', 'Need website quote', '2024-03-21 17:05:34', 0),
(9, 'Lisa Anderson', '0891234567', '<EMAIL>', 'Mobile app development inquiry', '2024-03-21 18:05:34', 0),
(10, 'Robert Taylor', '0901234567', '<EMAIL>', 'E-commerce solution needed', '2024-03-21 19:05:34', 0),
(11, 'taariq 88', '*********0', '<EMAIL>', 'Contact Information test form', '2025-04-08 13:25:37', 1),
(12, 'taariq 88', '*********0', '<EMAIL>', 'Contact Information test form', '2025-04-08 13:33:05', 1),
(14, 'Test Uk', '*********', '<EMAIL>', 'Test', '2025-04-09 11:44:36', 1),
(15, 'UK007', '*********', '<EMAIL>', 'TEST', '2025-04-09 11:52:09', 0);

-- --------------------------------------------------------

--
-- Table structure for table `schedule_list`
--

CREATE TABLE `schedule_list` (
  `id` int(11) NOT NULL,
  `title` text NOT NULL,
  `description` text NOT NULL,
  `start_datetime` datetime NOT NULL,
  `end_datetime` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `services_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` mediumtext NOT NULL,
  `cost_price` decimal(10,2) NOT NULL,
  `sales_price` decimal(10,2) NOT NULL,
  `vat_percentage` decimal(10,2) NOT NULL DEFAULT 0.00,
  `vatT` decimal(10,2) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` varchar(255) NOT NULL DEFAULT '0000-00-00',
  `featured` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `category_id`, `services_id`, `name`, `description`, `cost_price`, `sales_price`, `vat_percentage`, `vatT`, `price`, `quantity`, `status`, `created_at`, `featured`) VALUES
(1, NULL, 'SRV67def923ceee0', 'Add Service Updated', '<h4><strong>Add</strong> <i>Service</i></h4>', 200.95, 226.95, 0.00, 0.00, 226.95, 25, 1, '2025-03-22', 1),
(2, NULL, 'SRV67df068592123', 'Add Service 2', '<p><strong>Add Service 2</strong></p>', 22.25, 25.25, 0.00, 0.00, 25.25, 25, 1, '2025-03-22', 1),
(3, 1, 'SRV67df068592124', 'Web Development', '<p>Professional web development services</p>', 800.00, 1000.00, 15.00, 150.00, 1150.00, 1, 0, '2024-03-22', 1),
(4, 1, 'SRV67df068592125', 'SEO Optimization', '<p>Search engine optimization service</p>', 400.00, 500.00, 15.00, 75.00, 575.00, 1, 0, '2024-03-22', 0),
(5, 1, 'SRV67df068592126', 'Website Maintenance', '<p>Monthly website maintenance package</p>', 160.00, 200.00, 15.00, 30.00, 230.00, 1, 0, '2024-03-22', 1);

-- --------------------------------------------------------

--
-- Table structure for table `services_categories`
--

CREATE TABLE `services_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` mediumtext DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `services_categories`
--

INSERT INTO `services_categories` (`id`, `name`, `description`, `status`) VALUES
(1, 'service test category', 'service test category', 0);

-- --------------------------------------------------------

--
-- Table structure for table `services_more_information`
--

CREATE TABLE `services_more_information` (
  `id` int(11) NOT NULL,
  `product_name` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `number` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `read_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=Unread, 1=Read'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `services_more_information`
--

INSERT INTO `services_more_information` (`id`, `product_name`, `name`, `number`, `email`, `message`, `created_at`, `read_status`) VALUES
(1, 'Web Development', 'John Smith', '+27721234567', '<EMAIL>', 'Interested in a custom e-commerce website development', '2024-03-15 09:30:00', 0),
(2, 'Mobile App Development', 'Sarah Johnson', '+27831234567', '<EMAIL>', 'Looking for iOS and Android app development services', '2024-03-16 14:45:00', 0),
(3, 'Cloud Solutions', 'Michael Brown', '+27641234567', '<EMAIL>', 'Need consultation for cloud migration strategy', '2024-03-17 11:20:00', 0),
(4, 'Digital Marketing', 'Emma Wilson', '+27761234567', '<EMAIL>', 'Interested in SEO and social media marketing services', '2024-03-18 16:15:00', 0),
(5, 'IT Consulting', 'David Lee', '+***********', '<EMAIL>', 'Seeking IT infrastructure optimization consultation', '2024-03-19 10:00:00', 0);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `companyname` varchar(255) NOT NULL,
  `contactnumber` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `vat_reg` varchar(255) NOT NULL,
  `vat_percentage` varchar(2) NOT NULL,
  `bank` mediumtext NOT NULL,
  `bank_acc_name` varchar(255) NOT NULL,
  `bank_acc_type` varchar(255) NOT NULL,
  `bank_acc_no` varchar(255) NOT NULL,
  `bank_branch_code` varchar(255) NOT NULL,
  `company_reg_num` tinytext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `companyname`, `contactnumber`, `address`, `vat_reg`, `vat_percentage`, `bank`, `bank_acc_name`, `bank_acc_type`, `bank_acc_no`, `bank_branch_code`, `company_reg_num`) VALUES
(1, 'Web4u', '700', '22 Boom Street Durban2', '*********', '0', 'FNB', '*********', 'Bussines', '123456', '700', '2014/157156/07 ');

-- --------------------------------------------------------

--
-- Table structure for table `supplier`
--

CREATE TABLE `supplier` (
  `id` int(11) NOT NULL,
  `supplier_name` varchar(255) NOT NULL,
  `supplier_company_name` varchar(255) NOT NULL,
  `supplier_email` varchar(255) NOT NULL,
  `supplier_phone` varchar(20) NOT NULL,
  `supplier_address` varchar(255) DEFAULT NULL,
  `supplier_city` varchar(255) DEFAULT NULL,
  `supplier_postcode` varchar(10) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=visible,1=hidden',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `supplier`
--

INSERT INTO `supplier` (`id`, `supplier_name`, `supplier_company_name`, `supplier_email`, `supplier_phone`, `supplier_address`, `supplier_city`, `supplier_postcode`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Supplier Update', 'Company Name Supplier 2', '<EMAIL>', '2211234567', '222 Supplier road', 'Durban2', '4092', 1, '2025-03-21 14:29:22', '2025-03-21 14:32:39'),
(2, 'Add Supplier', 'Company Name Supplier 3', '<EMAIL>', '2211234567', 'Address', '', '4091', 1, '2025-03-22 19:04:46', '2025-03-22 19:04:46');

-- --------------------------------------------------------

--
-- Table structure for table `supplier_backup`
--

CREATE TABLE `supplier_backup` (
  `id` int(11) NOT NULL DEFAULT 0,
  `supplier_company_name` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `supplier_email` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `supplier_address` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `supplier_city` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `supplier_postcode` varchar(4) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `supplier_phone` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '	0=visible,1=hidden	'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `supplier_order`
--

CREATE TABLE `supplier_order` (
  `id` int(11) NOT NULL,
  `supplier_id` varchar(255) NOT NULL,
  `tracking_no` varchar(255) NOT NULL,
  `tot_vat` varchar(255) NOT NULL,
  `total_amount` varchar(255) NOT NULL,
  `order_date` date NOT NULL,
  `order_status` varchar(255) NOT NULL DEFAULT 'order place' COMMENT 'in progress - order place - waiting for delivery ',
  `payment_mode` varchar(255) NOT NULL COMMENT 'cash, online',
  `payment_status` varchar(255) NOT NULL DEFAULT 'Not Paid' COMMENT 'Not Paid, Paid'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `supplier_order_items`
--

CREATE TABLE `supplier_order_items` (
  `id` int(11) NOT NULL,
  `supplier_order_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `vat` varchar(255) NOT NULL,
  `quantity` varchar(255) NOT NULL,
  `price` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admin_login_history`
--
ALTER TABLE `admin_login_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`);

--
-- Indexes for table `backup_history`
--
ALTER TABLE `backup_history`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `call_log`
--
ALTER TABLE `call_log`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_category_parent` (`parent_id`);

--
-- Indexes for table `comments`
--
ALTER TABLE `comments`
  ADD PRIMARY KEY (`comment_id`);

--
-- Indexes for table `contacts`
--
ALTER TABLE `contacts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `custom_product_builds`
--
ALTER TABLE `custom_product_builds`
  ADD PRIMARY KEY (`id`),
  ADD KEY `buildable_product_id` (`buildable_product_id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `custom_product_build_items`
--
ALTER TABLE `custom_product_build_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `build_id` (`build_id`),
  ADD KEY `component_id` (`component_id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `order_report`
--
ALTER TABLE `order_report`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `product_bulk_pricing`
--
ALTER TABLE `product_bulk_pricing`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `product_colors`
--
ALTER TABLE `product_colors`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `product_components`
--
ALTER TABLE `product_components`
  ADD PRIMARY KEY (`id`),
  ADD KEY `buildable_product_id` (`buildable_product_id`),
  ADD KEY `component_category_id` (`component_category_id`);

--
-- Indexes for table `product_component_products`
--
ALTER TABLE `product_component_products`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `component_product` (`component_id`,`product_id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `product_images`
--
ALTER TABLE `product_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `product_labels`
--
ALTER TABLE `product_labels`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `product_label_assignments`
--
ALTER TABLE `product_label_assignments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `product_label` (`product_id`,`label_id`),
  ADD KEY `label_id` (`label_id`);

--
-- Indexes for table `product_more_information`
--
ALTER TABLE `product_more_information`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `product_secondary_categories`
--
ALTER TABLE `product_secondary_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `product_sizes`
--
ALTER TABLE `product_sizes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `product_types`
--
ALTER TABLE `product_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `product_variants`
--
ALTER TABLE `product_variants`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `color_id` (`color_id`),
  ADD KEY `size_id` (`size_id`);

--
-- Indexes for table `product_variant_images`
--
ALTER TABLE `product_variant_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `color_id` (`color_id`);

--
-- Indexes for table `quotations`
--
ALTER TABLE `quotations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `quotation_number` (`quotation_number`);

--
-- Indexes for table `quotation_items`
--
ALTER TABLE `quotation_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `quotation_id` (`quotation_id`);

--
-- Indexes for table `request_call_back`
--
ALTER TABLE `request_call_back`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `schedule_list`
--
ALTER TABLE `schedule_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services_categories`
--
ALTER TABLE `services_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services_more_information`
--
ALTER TABLE `services_more_information`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `supplier`
--
ALTER TABLE `supplier`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `supplier_email` (`supplier_email`);

--
-- Indexes for table `supplier_order`
--
ALTER TABLE `supplier_order`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `supplier_order_items`
--
ALTER TABLE `supplier_order_items`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `admin_login_history`
--
ALTER TABLE `admin_login_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `backup_history`
--
ALTER TABLE `backup_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `call_log`
--
ALTER TABLE `call_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `comments`
--
ALTER TABLE `comments`
  MODIFY `comment_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `contacts`
--
ALTER TABLE `contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `custom_product_builds`
--
ALTER TABLE `custom_product_builds`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `custom_product_build_items`
--
ALTER TABLE `custom_product_build_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `order_report`
--
ALTER TABLE `order_report`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `product_bulk_pricing`
--
ALTER TABLE `product_bulk_pricing`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `product_colors`
--
ALTER TABLE `product_colors`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `product_components`
--
ALTER TABLE `product_components`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `product_component_products`
--
ALTER TABLE `product_component_products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `product_images`
--
ALTER TABLE `product_images`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `product_labels`
--
ALTER TABLE `product_labels`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `product_label_assignments`
--
ALTER TABLE `product_label_assignments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `product_more_information`
--
ALTER TABLE `product_more_information`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `product_secondary_categories`
--
ALTER TABLE `product_secondary_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `product_sizes`
--
ALTER TABLE `product_sizes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `product_types`
--
ALTER TABLE `product_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `product_variants`
--
ALTER TABLE `product_variants`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `product_variant_images`
--
ALTER TABLE `product_variant_images`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `quotations`
--
ALTER TABLE `quotations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `quotation_items`
--
ALTER TABLE `quotation_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `request_call_back`
--
ALTER TABLE `request_call_back`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `schedule_list`
--
ALTER TABLE `schedule_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `services_categories`
--
ALTER TABLE `services_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `services_more_information`
--
ALTER TABLE `services_more_information`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `supplier`
--
ALTER TABLE `supplier`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `supplier_order`
--
ALTER TABLE `supplier_order`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `supplier_order_items`
--
ALTER TABLE `supplier_order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `fk_category_parent` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `product_component_products`
--
ALTER TABLE `product_component_products`
  ADD CONSTRAINT `product_component_products_ibfk_1` FOREIGN KEY (`component_id`) REFERENCES `product_components` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `product_component_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_label_assignments`
--
ALTER TABLE `product_label_assignments`
  ADD CONSTRAINT `product_label_assignments_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `product_label_assignments_ibfk_2` FOREIGN KEY (`label_id`) REFERENCES `product_labels` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `quotation_items`
--
ALTER TABLE `quotation_items`
  ADD CONSTRAINT `quotation_items_ibfk_1` FOREIGN KEY (`quotation_id`) REFERENCES `quotations` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
