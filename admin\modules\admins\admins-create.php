<?php 
include('includes/header.php'); 
checkUserRole('admin');
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Add Admin
                <a href="admins.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>
            <form action="code.php" method="POST">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="name">Name *</label>
                        <input type="text" name="name" id="name" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="email">Email *</label>
                        <input type="email" name="email" id="email" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="password">Password *</label>
                        <input type="password" name="password" id="password" required class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="phone">Phone Number</label>
                        <input type="number" name="phone" id="phone" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="user_type">Role</label>
                        <select name="user_type" id="user_type" class="form-select">
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="is_ban">Status</label>
                        <br/>
                        <input type="checkbox" name="is_ban" id="is_ban" style="width:30px;height:30px" />
                        <label for="is_ban">Banned</label>
                    </div>
                    <div class="col-md-6 mb-3 text-end">
                        <button type="submit" name="saveAdmin" class="btn btn-primary">Save Admin</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>
