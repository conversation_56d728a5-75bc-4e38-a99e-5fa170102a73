<?php
require '../../config/function.php';
require '../../config/dbcon.php';
require '../../includes/header.php';
include('product_audio.php'); // Include the product audio functionality
?>

<!-- <PERSON><PERSON> Styles -->
<style>
    #stickyButtonContainer {
        position: fixed;
        bottom: 100px;
        right: 30px;
        z-index: 99999;
    }

    #stickyButton {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
        padding: 15px 25px;
        font-weight: bold;
        font-size: 18px;
        border-radius: 50px;
        background-color: #28a745; /* Green background */
        border-color: #28a745;
        min-width: 200px;
        transition: all 0.3s ease;
    }

    #stickyButton:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
        background-color: #218838;
        border-color: #1e7e34;
    }
</style>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Edit Product
                <a href="products.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <?php alertMessage(); ?>

            <form action="code.php" method="POST" enctype="multipart/form-data">

                <?php
                $paramValue = checkParamId('id');
                if(!is_numeric($paramValue)){
                    echo '<h5>'.$paramValue.'</h5>';
                    return false;
                }

                $product = getById('products', $paramValue);
                if($product['status'] == 200)
                {
                ?>

                <input type="hidden" name="product_id" value="<?= $product['data']['id']; ?>">

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label>Select Category *</label>
                        <select name="category_id" class="form-select">
                            <option value="">Select Category</option>
                            <?php
                            $categories = getAll('categories');
                            if($categories){
                                if(mysqli_num_rows($categories) > 0){
                                    foreach($categories as $category){
                                        ?>
                                        <option value="<?= $category['id']; ?>"
                                            <?= $product['data']['category_id'] == $category['id'] ? 'selected':''; ?> >
                                            <?= $category['name']; ?>
                                        </option>
                                        <?php
                                    }
                                }else{
                                    echo '<option value="">No Categories Found</option>';
                                }
                            }else{
                                echo '<option value="">Something Went Wrong!</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label for="">Product Name *</label>
                        <input type="text" name="name" value="<?= $product['data']['name']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <label for="">Description</label>
                        <textarea name="description" id="editor" class="form-control" rows="3"><?= htmlspecialchars_decode($product['data']['description']); ?></textarea>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">Cost Price *</label>
                        <input type="text" name="cost_price" value="<?= $product['data']['cost_price']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">Sales Price *</label>
                        <input type="text" name="sales_price" value="<?= $product['data']['sales_price']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">VAT Percentage *</label>
                        <input type="text" name="vat_percentage" value="<?= $product['data']['vat_percentage']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">Quantity *</label>
                        <input type="text" name="quantity" value="<?= $product['data']['quantity']; ?>" required class="form-control" />
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="">Barcode</label>
                        <input type="text" name="barcode" value="<?= $product['data']['barcode']; ?>" class="form-control" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Image (Main)</label>
                        <input type="file" name="image" class="form-control" accept="image/*" />
                        <div class="mt-2">
                            <small class="text-muted">Current image:</small>
                            <?php if(!empty($product['data']['image'])): ?>
                                <img src="../../../uploads/products/<?= $product['data']['image']; ?>" style="width:60px;height:60px;object-fit:cover;" class="img-thumbnail" alt="Main Image" />
                            <?php else: ?>
                                <span class="text-muted">No image uploaded</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="">Additional Images</label>
                        <input type="file" name="additional_images[]" class="form-control" accept="image/*" multiple />
                        <small class="text-muted">You can select up to 3 additional images (hold Ctrl to select multiple)</small>

                        <div class="mt-2">
                            <small class="text-muted">Current additional images:</small>
                            <div class="d-flex flex-wrap gap-2 mt-1">
                                <?php
                                // Get additional images for this product
                                $additional_images_query = mysqli_query($conn, "SELECT * FROM product_images WHERE product_id = '{$product['data']['id']}' ORDER BY display_order ASC");

                                if($additional_images_query && mysqli_num_rows($additional_images_query) > 0) {
                                    while($img = mysqli_fetch_assoc($additional_images_query)) {
                                        echo '<div class="position-relative">';
                                        echo '<img src="../../../uploads/products/'.$img['image'].'" style="width:50px;height:50px;object-fit:cover;" class="img-thumbnail" alt="Additional Image" />';
                                        echo '<a href="code.php?action=delete_product_image&id='.$img['id'].'&product_id='.$product['data']['id'].'" class="position-absolute top-0 end-0 btn btn-danger btn-sm" style="padding: 0 5px; font-size: 10px;" onclick="return confirm(\'Are you sure you want to delete this image?\');">×</a>';
                                        echo '</div>';
                                    }
                                } else {
                                    echo '<span class="text-muted">No additional images</span>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    <!-- Product Labels -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-tag me-2"></i>Product Labels
                                    <?php createAudioButton('labelsAudioBtn', 'Listen to Product Labels explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Select a label to display on this product:</p>
                                <div class="d-flex flex-wrap gap-3">
                                    <?php
                                    // Get current product labels
                                    $current_labels = [];
                                    $label_query = mysqli_query($conn, "SELECT label_id FROM product_label_assignments WHERE product_id = {$product['data']['id']}");
                                    if(mysqli_num_rows($label_query) > 0) {
                                        while($label_row = mysqli_fetch_assoc($label_query)) {
                                            $current_labels[] = $label_row['label_id'];
                                        }
                                    }

                                    // Get all labels
                                    $labels = mysqli_query($conn, "SELECT * FROM product_labels WHERE status = 0 ORDER BY name ASC");
                                    if(mysqli_num_rows($labels) > 0) {
                                        while($label = mysqli_fetch_assoc($labels)) {
                                            $checked = in_array($label['id'], $current_labels) ? 'checked' : '';
                                            ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="product_labels[]" value="<?= $label['id'] ?>" id="label<?= $label['id'] ?>" <?= $checked ?>>
                                                <label class="form-check-label" for="label<?= $label['id'] ?>">
                                                    <span class="badge" style="background-color: <?= $label['background_color'] ?>; color: <?= $label['text_color'] ?>; padding: 5px 10px;">
                                                        <?= htmlspecialchars($label['name']) ?>
                                                    </span>
                                                </label>
                                            </div>
                                            <?php
                                        }
                                    } else {
                                        echo '<p>No labels available. <a href="labels-create.php">Create some labels</a> first.</p>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Type -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-cube me-2"></i>Product Type
                                    <?php createAudioButton('typeAudioBtn', 'Listen to Product Type explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="productType">Select Product Type</label>
                                    <select name="product_type_id" id="productType" class="form-select">
                                        <?php
                                        $product_types_query = mysqli_query($conn, "SELECT * FROM product_types WHERE status = 0");
                                        while($type = mysqli_fetch_assoc($product_types_query)) {
                                            $selected = ($product['data']['product_type_id'] == $type['id']) ? 'selected' : '';
                                            echo "<option value='{$type['id']}' {$selected}>{$type['name']}</option>";
                                        }
                                        ?>
                                    </select>
                                    <small class="text-muted">Standard: Regular product | Buildable: Product that can be built from components | Component: Can be used in buildable products</small>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="can_be_component" id="canBeComponent" value="1" <?= $product['data']['can_be_component'] == 1 ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="canBeComponent">
                                        This product can be used as a component in buildable products
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Variants -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-tags me-2"></i>Product Variants
                                    <?php createAudioButton('variantsAudioBtn', 'Listen to Product Variants explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableVariants" name="enable_variants" value="1" <?= isset($product['data']['enable_variants']) && $product['data']['enable_variants'] == 1 ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="enableVariants">
                                        Enable product variants (colors and sizes)
                                    </label>
                                </div>

                                <div class="variants-container mt-3" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <h6>Available Colors</h6>
                                            <div class="border p-3 rounded">
                                                <?php
                                                $colors_query = mysqli_query($conn, "SELECT * FROM product_colors WHERE status = 0 ORDER BY name ASC");
                                                if(mysqli_num_rows($colors_query) > 0) {
                                                    while($color = mysqli_fetch_assoc($colors_query)) {
                                                        ?>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="colors[]" value="<?= $color['id'] ?>" id="color<?= $color['id'] ?>">
                                                            <label class="form-check-label" for="color<?= $color['id'] ?>">
                                                                <span class="color-swatch" style="display: inline-block; width: 20px; height: 20px; background-color: <?= $color['hex_code'] ?>; border: 1px solid #ddd; vertical-align: middle; margin-right: 5px;"></span>
                                                                <?= htmlspecialchars($color['name']) ?>
                                                            </label>
                                                        </div>
                                                        <?php
                                                    }
                                                } else {
                                                    echo '<p>No colors available.</p>';
                                                }
                                                ?>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <h6>Available Sizes</h6>
                                            <div class="border p-3 rounded">
                                                <?php
                                                $sizes_query = mysqli_query($conn, "SELECT * FROM product_sizes WHERE status = 0 ORDER BY name ASC");
                                                if(mysqli_num_rows($sizes_query) > 0) {
                                                    while($size = mysqli_fetch_assoc($sizes_query)) {
                                                        ?>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="sizes[]" value="<?= $size['id'] ?>" id="size<?= $size['id'] ?>">
                                                            <label class="form-check-label" for="size<?= $size['id'] ?>">
                                                                <?= htmlspecialchars($size['name']) ?>
                                                            </label>
                                                        </div>
                                                        <?php
                                                    }
                                                } else {
                                                    echo '<p>No sizes available.</p>';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Pricing -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0 d-flex align-items-center">
                                    <i class="fas fa-money-bill-wave me-2"></i>Bulk Pricing
                                    <?php createAudioButton('bulkPricingAudioBtn', 'Listen to Bulk Pricing explanation'); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="enableBulkPricing" name="enable_bulk_pricing" value="1" <?= isset($product['data']['enable_bulk_pricing']) && $product['data']['enable_bulk_pricing'] == 1 ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="enableBulkPricing">
                                        Enable bulk pricing
                                    </label>
                                </div>
                                <small class="text-muted">Set different prices for quantity ranges</small>

                                <div class="bulk-pricing-container mt-3" style="display: none;">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Quantity Range</th>
                                                    <th>Price Per Unit</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody id="bulkPricingTableBody">
                                                <tr>
                                                    <td>
                                                        <div class="input-group">
                                                            <input type="number" name="bulk_min_qty[]" class="form-control" placeholder="Min" min="2" value="2">
                                                            <span class="input-group-text">-</span>
                                                            <input type="number" name="bulk_max_qty[]" class="form-control" placeholder="Max" min="5" value="5">
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <input type="number" step="0.01" name="bulk_price[]" class="form-control" placeholder="Price per unit">
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-danger remove-bulk-row">Remove</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success" id="addBulkPricingRow">Add Range</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="">Product Visibility</label>
                        <br/>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="statusSwitch" name="status" <?= $product['data']['status'] == 1 ? 'checked':''; ?> style="width:50px;height:25px;" />
                            <label class="form-check-label" for="statusSwitch">
                                <span id="statusText" style="font-weight:bold;color:<?= $product['data']['status'] == 1 ? 'red':'green'; ?>">
                                    <?= $product['data']['status'] == 1 ? 'HIDDEN - Not visible to customers' : 'VISIBLE - Shown to customers'; ?>
                                </span>
                            </label>
                        </div>
                        <small class="text-muted">Toggle the switch to change visibility</small>
                    </div>

                    <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const statusSwitch = document.getElementById('statusSwitch');
                        const statusText = document.getElementById('statusText');

                        statusSwitch.addEventListener('change', function() {
                            if (this.checked) {
                                statusText.textContent = 'HIDDEN - Not visible to customers';
                                statusText.style.color = 'red';
                            } else {
                                statusText.textContent = 'VISIBLE - Shown to customers';
                                statusText.style.color = 'green';
                            }
                        });
                    });
                    </script>
                    <!-- Regular button (will be hidden on larger screens) -->
                    <div class="col-md-6 d-md-none">
                        <button type="submit" name="updateProduct" class="btn btn-primary">Update</button>
                    </div>
                </div>

            <!-- Sticky button that triggers form submission -->
            <div id="stickyButtonContainer" class="d-none d-md-block">
                <button type="button" id="stickyButton" class="btn btn-success" onclick="document.querySelector('form').submit();">
                    <i class="fas fa-sync-alt me-2"></i> Update Product
                </button>
            </div>

            <?php
                }
                else
                {
                    echo '<h5>'.$product['message'].'</h5>';
                }
                ?>

            </form>

        </div>
    </div>
</div>

<!-- CKEditor CDN -->
<script src="https://cdn.ckeditor.com/ckeditor5/38.1.0/classic/ckeditor.js"></script>

<script>
// Initialize CKEditor and other functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor
    ClassicEditor
        .create(document.querySelector('#editor'), {
            toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote'],
            heading: {
                options: [
                    { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                    { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                ]
            }
        })
        .catch(error => {
            console.error(error);
        });

    // Product Type Toggle
    const productTypeSelect = document.getElementById('productType');
    const canBeComponentCheckbox = document.getElementById('canBeComponent');
    const buildableOptionsContainer = document.getElementById('buildableOptions');

    // Get the container of the canBeComponent checkbox
    const canBeComponentContainer = canBeComponentCheckbox.closest('.form-check');

    productTypeSelect.addEventListener('change', function() {
        // If product type is Component (id=3), disable the can_be_component checkbox
        if (this.value === '3') {
            canBeComponentCheckbox.checked = false;
            canBeComponentCheckbox.disabled = true;
            canBeComponentContainer.style.display = 'block'; // Show the checkbox but disabled
            if (buildableOptionsContainer) buildableOptionsContainer.style.display = 'none';
        } else if (this.value === '2') { // Buildable product
            canBeComponentCheckbox.checked = false;
            canBeComponentCheckbox.disabled = true;
            canBeComponentContainer.style.display = 'none'; // Hide the checkbox completely
            if (buildableOptionsContainer) buildableOptionsContainer.style.display = 'block';
        } else { // Standard product
            canBeComponentCheckbox.disabled = false;
            canBeComponentContainer.style.display = 'block'; // Show the checkbox
            if (buildableOptionsContainer) buildableOptionsContainer.style.display = 'none';
        }
    });

    // Trigger the change event to set initial state
    productTypeSelect.dispatchEvent(new Event('change'));

    // Product Variants Toggle
    const enableVariantsCheckbox = document.getElementById('enableVariants');
    const variantsContainer = document.querySelector('.variants-container');

    if (variantsContainer) {
        enableVariantsCheckbox.addEventListener('change', function() {
            if (this.checked) {
                variantsContainer.style.display = 'block';
            } else {
                variantsContainer.style.display = 'none';
            }
        });

        // Set initial state
        variantsContainer.style.display = enableVariantsCheckbox.checked ? 'block' : 'none';
    }

    // Bulk Pricing Toggle
    const enableBulkPricingCheckbox = document.getElementById('enableBulkPricing');
    const bulkPricingContainer = document.querySelector('.bulk-pricing-container');

    if (bulkPricingContainer) {
        enableBulkPricingCheckbox.addEventListener('change', function() {
            if (this.checked) {
                bulkPricingContainer.style.display = 'block';
            } else {
                bulkPricingContainer.style.display = 'none';
            }
        });

        // Set initial state
        bulkPricingContainer.style.display = enableBulkPricingCheckbox.checked ? 'block' : 'none';

        // Add Bulk Pricing Row
        const addBulkPricingRowBtn = document.getElementById('addBulkPricingRow');
        const bulkPricingTableBody = document.getElementById('bulkPricingTableBody');

        if (addBulkPricingRowBtn && bulkPricingTableBody) {
            addBulkPricingRowBtn.addEventListener('click', function() {
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td>
                        <div class="input-group">
                            <input type="number" name="bulk_min_qty[]" class="form-control" placeholder="Min" min="2">
                            <span class="input-group-text">-</span>
                            <input type="number" name="bulk_max_qty[]" class="form-control" placeholder="Max" min="5">
                        </div>
                    </td>
                    <td>
                        <input type="number" step="0.01" name="bulk_price[]" class="form-control" placeholder="Price per unit">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-bulk-row">Remove</button>
                    </td>
                `;

                bulkPricingTableBody.appendChild(newRow);

                // Add event listener to the new remove button
                newRow.querySelector('.remove-bulk-row').addEventListener('click', function() {
                    this.closest('tr').remove();
                });
            });

            // Remove Bulk Pricing Row
            document.querySelectorAll('.remove-bulk-row').forEach(function(button) {
                button.addEventListener('click', function() {
                    this.closest('tr').remove();
                });
            });
        }
    }
});
</script>

<?php include('../../includes/footer.php'); ?>
