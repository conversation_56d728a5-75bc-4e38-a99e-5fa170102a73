<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Check the structure of the settings table
$query = "DESCRIBE settings";
$result = mysqli_query($conn, $query);

if ($result) {
    echo "<h2>Settings Table Structure</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Show the data in the settings table
    $data_query = "SELECT * FROM settings LIMIT 10";
    $data_result = mysqli_query($conn, $data_query);
    
    if ($data_result) {
        echo "<h2>Settings Table Data (First 10 rows)</h2>";
        echo "<table border='1'>";
        
        // Get column names
        $fields = mysqli_fetch_fields($data_result);
        echo "<tr>";
        foreach ($fields as $field) {
            echo "<th>" . $field->name . "</th>";
        }
        echo "</tr>";
        
        // Get data
        while ($row = mysqli_fetch_assoc($data_result)) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . $value . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Error fetching data: " . mysqli_error($conn) . "</p>";
    }
} else {
    echo "<p>Error: " . mysqli_error($conn) . "</p>";
    
    // Check if the table exists
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'settings'");
    if (mysqli_num_rows($table_check) == 0) {
        echo "<p>The settings table does not exist.</p>";
        
        // Create the settings table
        echo "<h2>Creating settings table...</h2>";
        
        $create_table = "CREATE TABLE `settings` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(255) NOT NULL,
          `value` varchar(255) NOT NULL,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
        
        if (mysqli_query($conn, $create_table)) {
            echo "<p>Settings table created successfully!</p>";
            
            // Insert default VAT percentage
            $insert_vat = "INSERT INTO `settings` (`name`, `value`) VALUES ('vat_percentage', '15')";
            if (mysqli_query($conn, $insert_vat)) {
                echo "<p>Default VAT percentage added successfully!</p>";
            } else {
                echo "<p>Error adding default VAT percentage: " . mysqli_error($conn) . "</p>";
            }
        } else {
            echo "<p>Error creating settings table: " . mysqli_error($conn) . "</p>";
        }
    }
}
?>
