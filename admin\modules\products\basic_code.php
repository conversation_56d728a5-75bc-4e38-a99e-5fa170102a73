<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require '../../config/function.php';

// Create a log file
$log_file = __DIR__ . '/basic_code_log.txt';
file_put_contents($log_file, date('Y-m-d H:i:s') . " - <PERSON>ript started\n", FILE_APPEND);

// Function to log messages
function log_message($message, $data = null) {
    global $log_file;
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - Data: " . print_r($data, true);
        } else {
            $log_message .= " - Data: " . $data;
        }
    }
    file_put_contents($log_file, $log_message . "\n", FILE_APPEND);
}

// Product Management
if(isset($_POST['saveProduct'])) {
    log_message("saveProduct form submitted");
    
    try {
        // Basic product data
        $category_id = validate($_POST['category_id']);
        $name = validate($_POST['name']);
        $description = $_POST['description']; // Don't validate HTML content
        $cost_price = validate($_POST['cost_price']);
        $sales_price = validate($_POST['sales_price']);
        $quantity = validate($_POST['quantity']);
        $barcode = validate($_POST['barcode']);
        $status = isset($_POST['status']) ? 1 : 0;
        
        log_message("Basic product data", [
            'category_id' => $category_id,
            'name' => $name,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'quantity' => $quantity,
            'barcode' => $barcode,
            'status' => $status
        ]);
        
        // Get VAT percentage from settings
        $vat_query = mysqli_query($conn, "SELECT vat_percentage FROM settings LIMIT 1");
        $vat_percentage = 0; // Default value
        
        if($vat_query && mysqli_num_rows($vat_query) > 0) {
            $vat_percentage = mysqli_fetch_assoc($vat_query)['vat_percentage'];
        }
        
        // Calculate VAT
        $vatT = ($sales_price * $vat_percentage) / 100;
        $price = $sales_price + $vatT;
        
        log_message("VAT calculations", [
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price
        ]);
        
        // Handle main image upload
        $finalImage = "default.jpg"; // Default image
        if(isset($_FILES['image']['name']) && !empty($_FILES['image']['name'])) {
            log_message("Main image found", $_FILES['image']['name']);
            
            $upload_path = "../../../uploads/products/";
            
            // Ensure upload directory exists
            if (!file_exists($upload_path)) {
                mkdir($upload_path, 0777, true);
                log_message("Created upload directory", $upload_path);
            }
            
            $image = $_FILES['image']['name'];
            
            if($image != "") {
                $finalImage = time() . '_main_' . $image;
                $upload_to = $upload_path . $finalImage;
                
                log_message("Uploading main image", [
                    'original' => $image,
                    'final' => $finalImage,
                    'path' => $upload_to
                ]);
                
                if(move_uploaded_file($_FILES['image']['tmp_name'], $upload_to)) {
                    log_message("Main image uploaded successfully");
                } else {
                    log_message("Failed to upload main image", [
                        'error' => $_FILES['image']['error'],
                        'tmp_name' => $_FILES['image']['tmp_name'],
                        'exists' => file_exists($_FILES['image']['tmp_name'])
                    ]);
                }
            }
        } else {
            log_message("No main image provided");
        }
        
        // Prepare product data
        $data = [
            'category_id' => $category_id,
            'name' => $name,
            'description' => $description,
            'cost_price' => $cost_price,
            'sales_price' => $sales_price,
            'vat_percentage' => $vat_percentage,
            'vatT' => $vatT,
            'price' => $price,
            'quantity' => $quantity,
            'barcode' => $barcode,
            'image' => $finalImage,
            'status' => $status,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        log_message("Product data prepared", $data);
        
        // Insert the product using direct SQL
        $columns = implode(", ", array_keys($data));
        $placeholders = implode(", ", array_fill(0, count($data), "?"));
        
        $sql = "INSERT INTO products ($columns) VALUES ($placeholders)";
        log_message("SQL Query", $sql);
        
        $stmt = mysqli_prepare($conn, $sql);
        
        if ($stmt) {
            // Bind parameters
            $types = str_repeat("s", count($data));
            $stmt->bind_param($types, ...array_values($data));
            
            // Execute the statement
            $result = $stmt->execute();
            $product_id = $stmt->insert_id;
            
            log_message("Query result", [
                'success' => $result ? 'true' : 'false',
                'product_id' => $product_id,
                'error' => $stmt->error
            ]);
            
            $stmt->close();
            
            if($result) {
                // Success - redirect to products page
                log_message("Product created successfully, redirecting to products.php");
                
                // Output success message instead of redirecting
                echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 5px;'>
                    <h3>Success!</h3>
                    <p>The product was created successfully with ID: $product_id</p>
                    <p><a href='products.php'>View all products</a></p>
                </div>";
                exit;
            } else {
                // Failure - redirect back to create page
                log_message("Failed to create product");
                
                // Output error message instead of redirecting
                echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
                    <h3>Error!</h3>
                    <p>Failed to insert the product into the database.</p>
                    <p>MySQL Error: " . mysqli_error($conn) . "</p>
                    <p><a href='products-create.php'>Try again</a></p>
                </div>";
                exit;
            }
        } else {
            log_message("Failed to prepare statement", mysqli_error($conn));
            
            // Output error message
            echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
                <h3>Error!</h3>
                <p>Failed to prepare statement.</p>
                <p>MySQL Error: " . mysqli_error($conn) . "</p>
                <p><a href='products-create.php'>Try again</a></p>
            </div>";
            exit;
        }
    } catch (Exception $e) {
        // Handle exceptions
        log_message("Exception", [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        // Output error message
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Error!</h3>
            <p>An exception occurred: " . $e->getMessage() . "</p>
            <p><a href='products-create.php'>Try again</a></p>
        </div>";
        exit;
    } catch (Error $e) {
        // Handle errors
        log_message("Error", [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        // Output error message
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 20px; border-radius: 5px;'>
            <h3>Error!</h3>
            <p>An error occurred: " . $e->getMessage() . "</p>
            <p><a href='products-create.php'>Try again</a></p>
        </div>";
        exit;
    }
}
?>
