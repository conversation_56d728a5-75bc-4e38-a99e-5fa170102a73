<?php
session_start();
require '../config/function.php';
require '../config/dbcon.php';
require_once('../config/config.php');

if(!isset($_SESSION['loggedIn'])) {
    redirect('../auth/login.php','Login to continue...');
}

define('VOICE_CONTROL_INCLUDED', true);

include('../includes/header.php');
?>

<!-- Voice Command Feedback Modal -->
<div class="modal fade" id="voiceFeedbackModal" tabindex="-1" role="dialog" aria-labelledby="voiceFeedbackLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <p id="voiceFeedbackText" role="status" aria-live="polite">Listening...</p>
            </div>
        </div>
    </div>
</div>

<!-- Make sure Bootstrap JS is loaded -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Modified VoiceControl class
class VoiceControl {
    constructor(config) {
        this.config = {
            commands: {},
            feedbackDuration: 3000,
            maxRetries: 2,
            retryDelay: 3000, // Time between retries
            ...config
        };
        this.recognition = null;
        this.isListening = false;
        this.retryCount = 0;
        this.retryTimeout = null;
        
        // Add browser detection
        this.isBraveBrowser = this.detectBrave();
    }

    detectBrave() {
        return navigator.brave?.isBrave() || // Modern Brave detection
               (window.navigator.userAgent.includes("Brave")); // Fallback detection
    }

    async checkNetworkConnection() {
        try {
            // First try the native navigator.onLine
            if (!navigator.onLine) {
                return false;
            }

            // Then try a simple same-origin fetch to avoid CORS issues
            try {
                const response = await fetch(window.location.href, {
                    method: 'HEAD',
                    cache: 'no-cache'
                });
                return response.ok;
            } catch (e) {
                console.warn('Same-origin fetch failed:', e);
                
                // Fallback to a minimal request
                const response = await fetch('favicon.ico', {
                    method: 'HEAD',
                    cache: 'no-cache'
                });
                return response.ok;
            }
        } catch (e) {
            console.error('Network check failed:', e);
            return navigator.onLine; // Fallback to basic check
        }
    }

    checkHttps() {
        if (window.location.protocol !== 'https:') {
            this.showFeedback(`
                Voice recognition requires HTTPS. 
                Current protocol: ${window.location.protocol}
                Please use a secure connection.
            `);
            return false;
        }
        return true;
    }

    async toggleListening() {
        if (!this.isListening) {
            if (!this.checkHttps()) return;
            try {
                // Then check network
                const isOnline = await this.checkNetworkConnection();
                if (!isOnline) {
                    if (this.isBraveBrowser) {
                        this.showFeedback(`
                            Connection issues detected. Please check:
                            1. Your internet connection
                            2. Brave Shields settings
                            3. Try disabling Brave Shields temporarily
                        `);
                    }
                    return;
                }

                // If all checks pass, start recognition
                console.log('Starting recognition...');
                await this.recognition.start();
                console.log('Recognition started successfully');
            } catch (error) {
                console.error('Error starting recognition:', error);
                this.handleStartError(error);
            }
        } else {
            this.stopListening();
        }
    }

    stopListening() {
        // Clear any pending retry timeout
        if (this.retryTimeout) {
            clearTimeout(this.retryTimeout);
            this.retryTimeout = null;
        }

        try {
            this.recognition.stop();
        } catch (error) {
            console.error('Error stopping recognition:', error);
        }
        this.isListening = false;
        this.retryCount = 0;
    }

    handleNetworkError() {
        const errorMessage = 'Network connection failed. Please check your internet connection.';
        
        if (this.retryCount < this.config.maxRetries) {
            this.retryCount++;
            this.showFeedback(`${errorMessage} Retrying... (Attempt ${this.retryCount}/${this.config.maxRetries})`);
            
            // Set up retry with timeout
            this.retryTimeout = setTimeout(async () => {
                if (this.isListening) {
                    const isOnline = await this.checkNetworkConnection();
                    if (isOnline) {
                        this.toggleListening();
                    } else {
                        this.showFeedback('Still no internet connection. Please try again later.');
                        this.stopListening();
                    }
                }
            }, this.config.retryDelay);
        } else {
            this.showFeedback(`${errorMessage} Max retries reached. Please try again later.`);
            this.stopListening();
        }
    }

    init() {
        try {
            console.log('Initializing speech recognition...');
            
            // Add Brave warning
            if (this.isBraveBrowser) {
                console.warn('Brave browser detected - voice recognition may not work properly');
                this.showBraveWarning();
            }

            this.recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            this.recognition.lang = 'en-US';
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            
            this.recognition.addEventListener('error', (event) => {
                console.error('Voice recognition error:', event.error);
                
                switch(event.error) {
                    case 'network':
                        this.handleNetworkError();
                        break;
                    case 'not-allowed':
                    case 'permission-denied':
                        this.showFeedback('Microphone access denied. Please allow microphone access in your browser settings.');
                        this.stopListening();
                        break;
                    case 'no-speech':
                        this.showFeedback('No speech detected. Please try again.');
                        this.stopListening();
                        break;
                    default:
                        this.showFeedback(`Error: ${event.error}`);
                        this.stopListening();
                }
            });
            
            this.setupEventListeners();
            console.log('Speech recognition initialized successfully');
        } catch (e) {
            console.error('Speech recognition not supported:', e);
            const voiceBtn = document.getElementById('voiceCommandBtn');
            if (voiceBtn) {
                voiceBtn.style.display = 'none';
                this.showFeedback('Speech recognition is not supported in this browser');
            }
        }
    }

    setupEventListeners() {
        const voiceBtn = document.getElementById('voiceCommandBtn');
        console.log('Setting up event listeners, voiceBtn:', voiceBtn);

        if (voiceBtn) {
            voiceBtn.addEventListener('click', () => {
                console.log('Voice button clicked, isListening:', this.isListening);
                this.toggleListening();
            });
        }

        this.recognition.onstart = () => {
            console.log('Recognition started event fired');
            this.isListening = true;
            if (voiceBtn) {
                voiceBtn.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                voiceBtn.classList.add('btn-danger');
            }
            this.showFeedback('Listening...');
        };

        this.recognition.onend = () => {
            console.log('Recognition ended');
            this.isListening = false;
            if (voiceBtn) {
                voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                voiceBtn.classList.remove('btn-danger');
            }
        };

        this.recognition.onresult = (event) => {
            const command = event.results[0][0].transcript.toLowerCase();
            console.log('Voice command:', command);
            this.handleCommand(command);
        };

        this.recognition.onerror = (event) => {
            console.error('Voice recognition error:', event.error);
            this.showFeedback('Error: ' + event.error);
            this.stopListening();
        };
    }

    showFeedback(message, duration = 5000) {
        const feedbackText = document.getElementById('voiceFeedbackText');
        const feedbackModal = new bootstrap.Modal(document.getElementById('voiceFeedbackModal'));
        
        if (feedbackText) {
            feedbackText.innerHTML = message;
            feedbackModal.show();
            
            setTimeout(() => {
                feedbackModal.hide();
            }, duration);
        } else {
            console.warn('Feedback elements not found');
        }
    }

    handleCommand(command) {
        for (const [pattern, handler] of Object.entries(this.config.commands)) {
            if (command.includes(pattern)) {
                this.showFeedback('Executing: ' + pattern);
                handler(command);
                return;
            }
        }
        this.showFeedback('Command not recognized: ' + command);
    }

    showBraveWarning() {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'alert alert-warning alert-dismissible fade show';
        warningDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i> 
            You're using Brave browser. If voice recognition doesn't work:
            <ul>
                <li>Check microphone permissions in Brave shield settings</li>
                <li>Try using Chrome, Edge, or Safari instead</li>
                <li>Make sure you're on HTTPS</li>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.querySelector('.container-fluid').prepend(warningDiv);
    }

    handleStartError(error) {
        if (error.name === 'NotAllowedError') {
            this.showFeedback(`
                Microphone access denied. Please:
                1. Click the camera icon in your address bar
                2. Allow microphone access
                3. Reload the page
            `);
        } else if (error.name === 'NetworkError') {
            this.showFeedback(`
                Network error detected. Please:
                1. Check your internet connection
                2. If using Brave, try disabling Shields
                3. Try refreshing the page
            `);
        } else {
            this.showFeedback(`Error: ${error.message || 'Unknown error'}. Please try again.`);
        }
        this.stopListening();
    }
}

// Initialize only after DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Add a visible status indicator
    const statusDiv = document.createElement('div');
    statusDiv.id = 'connectionStatus';
    statusDiv.className = 'alert alert-info';
    statusDiv.style.position = 'fixed';
    statusDiv.style.top = '10px';
    statusDiv.style.right = '10px';
    statusDiv.style.zIndex = '9999';
    document.body.appendChild(statusDiv);

    // Monitor connection status
    function updateConnectionStatus() {
        const status = navigator.onLine;
        statusDiv.className = `alert alert-${status ? 'success' : 'danger'}`;
        statusDiv.textContent = status ? 'Connected' : 'No Internet Connection';
        setTimeout(() => statusDiv.style.display = 'none', 3000);
    }

    window.addEventListener('online', updateConnectionStatus);
    window.addEventListener('offline', updateConnectionStatus);
    updateConnectionStatus();

    // Check browser compatibility
    const browserWarnings = [];
    
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        browserWarnings.push('Voice recognition is not supported in this browser. Please use Chrome, Edge, or Safari.');
    }
    
    if (window.location.protocol !== 'https:') {
        browserWarnings.push('Voice recognition requires a secure HTTPS connection to work properly.');
    }
    
    if (browserWarnings.length > 0) {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'alert alert-warning';
        warningDiv.innerHTML = browserWarnings.map(warning => 
            `<i class="fas fa-exclamation-triangle"></i> ${warning}`
        ).join('<br>');
        document.querySelector('.container-fluid').prepend(warningDiv);
    }

    // Create button and initialize voice control
    if (!document.getElementById('voiceCommandBtn')) {
        const btn = document.createElement('button');
        btn.id = 'voiceCommandBtn';
        btn.className = 'btn btn-primary position-fixed';
        btn.style.cssText = 'bottom: 20px; right: 20px; border-radius: 50%; width: 60px; height: 60px; z-index: 1050;';
        btn.innerHTML = '<i class="fas fa-microphone"></i>';
        document.body.appendChild(btn);
    }

    const voiceControl = new VoiceControl({
        commands: {
            'create invoice': () => {
                console.log('Creating invoice...');
                const customerModal = new bootstrap.Modal(document.getElementById('selectCustomerModal'));
                customerModal.show();
            },
            'show orders': () => {
                console.log('Showing orders...');
                window.location.href = '../modules/orders/orders.php';
            },
            'back to dashboard': () => {
                console.log('Going to dashboard...');
                window.location.href = '../index.php';
            },
            'help': () => {
                console.log('Opening help...');
                window.location.href = 'voice-commands.php';
            }
        }
    });

    voiceControl.init();

    // Check actual network connectivity
    window.addEventListener('online', () => {
        console.log('Network connection restored');
    });
    
    window.addEventListener('offline', () => {
        console.log('Network connection lost');
        voiceControl.showFeedback('Network connection lost. Voice recognition paused.');
    });

    // Browser detection and recommendations
    function getBrowserInfo() {
        const ua = navigator.userAgent;
        if (navigator.brave?.isBrave() || ua.includes("Brave")) {
            return {
                name: 'Brave',
                recommended: false,
                alternatives: ['Edge', 'Chrome', 'Safari']
            };
        } else if (ua.includes("Firefox")) {
            return {
                name: 'Firefox',
                recommended: false,
                alternatives: ['Edge', 'Chrome']
            };
        } else if (ua.includes("Edg")) {
            return {
                name: 'Edge',
                recommended: true
            };
        } else if (ua.includes("Chrome")) {
            return {
                name: 'Chrome',
                recommended: true
            };
        } else if (ua.includes("Safari")) {
            return {
                name: 'Safari',
                recommended: true
            };
        }
        return null;
    }

    const browserInfo = getBrowserInfo();
    if (browserInfo && !browserInfo.recommended) {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'alert alert-info alert-dismissible fade show';
        warningDiv.innerHTML = `
            <i class="fas fa-info-circle"></i> 
            You're using ${browserInfo.name}. For best voice recognition experience, we recommend:
            <ul>
                ${browserInfo.alternatives.map(browser => `<li>${browser}</li>`).join('')}
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.querySelector('.container-fluid').prepend(warningDiv);
    }
});
</script>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">Voice Commands Help
                <a href="javascript:history.back()" class="btn btn-primary float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <h5 class="mb-4">How to Use Voice Commands</h5>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Click the microphone button <button class="btn btn-primary btn-sm" style="border-radius: 50%;"><i class="fas fa-microphone"></i></button> in the bottom right corner to start voice recognition.
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Command</th>
                                    <th>Action</th>
                                    <th>Available On</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>"create invoice"</strong></td>
                                    <td>Opens the customer selection modal to create a new invoice</td>
                                    <td>Orders page only</td>
                                </tr>
                                <tr>
                                    <td><strong>"show orders"</strong></td>
                                    <td>Navigate to the orders page</td>
                                    <td>Dashboard only</td>
                                </tr>
                                <tr>
                                    <td><strong>"show customers"</strong></td>
                                    <td>Navigate to the customers page</td>
                                    <td>Dashboard only</td>
                                </tr>
                                <tr>
                                    <td><strong>"back to dashboard"</strong></td>
                                    <td>Return to the main dashboard</td>
                                    <td>Voice Commands Help page only</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        <h5>Tips for Better Recognition</h5>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i> Speak clearly and at a normal pace
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i> Use the exact phrases shown above
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i> Wait for the "Listening..." message before speaking
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i> Minimize background noise
                            </li>
                        </ul>
                    </div>

                    <div class="mt-4">
                        <h5>Troubleshooting</h5>
                        <div class="alert alert-warning">
                            <ul class="mb-0">
                                <li>Make sure your browser has permission to use the microphone</li>
                                <li>Voice commands work best in Chrome, Edge, and Safari browsers</li>
                                <li>If a command isn't recognized, try speaking more clearly or checking the exact phrase in the table above</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include('../includes/footer.php'); ?>





























