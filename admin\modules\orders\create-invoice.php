<?php
session_start();
require '../../config/function.php';
require '../../config/dbcon.php';

// Check if admin is logged in
if (!isset($_SESSION['loggedIn'])) {
    redirect('/webapp/admin/login.php', 'Login to continue...');
}

// Check for customer_id
if (!isset($_GET['customer_id'])) {
    redirect('orders.php');
}

$customerId = validate($_GET['customer_id']);
$query = "SELECT * FROM customers WHERE id = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, "i", $customerId);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (!$customer = mysqli_fetch_assoc($result)) {
    redirect('orders.php');
}

// Fetch only active products with stock
$products_query = "SELECT * FROM products WHERE status = '0' AND quantity > 0 ORDER BY name ASC";
$products = mysqli_query($conn, $products_query);

// Fetch only active services
$services_query = "SELECT * FROM services WHERE status = '0' ORDER BY name ASC";
$services = mysqli_query($conn, $services_query);

// Get VAT settings
function getVatSettings() {
    global $conn;
    $stmt = mysqli_prepare($conn, "SELECT vat_percentage FROM settings LIMIT 1");
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($row = mysqli_fetch_assoc($result)) {
        return (int)$row['vat_percentage'];
    }
    return 0;
}

$vat_percentage = getVatSettings();

include('../../includes/header.php');
?>

<div class="container-fluid px-4">
    <div class="card mt-4">
        <div class="card-header">
            <h4 class="mb-0">Create Invoice
                <a href="orders.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <form id="invoiceForm">
                <input type="hidden" name="customer_id" value="<?= $customerId ?>">

                <!-- Customer Information Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>Customer Information</h5>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label>Name</label>
                            <input type="text" name="customer_name" class="form-control" required
                                   value="<?= isset($customer) ? $customer['name'] : '' ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label>Email</label>
                            <input type="email" name="email" class="form-control"
                                   value="<?= isset($customer) ? $customer['email'] : '' ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label>Phone</label>
                            <input type="text" name="phone" class="form-control" required
                                   value="<?= isset($customer) ? $customer['phone'] : '' ?>">
                        </div>
                    </div>
                </div>

                <!-- Payment Information Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>Payment Information</h5>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label>Payment Method</label>
                            <select name="payment_mode" class="form-select" required>
                                <option value="">Select Payment Method</option>
                                <option value="cash">Cash</option>
                                <option value="online">Online Transfer</option>
                                <option value="card">Card Payment</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label>Payment Status</label>
                            <select name="payment_status" class="form-select" required id="paymentStatus">
                                <option value="Not Paid">Not Paid</option>
                                <option value="Paid">Paid</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3 payment-date-field" style="display: none;">
                            <label>Payment Date</label>
                            <input type="datetime-local" name="payment_date" class="form-control"
                                   value="<?= date('Y-m-d\TH:i') ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label>Delivery Fee (R)</label>
                            <div class="input-group">
                                <input type="number" id="deliveryFee" class="form-control"
                                       value="0" min="0" step="0.01">
                                <button type="button" id="addDeliveryFee" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Add
                                </button>
                            </div>
                            <small class="text-muted">Added as a separate item with 0% VAT</small>
                        </div>
                    </div>
                </div>

                <!-- Products Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>Add Products</h5>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label>Select Product</label>
                            <select class="form-select" id="productSelect">
                                <option value="">Select a Product</option>
                                <?php
                                if(mysqli_num_rows($products) > 0) {
                                    foreach($products as $item) {
                                        ?>
                                        <option value="<?= $item['id'] ?>"
                                                data-price="<?= $item['price'] ?>"
                                                data-vat="<?= $vat_percentage ?>"
                                                data-name="<?= $item['name'] ?>">
                                            <?= $item['name'] ?> (Stock: <?= $item['quantity'] ?>) - R<?= number_format($item['price'], 2) ?>
                                        </option>
                                        <?php
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label>Quantity</label>
                            <input type="number" id="productQuantity" class="form-control" min="1" value="1">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label>&nbsp;</label>
                            <button type="button" id="addProduct" class="btn btn-primary form-control">Add Product</button>
                        </div>
                    </div>
                </div>

                <!-- Services Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>Add Services</h5>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label>Select Service</label>
                            <select class="form-select" id="serviceSelect">
                                <option value="">Select a Service</option>
                                <?php
                                if(mysqli_num_rows($services) > 0) {
                                    foreach($services as $item) {
                                        ?>
                                        <option value="<?= $item['id'] ?>"
                                                data-price="<?= $item['price'] ?>"
                                                data-vat="<?= $vat_percentage ?>"
                                                data-name="<?= $item['name'] ?>">
                                            <?= $item['name'] ?> - R<?= number_format($item['price'], 2) ?>
                                        </option>
                                        <?php
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label>Quantity</label>
                            <input type="number" id="serviceQuantity" class="form-control" min="1" value="1">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label>&nbsp;</label>
                            <button type="button" id="addService" class="btn btn-primary form-control">Add Service</button>
                        </div>
                    </div>
                </div>

                <!-- Selected Items Table -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Selected Items</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Type</th>
                                                <th>Name</th>
                                                <th>Quantity</th>
                                                <th>Price</th>
                                                <th>VAT %</th>
                                                <th>VAT Amount</th>
                                                <th>Total</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="selectedItems">
                                            <!-- Items will be added here dynamically -->
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="5" class="text-end"><strong>Subtotal:</strong></td>
                                                <td id="subtotalVat">R0.00</td>
                                                <td id="subtotalAmount">R0.00</td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td colspan="5" class="text-end"><strong>Total:</strong></td>
                                                <td colspan="3" id="totalAmount">R0.00</td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary float-end">Generate Invoice</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const productSelect = document.getElementById('productSelect');
    const serviceSelect = document.getElementById('serviceSelect');
    const productQuantity = document.getElementById('productQuantity');
    const serviceQuantity = document.getElementById('serviceQuantity');
    const addProductBtn = document.getElementById('addProduct');
    const addServiceBtn = document.getElementById('addService');
    const addDeliveryFeeBtn = document.getElementById('addDeliveryFee');
    const deliveryFeeInput = document.getElementById('deliveryFee');
    const selectedItems = document.getElementById('selectedItems');
    const paymentStatus = document.getElementById('paymentStatus');
    const paymentDateField = document.querySelector('.payment-date-field');

    // Store selected items
    let items = [];

    // Show/hide payment date field based on payment status
    paymentStatus.addEventListener('change', function() {
        if (this.value === 'Paid') {
            paymentDateField.style.display = 'block';
        } else {
            paymentDateField.style.display = 'none';
        }
    });

    // Remove item from the table
    window.removeItem = function(button, index) {
        // Remove from DOM
        button.closest('tr').remove();

        // Remove from items array
        items.splice(index, 1);

        // Update totals
        updateTotals();

        // Update indices for remaining items
        const removeButtons = selectedItems.querySelectorAll('button');
        removeButtons.forEach((btn, i) => {
            btn.setAttribute('onclick', `removeItem(this, ${i})`);
        });
    };

    // Add product to the table
    addProductBtn.addEventListener('click', function() {
        if (productSelect.value === '') {
            alert('Please select a product');
            return;
        }

        const selectedOption = productSelect.options[productSelect.selectedIndex];
        const quantity = parseInt(productQuantity.value);

        if (quantity <= 0) {
            alert('Please enter a valid quantity');
            return;
        }

        // Create item object
        const item = {
            id: parseInt(productSelect.value),
            type: 'product',
            name: selectedOption.dataset.name,
            quantity: quantity,
            price: parseFloat(selectedOption.dataset.price),
            vat: parseFloat(selectedOption.dataset.vat)
        };

        addItemToTable(item);

        // Reset inputs
        productSelect.selectedIndex = 0;
        productQuantity.value = 1;
    });

    // Add service to the table
    addServiceBtn.addEventListener('click', function() {
        if (serviceSelect.value === '') {
            alert('Please select a service');
            return;
        }

        const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
        const quantity = parseInt(serviceQuantity.value);

        if (quantity <= 0) {
            alert('Please enter a valid quantity');
            return;
        }

        // Create item object
        const item = {
            id: parseInt(serviceSelect.value),
            type: 'service',
            name: selectedOption.dataset.name,
            quantity: quantity,
            price: parseFloat(selectedOption.dataset.price),
            vat: parseFloat(selectedOption.dataset.vat)
        };

        addItemToTable(item);

        // Reset inputs
        serviceSelect.selectedIndex = 0;
        serviceQuantity.value = 1;
    });

    // Add delivery fee as an item
    addDeliveryFeeBtn.addEventListener('click', function() {
        const deliveryFeeValue = parseFloat(deliveryFeeInput.value);

        if (deliveryFeeValue <= 0) {
            alert('Please enter a delivery fee greater than 0');
            return;
        }

        // Create a delivery fee item
        const item = {
            id: 0,
            type: 'fee',
            name: 'Delivery Fee',
            quantity: 1,
            price: deliveryFeeValue,
            vat: 0 // 0% VAT for delivery fee
        };

        addItemToTable(item);

        // Reset delivery fee input
        deliveryFeeInput.value = 0;
    });

    // Add item to table
    function addItemToTable(item) {
        // Calculate amounts
        const vatAmount = (item.price * item.quantity) * (item.vat / 100);
        const total = (item.price * item.quantity) + vatAmount;

        // Add to items array
        items.push(item);

        // Add row to table
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.type.charAt(0).toUpperCase() + item.type.slice(1)}</td>
            <td>${item.name}</td>
            <td>${item.quantity}</td>
            <td>R${item.price.toFixed(2)}</td>
            <td>${item.vat}%</td>
            <td>R${vatAmount.toFixed(2)}</td>
            <td>R${total.toFixed(2)}</td>
            <td>
                <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this, ${items.length - 1})">
                    Remove
                </button>
            </td>
        `;
        selectedItems.appendChild(row);

        updateTotals();
    }

    function updateTotals() {
        let totalVat = 0;
        let totalAmount = 0;

        items.forEach(item => {
            const vatAmount = (item.price * item.quantity) * (item.vat / 100);
            const total = (item.price * item.quantity) + vatAmount;
            totalVat += vatAmount;
            totalAmount += total;
        });

        document.getElementById('subtotalVat').textContent = `R${totalVat.toFixed(2)}`;
        document.getElementById('subtotalAmount').textContent = `R${totalAmount.toFixed(2)}`;
        document.getElementById('totalAmount').textContent = `R${totalAmount.toFixed(2)}`;
    }

    // Form submission
    document.getElementById('invoiceForm').addEventListener('submit', function(e) {
        e.preventDefault();

        if (items.length === 0) {
            alert('Please add at least one item to the invoice');
            return;
        }

        // Generate tracking number
        const tracking_no = 'TRK' + Math.floor(Math.random() * 900000 + 100000);

        // Get form data
        const formData = new FormData(this);

        // Add items to form data with tracking number
        const itemsWithTracking = items.map(item => ({
            ...item,
            tracking_no: tracking_no
        }));

        formData.append('items', JSON.stringify(itemsWithTracking));
        formData.append('tracking_no', tracking_no);

        // Send data to server
        fetch('save-order-fixed.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Redirect directly to the invoice page
                window.location.href = `order_invoice.php?id=${data.order_id}&tracking_no=${data.tracking_no}`;
            } else {
                // Show error message in a more elegant way
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger';
                errorDiv.textContent = 'Error: ' + data.message;
                document.querySelector('.card-body').prepend(errorDiv);

                // Scroll to the top to show the error
                window.scrollTo(0, 0);
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Show error message in a more elegant way
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger';
            errorDiv.textContent = 'Error creating invoice. Please try again.';
            document.querySelector('.card-body').prepend(errorDiv);

            // Scroll to the top to show the error
            window.scrollTo(0, 0);
        });
    });
});
</script>

<?php include('../../includes/footer.php'); ?>