<?php
// Include database connection if not already included
if (!function_exists('mysqli_connect') && file_exists('../../config/dbcon.php')) {
    require_once '../../config/dbcon.php';
}

/**
 * Check if a customer exists by email or phone
 * 
 * @param string $email Customer email
 * @param string $phone Customer phone
 * @return array|false Customer data if exists, false otherwise
 */
function checkCustomerExists($email, $phone) {
    global $conn;
    
    // Prepare query to check if customer exists
    $query = "SELECT * FROM customers WHERE email = ? OR phone = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ss', $email, $phone);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }
    
    return false;
}

/**
 * Save a new customer from quotation data
 * 
 * @param string $name Customer name
 * @param string $email Customer email
 * @param string $phone Customer phone
 * @return array|false New customer data if successful, false otherwise
 */
function saveCustomerFromQuotation($name, $email, $phone) {
    global $conn;
    
    // Check if customer already exists
    $existing_customer = checkCustomerExists($email, $phone);
    if ($existing_customer) {
        return $existing_customer;
    }
    
    // Generate random client code (CT######)
    $client_code = 'CT' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
    
    // Insert new customer
    $query = "INSERT INTO customers (name, email, phone, status, client_code) VALUES (?, ?, ?, 0, ?)";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ssss', $name, $email, $phone, $client_code);
    
    if (mysqli_stmt_execute($stmt)) {
        $customer_id = mysqli_insert_id($conn);
        
        // Get the newly created customer
        $get_query = "SELECT * FROM customers WHERE id = ?";
        $get_stmt = mysqli_prepare($conn, $get_query);
        mysqli_stmt_bind_param($get_stmt, 'i', $customer_id);
        mysqli_stmt_execute($get_stmt);
        $result = mysqli_stmt_get_result($get_stmt);
        
        if (mysqli_num_rows($result) > 0) {
            return mysqli_fetch_assoc($result);
        }
    }
    
    return false;
}

/**
 * Update quotation with customer ID
 * 
 * @param int $quotation_id Quotation ID
 * @param int $customer_id Customer ID
 * @return bool Success status
 */
function linkQuotationToCustomer($quotation_id, $customer_id) {
    global $conn;
    
    // Check if the customer_id column exists in the quotations table
    $check_column_query = "SHOW COLUMNS FROM quotations LIKE 'customer_id'";
    $check_column_result = mysqli_query($conn, $check_column_query);
    
    // If the column doesn't exist, add it
    if (mysqli_num_rows($check_column_result) == 0) {
        $add_column_query = "ALTER TABLE quotations ADD COLUMN customer_id INT NULL AFTER id";
        mysqli_query($conn, $add_column_query);
    }
    
    // Update the quotation with the customer ID
    $query = "UPDATE quotations SET customer_id = ? WHERE id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ii', $customer_id, $quotation_id);
    
    return mysqli_stmt_execute($stmt);
}
?>
