<?php 
include('includes/header.php');
checkUserAccess();

$callLogId = checkParamId('id');
if(!is_numeric($callLogId)){
    redirect('call-log.php', 'Invalid Call Log ID');
    exit;
}

// Use prepared statement to get call log data
$stmt = mysqli_prepare($conn, "SELECT * FROM call_log WHERE id = ?");
mysqli_stmt_bind_param($stmt, "i", $callLogId);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$callLog = mysqli_fetch_assoc($result);

if(!$callLog) {
    redirect('call-log.php', 'Call Log not found');
    exit;
}
?>

<div class="container-fluid px-4">
    <div class="card mt-4 shadow-sm">
        <div class="card-header">
            <h4 class="mb-0">View Call Log
                <a href="call-log.php" class="btn btn-danger float-end">Back</a>
            </h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="fw-bold">Company Name:</label>
                    <p><?= htmlspecialchars($callLog['company_name']) ?></p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="fw-bold">Number Called:</label>
                    <p><?= htmlspecialchars($callLog['number_called']) ?></p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="fw-bold">Date:</label>
                    <p><?= htmlspecialchars($callLog['date']) ?></p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="fw-bold">Time:</label>
                    <p><?= htmlspecialchars($callLog['time']) ?></p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="fw-bold">Call Log Note:</label>
                    <p><?= nl2br(htmlspecialchars($callLog['call_log_note'])) ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include('includes/footer.php'); ?>
